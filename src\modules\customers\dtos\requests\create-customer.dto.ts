import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, IsUUID } from "class-validator";

export class CreateCustomerDto {
  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsUUID()
  companyId: string;

  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty()
  customerName: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @IsOptional()
  @IsString()
  notes: string;
}
