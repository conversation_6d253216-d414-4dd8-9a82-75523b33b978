import { MigrationInterface, QueryRunner, Table } from 'typeorm';
import {
  columnCreatedAt,
  columnDeletedAt,
  columnId,
  columnUpdatedAt,
} from '../constants/columns.constant';

export class CreateNozzleTable1702441939822 implements MigrationInterface {
  private tableName = 'nozzle';
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: this.tableName,
        columns: [
          columnId,
          {
            name: 'dailyReportId',
            type: 'uuid',
            isNullable: false,
          },
          {
            name: 'identificationNumber',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'orificeSize',
            type: 'float8',
            isNullable: true,
          },
          columnCreatedAt,
          columnUpdatedAt,
          columnDeletedAt,
        ],
      }),
      false,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable(this.tableName);
  }
}
