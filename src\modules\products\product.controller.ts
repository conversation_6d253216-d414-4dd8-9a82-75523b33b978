import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { AuthGuard } from '../auth/guards/auth.guard';

import { toDto, toPaginateDtos } from '../../common/transformers/dto.transformer';

import { RolesDecorator } from '../auth/decorators/roles.decorator';
import { UserRole } from '../roles/enums/roles.enum';
import { RolesGuard } from '../auth/guards/roles.guard';
import { ProductService } from './product.service';
import { CreateProductDto } from './dtos/requests/create-product.dto';
import { ProductResponseDto } from './dtos/responses/product.response.dto';
import { CurrentUser } from '../../common/decorators/current-user.decorator';
import { User } from '../users/entities/user.entity';
import { UpdateProductDto } from './dtos/requests/update-product.dto';
import { Responder } from '../../common/decorators/responder.decorator';
import { PaginationDto } from '../../common/dtos/paginationDto';
import { PaginationResponseDto } from '../../common/dtos/pagination.response.dto';
import { ProductDetailResponseDto } from './dtos/responses/product-detail.response.dto';
import { GetProductsQueryDto } from './dtos/requests/get-products.query.dto';

@ApiTags('Product')
@Controller('products')
@ApiBearerAuth('access-token')
@UseGuards(AuthGuard, RolesGuard)
export class ProductController {
  constructor(private readonly productService: ProductService) {}

  @Get()
  @ApiOperation({ description: 'Get products' })
  @Responder.handle('Get products')
  @HttpCode(HttpStatus.OK)
  @RolesDecorator(UserRole.ADMIN, UserRole.SUPERVISOR, UserRole.ENGINEER, UserRole.COMPANY_ADMIN)
  async findAll(
    @CurrentUser() user: User,
    @Query() query: GetProductsQueryDto,
    @Query() paginationQuery: PaginationDto,
  ): Promise<PaginationResponseDto<ProductResponseDto>> {
    const data = await this.productService.findAll(user, query, paginationQuery);
    return toPaginateDtos(ProductResponseDto, data);
  }

  @Post()
  @ApiOperation({ description: 'Create product' })
  @Responder.handle('Create product')
  @HttpCode(HttpStatus.CREATED)
  @RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN, UserRole.SUPERVISOR, UserRole.ENGINEER)
  async create(
    @CurrentUser() user: User,
    @Body() data: CreateProductDto,
  ): Promise<ProductResponseDto> {
    const value = await this.productService.create(user, data);
    return toDto(ProductResponseDto, value);
  }

  @Put('/:id')
  @ApiOperation({ description: 'Update product' })
  @Responder.handle('Update product')
  @HttpCode(HttpStatus.CREATED)
  @RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN, UserRole.SUPERVISOR, UserRole.ENGINEER)
  async update(@Param('id') id: string, @Body() data: UpdateProductDto): Promise<boolean> {
    return this.productService.update(id, data);
  }

  @Get('/:id')
  @ApiOperation({ description: 'Get product detail' })
  @Responder.handle('Get product detail')
  @HttpCode(HttpStatus.OK)
  @RolesDecorator(UserRole.ADMIN, UserRole.SUPERVISOR, UserRole.ENGINEER, UserRole.COMPANY_ADMIN)
  async detail(@Param('id') id: string): Promise<ProductDetailResponseDto> {
    const data = await this.productService.findOne(id);
    return toDto(ProductDetailResponseDto, data);
  }

  @Delete('/:id')
  @ApiOperation({ description: 'Delete product' })
  @Responder.handle('Delete product')
  @HttpCode(HttpStatus.OK)
  @RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN, UserRole.SUPERVISOR, UserRole.ENGINEER)
  delete(@Param('id') id: string): Promise<boolean> {
    return this.productService.softDeleteById(id);
  }
}
