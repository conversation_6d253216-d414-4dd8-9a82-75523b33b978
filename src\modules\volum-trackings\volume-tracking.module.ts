import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IsExist } from '../../utils/validators/is-exists.validator';
import { IsNotExist } from '../../utils/validators/is-not-exists.validator';
import { VolumeTrackingService } from './volume-tracking.service';
import { VolumeTracking } from './entities/volume-tracking.entity';
import { JwtModule } from '@nestjs/jwt';
import { VolumeTrackingController } from './volume-tracking.controller';
import { VolumeTrackingRepository } from './volume-tracking.repository';
import { VolumeTrackingItem } from '../volume-tracking-items/entities/volume-tracking-item.entity';
import { DailyReportRepository } from '../daily-reports/daily-report.repository';

@Module({
  imports: [TypeOrmModule.forFeature([VolumeTracking, VolumeTrackingItem]), JwtModule.register({})],
  controllers: [VolumeTrackingController],
  providers: [
    IsExist,
    IsNotExist,
    VolumeTrackingService,
    VolumeTrackingRepository,
    DailyReportRepository,
  ],
  exports: [VolumeTrackingService],
})
export class VolumeTrackingModule {}
