import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IsExist } from '../../utils/validators/is-exists.validator';
import { IsNotExist } from '../../utils/validators/is-not-exists.validator';
import { GuideService } from './guide.service';
import { Guide } from './entities/guide.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Guide])],
  providers: [IsExist, IsNotExist, GuideService],
  exports: [GuideService],
})
export class GuideModule {}
