import { Injectable } from '@nestjs/common';

import { SelectQueryBuilder } from 'typeorm';

import { PaginationResponseDto } from '../../common/dtos/pagination.response.dto';

import { PaginationDto } from '../../common/dtos/paginationDto';
import { CostRepository } from './cost.repository';
import { GetCostsQueryDto } from './dtos/requests/get-costs.dto';
import { Cost } from './entities/cost.entity';
import { CreateCostDto } from './dtos/requests/create-cost.dto';
import { UpdateCostDto } from './dtos/requests/update-cost.dto';
import { CostSetting } from '../cost-settings/entities/cost-setting.entity';
import { DailyReportRepository } from '../daily-reports/daily-report.repository';
import { User } from '../users/entities/user.entity';
import { CostSummary } from './entities/cost-summary.entity';
import { GetCostSummaryDto } from './dtos/requests/get-cost-summary.dto';
import { DailyReport } from '../daily-reports/entities/daily-report.entity';

@Injectable()
export class CostService {
  constructor(
    private repository: CostRepository,
    private reportRepository: DailyReportRepository,
  ) {}

  async findAll(
    query: GetCostsQueryDto,
    paginationQuery: PaginationDto,
  ): Promise<PaginationResponseDto<Cost>> {
    const queryBuilder: SelectQueryBuilder<Cost> = this.repository
      .createQueryBuilder('cost')
      .leftJoinAndSelect('cost.costSetting', 'cost_setting')
      .where('cost.dailyReportId = :dailyReportId', { dailyReportId: query.dailyReportId })
      .orderBy('cost.createdAt', 'DESC');
    return this.repository.paginate(queryBuilder, paginationQuery);
  }

  async create(user: User, data: CreateCostDto): Promise<Cost> {
    const cost = this.repository.create(data);
    cost.costSetting = { id: data.costSettingId } as CostSetting;
    cost.dailyReport = { id: data.dailyReportId } as DailyReport;
    await this.reportRepository.updateReportUpdatedBy(user, data.dailyReportId);
    return this.repository.save(cost);
  }

  async update(user: User, id: string, data: UpdateCostDto): Promise<boolean> {
    await this.reportRepository.updateReportUpdatedBy(user, data.dailyReportId!);
    const dataUpdate = {
      id,
      dailyReport: { id: data.dailyReportId } as DailyReport,
      costSetting: { id: data.costSettingId },
      ...data,
    };
    delete dataUpdate.dailyReportId;
    await this.repository.save(dataUpdate);
    return true;
  }

  async findOne(id: string): Promise<Cost | null> {
    return this.repository.findOne({ where: { id }, relations: ['costSetting'] });
  }

  async findCostSummary(query: GetCostSummaryDto): Promise<CostSummary | null> {
    const dailyCosts = await this.repository
      .createQueryBuilder('cost')
      .where('cost.dailyReportId = :dailyReportId', { dailyReportId: query.dailyReportId })
      .leftJoinAndSelect('cost.costSetting', 'costSetting')
      .getMany();
    let totalDailyCosts = 0;
    if (dailyCosts.length) {
      totalDailyCosts = dailyCosts.map(e => e.costSetting.cost).reduce((a, b) => a + b);
    }
    const wellDailyCosts = await this.repository
      .createQueryBuilder('cost')
      .leftJoin('cost.dailyReport', 'dailyReport')
      .where('dailyReport.wellId = :wellId', { wellId: query.wellId })
      .leftJoinAndSelect('cost.costSetting', 'costSetting')
      .getMany();
    let totalWellDailyCosts = 0;
    if (dailyCosts.length) {
      totalWellDailyCosts = wellDailyCosts.map(e => e.costSetting.cost).reduce((a, b) => a + b);
    }
    return {
      dailyCost: totalDailyCosts,
      cumulativeCost: totalWellDailyCosts,
    } as CostSummary;
  }

  async softDeleteById(user: User, id: string): Promise<boolean> {
    const item = await this.repository
      .createQueryBuilder('cost')
      .where('cost.id = :id', { id })
      .leftJoinAndSelect('cost.dailyReport', 'dailyReport')
      .getOne();
    if (item?.dailyReport.id) {
      await this.reportRepository.updateReportUpdatedBy(user, item!.dailyReport.id!);
    }
    await this.repository.softDelete({ id });
    return true;
  }
}
