import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsUUID } from "class-validator";
import { IsUuidArray } from '../../../../common/decorators/class-validator/is-array-uuid.decorator';

export class AssignEngineersDto {
  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsUuidArray()
  engineerIds: string[];

  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsUUID()
  supervisorId: string;
}
