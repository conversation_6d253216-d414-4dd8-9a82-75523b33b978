import { Injectable } from '@nestjs/common';
import { SelectQueryBuilder } from 'typeorm';
import { PaginationDto } from '../../common/dtos/paginationDto';
import { PaginationResponseDto } from '../../common/dtos/pagination.response.dto';

import { TaskRepository } from './task.repository';
import { Task } from './entities/task.entity';
import { CreateTaskDto } from './dtos/requests/create-task.dto';
import { UpdateTaskDto } from './dtos/requests/update-task.dto';
import { GetTaskDto } from './dtos/requests/get-tasks.dto';
import { DailyReportRepository } from '../daily-reports/daily-report.repository';
import { User } from '../users/entities/user.entity';

@Injectable()
export class TaskService {
  constructor(
    private taskRepository: TaskRepository,
    private reportRepository: DailyReportRepository,
  ) {}

  async findAll(
    query: GetTaskDto,
    paginationQuery: PaginationDto,
  ): Promise<PaginationResponseDto<Task>> {
    const queryBuilder: SelectQueryBuilder<Task> = this.taskRepository
      .createQueryBuilder('task')
      .where('task.dailyReportId = :dailyReportId', { dailyReportId: query.dailyReportId })
      .orderBy('task.createdAt', 'DESC')
      .addOrderBy('task.description', 'ASC');
    return this.taskRepository.paginate(queryBuilder, paginationQuery);
  }

  async create(user: User, data: CreateTaskDto): Promise<Task> {
    await this.reportRepository.updateReportUpdatedBy(user, data.dailyReportId);
    const volume = this.taskRepository.create(data);
    return this.taskRepository.save(volume);
  }

  async update(user: User, id: string, data: UpdateTaskDto): Promise<boolean> {
    await this.reportRepository.updateReportUpdatedBy(user, data.dailyReportId);
    await this.taskRepository.update({ id }, data);
    return true;
  }

  async findOne(id: string): Promise<Task | null> {
    return this.taskRepository.createQueryBuilder('task').where('task.id = :id', { id }).getOne();
  }

  async softDeleteById(user: User, id: string): Promise<boolean> {
    const item = await this.taskRepository
      .createQueryBuilder('task')
      .where('task.id = :id', { id })
      .getOne();
    if (item?.dailyReportId) {
      await this.reportRepository.updateReportUpdatedBy(user, item!.dailyReportId);
    }
    await this.taskRepository.softDelete({ id });
    return true;
  }
}
