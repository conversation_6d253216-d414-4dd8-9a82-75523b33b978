import { CountryRepository } from '../../country.repository';
import { GetCountriesQueryDto } from '../../dtos/requests/get-countries.dto';

export const buildGetAllCountries = (repo: CountryRepository, query: GetCountriesQueryDto) => {
  const { keyword } = query;

  let builder = repo.createQueryBuilder('country');
  keyword && (builder = builder.where('country.name ILIKE :name', { name: `%${keyword}%` }));

  return builder;
};
