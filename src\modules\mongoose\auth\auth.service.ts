import { Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import bcrypt from 'bcryptjs';
import ms from 'ms';
import { AuthLoginRequestDto } from '../../auth/dto/requests/auth-login.request.dto';
import { AuthLoginResponseDto } from '../../auth/dto/responses/auth-login.response.dto';
import { HttpNotFoundError } from '../../../errors/not-found.error';
import { HttpBadRequestError } from '../../../errors/bad-request.error';
import { ErrorCode } from '../../../errors/error-code';
import { AllConfigType } from '../../../config/config.type';
import { UserService } from '../users/user.service';

@Injectable()
export class MongoAuthService {
  constructor(
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService<AllConfigType>,
    private readonly userService: UserService,
  ) {}

  async login(body: AuthLoginRequestDto): Promise<AuthLoginResponseDto> {
    const { email, password } = body;
    const user = await this.userService.findByEmail(email);
    if (!user) {
      throw new HttpNotFoundError(ErrorCode.USER_NOT_FOUND);
    }

    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      throw new HttpBadRequestError(ErrorCode.INCORRECT_EMAIL_PASSWORD);
    }

    const tokenData = await this.generateToken({
      id: user.id,
      roles: user.roles,
      email: user.email,
      companyId: user.company[0],
    });

    // For MongoDB, we'll return a simplified profile structure
    // Note: This is a temporary solution until proper role mapping is implemented
    const profile = {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      companyId: user.company[0],
      officePhone: user.officePhone,
      mobilePhone: user.mobilePhone,
      address: user.address,
      note: user.note,
      roles: [], // Simplified - roles will be handled in JWT token
      status: user.status,
    };

    return { ...tokenData, profile } as AuthLoginResponseDto;
  }

  async logout(): Promise<void> {
    // TODO: remove refresh token session
  }

  private async generateToken(data: {
    id: string;
    roles?: string[];
    email: string;
    companyId: string | null;
  }) {
    const tokenExpiresIn = this.configService.getOrThrow('auth.expires', { infer: true });
    const secret = this.configService.getOrThrow('auth.secret', { infer: true });
    const tokenExpires = Date.now() + ms(tokenExpiresIn);

    const token = await this.jwtService.signAsync(
      {
        id: data.id,
        roles: data.roles,
        email: data.email,
        companyId: data.companyId,
      },
      {
        secret,
        expiresIn: tokenExpiresIn,
      },
    );

    return {
      token,
      tokenExpires,
    };
  }
}
