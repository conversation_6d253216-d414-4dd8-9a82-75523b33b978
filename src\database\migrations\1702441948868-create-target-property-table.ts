import { MigrationInterface, QueryRunner, Table } from 'typeorm';
import {
  columnCreatedAt,
  columnDeletedAt,
  columnId,
  columnUpdatedAt,
} from '../constants/columns.constant';

export class CreateTargetPropertyTable1702441948868 implements MigrationInterface {
  private tableName = 'target_property';
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: this.tableName,
        columns: [
          columnId,
          {
            name: 'wellId',
            type: 'uuid',
            isNullable: false,
          },
          {
            name: 'fluidType',
            type: 'int',
            isNullable: false,
          },
          {
            name: 'mudWeight',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'funnelViscosity',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'plasticViscosity',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'yieldPoint',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'apiFiltrate',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'apiCakeThickness',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'pH',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'mudAlkalinity',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'filtrateAlkalinity',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'chlorides',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'totalHardness',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'linearGelStrengthPercent',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'rpm',
            type: 'float8',
            isNullable: false,
          },
          columnCreatedAt,
          columnUpdatedAt,
          columnDeletedAt,
        ],
      }),
      false,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable(this.tableName);
  }
}
