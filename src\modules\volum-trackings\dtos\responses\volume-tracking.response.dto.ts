import { Exclude, Expose } from 'class-transformer';

@Exclude()
export class VolumeTrackingResponseDto {
  @Expose()
  id: string;

  @Expose()
  name: string;

  @Expose()
  storageType: string;

  @Expose()
  status: number;

  @Expose()
  measuredVolume: number;

  @Expose()
  mudWeight: number;

  @Expose()
  mudType: number;

  @Expose()
  totalAdditions: number;

  @Expose()
  totalLosses: number;

  @Expose()
  totalTransfers: number;

  @Expose()
  calculatedVolume: number;
}
