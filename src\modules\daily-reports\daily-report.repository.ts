import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { BaseRepository } from '../../common/bases/base.repository';
import { DailyReport } from './entities/daily-report.entity';
import { User } from '../users/entities/user.entity';
import moment from 'moment';
import { Well } from '../wells/entities/well.entity';

@Injectable()
export class DailyReportRepository extends BaseRepository<DailyReport> {
  constructor(dataSource: DataSource) {
    super(DailyReport, dataSource);
  }

  async findTodayReport(user: User, wellId: string): Promise<DailyReport> {
    const date = moment().format('yyyy-MM-DD').toString();
    const report = await this.createQueryBuilder('report')
      .where('report.wellId = :wellId', { wellId: wellId })
      .andWhere('report.companyId = :companyId', { companyId: user.companyId })
      .andWhere('report.reportDate = :date', { date })
      .getOne();
    if (report) {
      return report;
    }
    return this.save(
      this.create({
        well: { id: wellId } as Well,
        // reportDate: date,
        companyId: user.companyId,
        createdBy: user,
        updatedBy: user,
      }),
    );
  }

  async updateReportUpdatedBy(user: User, id: string): Promise<void> {
    await this.update({ id }, { updatedBy: user });
  }
}
