import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { AuthGuard } from '../auth/guards/auth.guard';

import { toDto, toPaginateDtos } from '../../common/transformers/dto.transformer';

import { RolesDecorator } from '../auth/decorators/roles.decorator';
import { UserRole } from '../roles/enums/roles.enum';
import { RolesGuard } from '../auth/guards/roles.guard';
import { CostSettingService } from './cost-setting.service';
import { CreateCostSettingDto } from './dtos/requests/create-cost-setting.dto';
import { CostSettingResponseDto } from './dtos/responses/cost-setting.response.dto';
import { GetCostSettingsQueryDto } from './dtos/requests/get-cost-settings.dto';
import { UpdateCostSettingDto } from './dtos/requests/update-cost-setting.dto';
import { CurrentUser } from '../../common/decorators/current-user.decorator';
import { User } from '../users/entities/user.entity';
import { Responder } from '../../common/decorators/responder.decorator';
import { PaginationDto } from '../../common/dtos/paginationDto';
import { PaginationResponseDto } from '../../common/dtos/pagination.response.dto';
import { CostSettingDetailResponseDto } from './dtos/responses/cost-setting-detail.response.dto';

@ApiTags('CostSetting')
@Controller('costSettings')
@ApiBearerAuth('access-token')
@UseGuards(AuthGuard, RolesGuard)
export class CostSettingController {
  constructor(private readonly costSettingService: CostSettingService) {}

  @Get()
  @ApiOperation({ description: 'Get Cost Settings' })
  @Responder.handle('Get Cost Settings')
  @HttpCode(HttpStatus.OK)
  @RolesDecorator(UserRole.ADMIN, UserRole.SUPERVISOR, UserRole.ENGINEER, UserRole.COMPANY_ADMIN)
  async findAll(
    @CurrentUser() user: User,
    @Query() query: GetCostSettingsQueryDto,
    @Query() paginationQuery: PaginationDto,
  ): Promise<PaginationResponseDto<CostSettingResponseDto>> {
    const data = await this.costSettingService.findAll(user, query, paginationQuery);
    return toPaginateDtos(CostSettingResponseDto, data);
  }

  @Post()
  @ApiOperation({ description: 'Create Cost Setting' })
  @Responder.handle('Create Cost Setting')
  @HttpCode(HttpStatus.CREATED)
  @RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN, UserRole.SUPERVISOR, UserRole.ENGINEER)
  async create(
    @CurrentUser() user: User,
    @Body() data: CreateCostSettingDto,
  ): Promise<CostSettingResponseDto> {
    const value = await this.costSettingService.create(user, data);
    return toDto(CostSettingResponseDto, value);
  }

  @Put('/:id')
  @ApiOperation({ description: 'Update Cost Setting' })
  @Responder.handle('Update Cost Setting')
  @HttpCode(HttpStatus.CREATED)
  @RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN, UserRole.SUPERVISOR, UserRole.ENGINEER)
  async update(@Param('id') id: string, @Body() data: UpdateCostSettingDto): Promise<boolean> {
    return this.costSettingService.update(id, data);
  }

  @Get('/:id')
  @ApiOperation({ description: 'Get cost setting detail' })
  @Responder.handle('Get cost setting detail')
  @HttpCode(HttpStatus.OK)
  @RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN, UserRole.SUPERVISOR, UserRole.ENGINEER, UserRole.COMPANY_ADMIN)
  async detail(@Param('id') id: string): Promise<CostSettingDetailResponseDto> {
    const data = await this.costSettingService.findOne(id);
    return toDto(CostSettingDetailResponseDto, data);
  }

  @Delete('/:id')
  @ApiOperation({ description: 'Delete Cost Setting' })
  @Responder.handle('Delete Cost Setting')
  @HttpCode(HttpStatus.OK)
  @RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN, UserRole.SUPERVISOR, UserRole.ENGINEER)
  delete(@Param('id') id: string): Promise<boolean> {
    return this.costSettingService.softDeleteById(id);
  }
}
