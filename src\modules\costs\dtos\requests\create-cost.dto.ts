import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsNotEmpty, IsNumber, IsUUID } from 'class-validator';

export class CreateCostDto {
  @ApiProperty({ required: true })
  @IsUUID()
  @IsNotEmpty()
  dailyReportId: string;

  @ApiProperty({ required: true })
  @IsUUID()
  @IsNotEmpty()
  costSettingId: string;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  unit: number;

  @ApiProperty({ required: true })
  @IsInt()
  @IsNotEmpty()
  quantity: number;
}
