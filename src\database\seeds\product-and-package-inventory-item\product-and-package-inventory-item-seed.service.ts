import { Injectable } from '@nestjs/common';
import { faker } from '@faker-js/faker';
import { ProductAndPackageTypeEnum } from '../../../modules/product-and-package-inventory-items/enums/product-and-package-type.enum';
import { Repository } from 'typeorm';
import { ProductAndPackageInventoryItem } from '../../../modules/product-and-package-inventory-items/entities/product-and-package-inventory-item.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { VolumeTracking } from '../../../modules/volum-trackings/entities/volume-tracking.entity';
import { ProductAndPackageInventoryReport } from '../../../modules/product-and-package-inventory-reports/entities/product-and-package-inventory-report.entity';

@Injectable()
export class ProductAndPackageInventoryItemSeedService {
  constructor(
    @InjectRepository(ProductAndPackageInventoryReport)
    private productAndPackageInventoryReportRepository: Repository<ProductAndPackageInventoryReport>,
    @InjectRepository(VolumeTracking)
    private locationService: Repository<VolumeTracking>,
    @InjectRepository(ProductAndPackageInventoryItem)
    private productAndPackageInventoryItemService: Repository<ProductAndPackageInventoryItem>,
  ) {}

  async run(): Promise<void> {
    await this.fakeProductAndPackageInventoryItems();
  }

  private async fakeProductAndPackageInventoryItems(): Promise<void> {
    const productAndPackageInventoryReports =
      await this.productAndPackageInventoryReportRepository.find({
        where: {},
        relations: ['productAndPackageInventory', 'productAndPackageInventory.dailyReport'],
      });
    for (let i = 0; i < productAndPackageInventoryReports.length; i++) {
      const dailyReportId =
        productAndPackageInventoryReports[i].productAndPackageInventory?.dailyReport?.id;
      const location = await this.locationService
        .createQueryBuilder('location')
        .where('location.dailyReportId = :dailyReportId', { dailyReportId })
        .getOne();
      await this.productAndPackageInventoryItemService.save(
        this.productAndPackageInventoryItemService.create({
          productAndPackageInventoryReport: { id: productAndPackageInventoryReports[i]?.id },
          location: { id: location?.id },
          cost: 100,
          bolNo: faker.string.alphanumeric(6),
          notes: faker.lorem.text(),
          type: ProductAndPackageTypeEnum.initial,
          quantity: 10,
        }),
      );
    }
  }
}
