import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, ManyToOne } from 'typeorm';
import { EntityHelper } from '../../../utils/entity-helper';
import { VolumeTracking } from '../../volum-trackings/entities/volume-tracking.entity';
import { ProductAndPackageInventoryReport } from '../../product-and-package-inventory-reports/entities/product-and-package-inventory-report.entity';

@Entity()
export class ProductAndPackageInventoryItem extends EntityHelper {
  @ManyToOne(
    () => ProductAndPackageInventoryReport,
    productAndPackageInventoryReport => productAndPackageInventoryReport.items,
    { nullable: true },
  )
  @JoinColumn({ name: 'productAndPackageInventoryReportId' })
  productAndPackageInventoryReport: ProductAndPackageInventoryReport;

  @ManyToOne(() => VolumeTracking, { nullable: true })
  @JoinColumn({ name: 'locationId' })
  location: VolumeTracking;

  @Column({ nullable: false })
  type: number;

  @Column({ type: 'int', nullable: false })
  quantity: number;

  @Column({ type: 'float', nullable: false })
  cost: number;

  @Column({ type: String, nullable: false })
  bolNo: string;

  @Column({ type: String, nullable: false })
  notes: string;
}
