import { faker } from '@faker-js/faker';
import { Injectable } from '@nestjs/common';
import { Repository } from 'typeorm';
import { DailyReport } from '../../../modules/daily-reports/entities/daily-report.entity';
import { DrillBit } from '../../../modules/drill-bits/entities/drill-bit.entity';
import { InjectRepository } from '@nestjs/typeorm';

@Injectable()
export class DrillBitSeedService {
  constructor(
    @InjectRepository(DailyReport)
    private dailyReportRepository: Repository<DailyReport>,
    @InjectRepository(DrillBit)
    private drillBitRepository: Repository<DrillBit>,
  ) {}

  async run(): Promise<void> {
    await this.fakeDrillBits();
  }

  private async fakeDrillBits(): Promise<void> {
    const reports = await this.dailyReportRepository.find({ where: {} });
    for (let i = 0; i < reports.length; i++) {
      for (let j = 0; j < 20; j++) {
        await this.drillBitRepository.save(
          this.drillBitRepository.create({
            dailyReportId: reports[i]?.id,
            bitNo: faker.string.alphanumeric(6),
            type: faker.string.alphanumeric(6),
            iadcType: faker.string.alphanumeric(6),
            bitSize: faker.number.float({ max: 200, precision: 0.01 }),
            depth: faker.number.float({ max: 200, precision: 0.01 }),
            bitRunDuration: faker.number.float({ max: 200, precision: 0.01 }),
          }),
        );
      }
    }
  }
}
