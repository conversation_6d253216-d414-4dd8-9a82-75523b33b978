import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IsExist } from '../../utils/validators/is-exists.validator';
import { IsNotExist } from '../../utils/validators/is-not-exists.validator';
import { SampleService } from './sample.service';
import { JwtModule } from '@nestjs/jwt';
import { SampleController } from './sample.controller';
import { SampleRepository } from './sample.repository';
import { Sample } from './entities/sample.entity';
import { DailyReportRepository } from '../daily-reports/daily-report.repository';

@Module({
  imports: [TypeOrmModule.forFeature([Sample]), JwtModule.register({})],
  controllers: [SampleController],
  providers: [IsExist, IsNotExist, SampleService, SampleRepository, DailyReportRepository],
  exports: [SampleService],
})
export class SampleModule {}
