import { Module } from '@nestjs/common';
import { DrillBitSeedService } from './drill-bit-seed.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DailyReport } from '../../../modules/daily-reports/entities/daily-report.entity';
import { DrillBit } from '../../../modules/drill-bits/entities/drill-bit.entity';

@Module({
  imports: [TypeOrmModule.forFeature([DailyReport, DrillBit])],
  providers: [DrillBitSeedService],
  exports: [DrillBitSeedService],
})
export class DrillBitSeedModule {}
