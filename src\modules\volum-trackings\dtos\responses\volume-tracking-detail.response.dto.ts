import { Exclude, Expose, Type } from 'class-transformer';
import { VolumeTrackingItemResponseDto } from '../../../volume-tracking-items/dtos/responses/volume-tracking-item.response.dto';

@Exclude()
export class VolumeTrackingDetailResponseDto {
  @Expose()
  id: string;

  @Expose()
  name: string;

  @Expose()
  storageType: string;

  @Expose()
  status: number;

  @Expose()
  measuredVolume: number;

  @Expose()
  mudWeight: number;

  @Expose()
  mudType: number;

  @Expose()
  totalAdditions: number;

  @Expose()
  totalLosses: number;

  @Expose()
  totalTransfers: number;

  @Expose()
  calculatedVolume: number;

  @Expose()
  @Type(() => VolumeTrackingItemResponseDto)
  volumeItems: VolumeTrackingItemResponseDto[];
}
