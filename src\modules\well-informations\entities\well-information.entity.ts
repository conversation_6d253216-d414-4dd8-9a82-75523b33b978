import { <PERSON>umn, CreateDateColumn, Entity, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToOne } from 'typeorm';
import { EntityHelper } from '../../../utils/entity-helper';
import { DailyReport } from '../../daily-reports/entities/daily-report.entity';
import { User } from '../../users/entities/user.entity';

@Entity()
export class WellInformation extends EntityHelper {
  @OneToOne(() => DailyReport, dailyReport => dailyReport.wellInformation, { nullable: true })
  @JoinColumn({ name: 'dailyReportId' })
  dailyReport: DailyReport;

  @CreateDateColumn({ type: 'timestamp' })
  reportedAt: Date;

  @ManyToOne(() => User, user => user.wellInformation)
  @JoinColumn({ name: 'engineerId' })
  engineer?: User | null;

  @Column({ type: String, nullable: false })
  activity: string;

  @Column({ type: 'float', nullable: false })
  measuredDepth: number;

  @Column({ type: 'float', nullable: false })
  trueVerticalDepth: number;

  @Column({ type: 'float', nullable: false })
  inclination: number;

  @Column({ type: 'float', nullable: false })
  azimuth: number;

  @Column({ type: 'float', nullable: false })
  weightOnBit: number;

  @Column({ type: 'float', nullable: false })
  rotaryWeight: number;

  @Column({ type: 'float', nullable: false })
  standOffWeight: number;

  @Column({ type: 'float', nullable: false })
  pullUpWeight: number;

  @Column({ type: 'float', nullable: false })
  revolutionsPerMinute: number;

  @Column({ type: 'float', nullable: false })
  rateOfPenetration: number;

  @Column({ type: String, nullable: false })
  drillingInterval: string;

  @Column({ type: String, nullable: false })
  formation: string;

  @Column({ type: 'float', nullable: false })
  depthDrilled: number;

  @Column({ type: 'float', nullable: false })
  totalStringLength: number;

  @Column({ type: 'float', nullable: false })
  totalLength: number;
}
