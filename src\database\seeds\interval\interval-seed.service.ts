import { Injectable } from '@nestjs/common';
import { faker } from '@faker-js/faker';
import { Repository } from 'typeorm';
import { Interval } from '../../../modules/intervals/entities/interval.entity';
import { Well } from '../../../modules/wells/entities/well.entity';
import { InjectRepository } from '@nestjs/typeorm';

@Injectable()
export class IntervalSeedService {
  constructor(
    @InjectRepository(Interval)
    private intervalService: Repository<Interval>,
    @InjectRepository(Well)
    private wellService: Repository<Well>,
  ) {}

  async run(): Promise<void> {
    await this.fakeIntervals();
  }

  private async fakeIntervals(): Promise<void> {
    const wells = (await this.wellService.find({})) ?? [];
    for (let i = 0; i < wells.length; i++) {
      await this.intervalService.save(
        this.intervalService.create({
          wellId: wells[i].id,
          interval: faker.number.int(100),
          notes: faker.lorem.text(),
        }),
      );
      if (i % 2 == 0) {
        await this.intervalService.save(
          this.intervalService.create({
            wellId: wells[i].id,
            interval: faker.number.int(100),
            notes: faker.lorem.text(),
          }),
        );
      }
    }
  }
}
