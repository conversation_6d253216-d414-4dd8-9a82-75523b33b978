import { Injectable } from '@nestjs/common';
import { guides } from '../datas/guides';
import { Repository } from 'typeorm';
import { Guide } from '../../../modules/guides/entities/guide.entity';
import { InjectRepository } from '@nestjs/typeorm';

@Injectable()
export class GuideSeedService {
  constructor(
    @InjectRepository(Guide)
    private guideService: Repository<Guide>,
  ) {}

  async run(): Promise<void> {
    await this.fakeGuides();
  }

  private async fakeGuides(): Promise<void> {
    for (let i = 0; i < guides.length; i++) {
      await this.guideService.save(
        this.guideService.create({
          type: i + 1,
          content: guides[i],
        }),
      );
    }
  }
}
