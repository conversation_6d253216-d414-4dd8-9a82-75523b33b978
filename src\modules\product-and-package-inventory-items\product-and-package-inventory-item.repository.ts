import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { BaseRepository } from '../../common/bases/base.repository';
import { ProductAndPackageInventoryItem } from './entities/product-and-package-inventory-item.entity';

@Injectable()
export class ProductAndPackageInventoryItemRepository extends BaseRepository<ProductAndPackageInventoryItem> {
  constructor(dataSource: DataSource) {
    super(ProductAndPackageInventoryItem, dataSource);
  }

  async getTotalCost(productAndPackageInventoryReportId: string): Promise<number> {
    const items = await this.createQueryBuilder('item')
      .where('item.productAndPackageInventoryReportId = :productAndPackageInventoryReportId', {
        productAndPackageInventoryReportId,
      })
      .getMany();
    if (!items.length) {
      return 0;
    }
    let total = 0;
    for (const item of items) {
      if (item.type === 1 || item.type === 2 || item.type === 4) {
        total += item.cost * item.quantity;
      }
      if (item.type === 3 || item.type === 5) {
        total -= item.cost * item.quantity;
      }
    }
    return total;
  }

  async getTotalQuantity(productAndPackageInventoryReportId: string): Promise<number> {
    const items = await this.createQueryBuilder('item')
      .where('item.productAndPackageInventoryReportId = :productAndPackageInventoryReportId', {
        productAndPackageInventoryReportId,
      })
      .getMany();
    if (!items.length) {
      return 0;
    }
    return items.map(e => e.quantity).reduce((a, b) => a + b);
    // let qty = 0;
    // for (const item of items) {
    //   if (item.type === 1 || item.type === 2 || item.type === 4) {
    //     qty += item.quantity;
    //   }
    //   if (item.type === 3 || item.type === 5) {
    //     qty -= item.quantity;
    //   }
    // }
    // return qty;
  }
}
