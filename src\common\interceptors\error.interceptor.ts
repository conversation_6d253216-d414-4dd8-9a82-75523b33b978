/**
 * @file Error interceptor
 * @module interceptor/error
 */

import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { CallHandler, ExecutionContext, Injectable, NestInterceptor } from '@nestjs/common';
import { ValidationError } from '../../errors/validation.error';
import { HttpBadRequestError } from '../../errors/bad-request.error';
import { HttpForbiddenError } from '../../errors/forbidden.error';
import { HttpNotFoundError } from '../../errors/not-found.error';
import { HttpUnauthorizedError } from '../../errors/unauthorized.error';
import { HttpInternalServerError } from '../../errors/internal-server.error';
import { ErrorCode } from '../../errors/error-code';
import { HttpDTOValidationError } from '../../errors/dto-validation.error';

/**
 * @class ErrorInterceptor
 * @classdesc catch error when controller Promise rejected
 */
@Injectable()
export class ErrorInterceptor implements NestInterceptor {
  constructor() {} // private readonly telegramService: TelegramService

  isInternalException(error) {
    return ![
      ValidationError.name,
      HttpBadRequestError.name,
      HttpForbiddenError.name,
      HttpNotFoundError.name,
      HttpUnauthorizedError.name,
      HttpDTOValidationError.name,
    ].includes(error.name);
  }

  intercept(context: ExecutionContext, next: CallHandler<any>): Observable<any> {
    const call$ = next.handle();
    return call$.pipe(
      catchError(error => {
        if (this.isInternalException(error)) {
          return throwError(() => new HttpInternalServerError(ErrorCode.INTERNAL_ERROR));
        }
        return throwError(() => error);
      }),
    );
  }
}
