import { Module } from '@nestjs/common';
import { SolidControlEquipmentTimeSeedService } from './solid-control-equipment-time-seed.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SolidControlEquipmentTime } from '../../../modules/solid-control-equipment-time/entities/solid-control-equipment-time.entity';
import { SolidControlEquipment } from '../../../modules/solid-control-equipment/entities/solid-control-equipment.entity';

@Module({
  imports: [TypeOrmModule.forFeature([SolidControlEquipmentTime, SolidControlEquipment])],
  providers: [SolidControlEquipmentTimeSeedService],
  exports: [SolidControlEquipmentTimeSeedService],
})
export class SolidControlEquipmentTimeSeedModule {}
