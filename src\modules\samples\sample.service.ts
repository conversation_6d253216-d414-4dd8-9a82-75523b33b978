import { Injectable } from '@nestjs/common';
import { SelectQueryBuilder } from 'typeorm';
import { PaginationDto } from '../../common/dtos/paginationDto';
import { PaginationResponseDto } from '../../common/dtos/pagination.response.dto';

import { SampleRepository } from './sample.repository';
import { GetSampleDto } from './dtos/requests/get-sample.dto';
import { Sample } from './entities/sample.entity';
import { CreateSampleDto } from './dtos/requests/create-sample.dto';
import { UpdateSampleDto } from './dtos/requests/udpate-sample.dto';
import { DailyReportRepository } from '../daily-reports/daily-report.repository';
import { User } from '../users/entities/user.entity';
import { SampleChartData } from './entities/sample-chart-data.entity';

@Injectable()
export class SampleService {
  constructor(
    private sampleRepository: SampleRepository,
    private reportRepository: DailyReportRepository,
  ) {}

  async findAll(
    query: GetSampleDto,
    paginationQuery: PaginationDto,
  ): Promise<PaginationResponseDto<Sample>> {
    const queryBuilder: SelectQueryBuilder<Sample> = this.sampleRepository
      .createQueryBuilder('sample')
      .where('sample.dailyReportId = :dailyReportId', { dailyReportId: query.dailyReportId })
      .orderBy('sample.createdAt', 'DESC');
    return this.sampleRepository.paginate(queryBuilder, paginationQuery);
  }

  async findAllChartSample(id: string): Promise<SampleChartData[]> {
    const sample = await this.sampleRepository.findOne({ where: { id } });
    const pv = sample?.plasticViscosity ?? 0;
    const yp = sample?.yieldPoint ?? 0;
    const shearRate1 = this.calculateShearRate(600, sample?.shearRate600 ?? 0);
    const shearStr1 = this.calculateShearStress(pv, yp, shearRate1);
    const shearRate2 = this.calculateShearRate(300, sample?.shearRate300 ?? 0);
    const shearStr2 = this.calculateShearStress(pv, yp, shearRate2);
    const shearRate3 = this.calculateShearRate(200, sample?.shearRate200 ?? 0);
    const shearStr3 = this.calculateShearStress(pv, yp, shearRate3);
    const shearRate4 = this.calculateShearRate(100, sample?.shearRate100 ?? 0);
    const shearStr4 = this.calculateShearStress(pv, yp, shearRate4);
    const shearRate5 = this.calculateShearRate(6, sample?.shearRate6 ?? 0);
    const shearStr5 = this.calculateShearStress(pv, yp, shearRate5);
    const shearRate6 = this.calculateShearRate(3, sample?.shearRate3 ?? 0);
    const shearStr6 = this.calculateShearStress(pv, yp, shearRate6);
    return [
      { shearRate: shearRate1, shearStress: shearStr1 } as SampleChartData,
      { shearRate: shearRate2, shearStress: shearStr2 } as SampleChartData,
      { shearRate: shearRate3, shearStress: shearStr3 } as SampleChartData,
      { shearRate: shearRate4, shearStress: shearStr4 } as SampleChartData,
      { shearRate: shearRate5, shearStress: shearStr5 } as SampleChartData,
      { shearRate: shearRate6, shearStress: shearStr6 } as SampleChartData,
    ];
  }

  calculateShearRate(speed: number, input: number): number {
    if (input === 0) {
      return 0;
    }
    return (2 * speed) / input;
  }

  calculateShearStress(pv: number, yp: number, rate: number): number {
    return pv + yp * rate;
  }

  async create(user: User, data: CreateSampleDto): Promise<Sample> {
    const dailyReport = { id: data.dailyReportId };
    delete data.dailyReportId;
    const createData = {
      dailyReport,
      ...data,
    };
    createData.linearGelStrengthPercent =
      data.mudWeight === 0 ? 0 : (data.gelStrength10s / data.mudWeight) * 100;
    createData.linearGelStrengthLbBbl =
      data.mudWeight === 0 ? 0 : (data.gelStrength10m / (data.mudWeight * 42)) * 100;
    createData.highGelStrengthPercent =
      data.mudWeight === 0 ? 0 : (data.gelStrength30m / data.mudWeight) * 100;
    createData.highGelStrengthLbBbl = data.gelStrength30m / 100;
    createData.bentoniteConcentrationPercent =
      data.mudWeight === 0 ? 0 : (data.mbtCapacity / data.mudWeight) * 100;
    createData.bentoniteConcentrationLbBbl = data.mbtCapacity;
    createData.drillSolidsConcentrationPercent =
      data.solids - createData.bentoniteConcentrationPercent;
    createData.drillSolidsConcentrationLbBbl =
      ((data.solids - createData.bentoniteConcentrationPercent) * data.mudWeight) / 100;
    createData.drillSolidsToBentoniteRatio =
      createData.bentoniteConcentrationLbBbl === 0
        ? 0
        : createData.drillSolidsConcentrationLbBbl / createData.bentoniteConcentrationLbBbl;
    createData.averageSpecificGravityOfSolids = data.averageSpecificGravityOfSolids;
    createData.apparentViscosity = data.plasticViscosity + data.yieldPoint / 2;
    await this.reportRepository.updateReportUpdatedBy(user, data.dailyReportId!);
    return this.sampleRepository.save(this.sampleRepository.create(createData));
  }

  async update(user: User, id: string, data: UpdateSampleDto): Promise<boolean> {
    const dailyReport = { id: data.dailyReportId };
    delete data.dailyReportId;
    const updateData = {
      dailyReport,
      ...data,
    };
    updateData.linearGelStrengthPercent =
      data.mudWeight === 0 ? 0 : (data.gelStrength10s / data.mudWeight) * 100;
    updateData.linearGelStrengthLbBbl =
      data.mudWeight === 0 ? 0 : (data.gelStrength10m / (data.mudWeight * 42)) * 100;
    updateData.highGelStrengthPercent =
      data.mudWeight === 0 ? 0 : (data.gelStrength30m / data.mudWeight) * 100;
    updateData.highGelStrengthLbBbl = data.gelStrength30m / 100;
    updateData.bentoniteConcentrationPercent =
      data.mudWeight === 0 ? 0 : (data.mbtCapacity / data.mudWeight) * 100;
    updateData.bentoniteConcentrationLbBbl = data.mbtCapacity;
    updateData.drillSolidsConcentrationPercent =
      data.solids - updateData.bentoniteConcentrationPercent;
    updateData.drillSolidsConcentrationLbBbl =
      ((data.solids - updateData.bentoniteConcentrationPercent) * data.mudWeight) / 100;
    updateData.drillSolidsToBentoniteRatio =
      updateData.bentoniteConcentrationLbBbl === 0
        ? 0
        : updateData.drillSolidsConcentrationLbBbl / updateData.bentoniteConcentrationLbBbl;
    updateData.averageSpecificGravityOfSolids = data.averageSpecificGravityOfSolids;
    updateData.apparentViscosity = data.plasticViscosity + data.yieldPoint / 2;
    await this.reportRepository.updateReportUpdatedBy(user, data.dailyReportId!);
    await this.sampleRepository.update({ id }, updateData);
    return true;
  }

  async findOne(id: string): Promise<Sample | null> {
    return this.sampleRepository.findOne({ where: { id } });
  }

  async softDeleteById(user: User, id: string): Promise<boolean> {
    const item = await this.sampleRepository
      .createQueryBuilder('sample')
      .where('sample.id = :id', { id })
      .leftJoinAndSelect('sample.dailyReport', 'dailyReport')
      .getOne();
    if (item?.dailyReport.id) {
      await this.reportRepository.updateReportUpdatedBy(user, item!.dailyReport.id);
    }
    await this.sampleRepository.softDelete({ id });
    return true;
  }
}
