import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IsExist } from '../../utils/validators/is-exists.validator';
import { IsNotExist } from '../../utils/validators/is-not-exists.validator';
import { TaskService } from './task.service';
import { JwtModule } from '@nestjs/jwt';
import { TaskController } from './task.controller';
import { TaskRepository } from './task.repository';
import { Task } from './entities/task.entity';
import { DailyReportRepository } from '../daily-reports/daily-report.repository';

@Module({
  imports: [TypeOrmModule.forFeature([Task]), JwtModule.register({})],
  controllers: [TaskController],
  providers: [IsExist, IsNotExist, TaskService, TaskRepository, DailyReportRepository],
  exports: [TaskService],
})
export class TaskModule {}
