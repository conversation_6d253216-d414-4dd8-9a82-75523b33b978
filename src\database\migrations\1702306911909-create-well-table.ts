import { MigrationInterface, QueryRunner, Table } from 'typeorm';
import {
  columnCreatedAt,
  columnDeletedAt,
  columnId,
  columnUpdatedAt,
} from '../constants/columns.constant';

export class CreateWellTable1702306911909 implements MigrationInterface {
  private tableName = 'well';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: this.tableName,
        columns: [
          columnId,
          {
            name: 'companyId',
            type: 'uuid',
            isNullable: true,
          },
          {
            name: 'stateOrProvinceId',
            type: 'uuid',
            isNullable: true,
          },
          {
            name: 'countryId',
            type: 'uuid',
            isNullable: true,
          },
          {
            name: 'nameOrNo',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'apiWellNo',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'latitude',
            type: 'float8',
            isNullable: true,
          },
          {
            name: 'longitude',
            type: 'float8',
            isNullable: true,
          },
          {
            name: 'fieldOrBlock',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'sectionOrTownshipOrRange',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'countyOrParishOrOffshoreArea',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'rigName',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'spudDate',
            type: 'date',
            isNullable: true,
          },
          {
            name: 'stockPoint',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'stockPointContact',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'operator',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'contractor',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'kickOffPoint',
            type: 'float8',
            isNullable: true,
          },
          {
            name: 'landingPoint',
            type: 'float8',
            isNullable: true,
          },
          {
            name: 'seaLevel',
            type: 'float8',
            isNullable: true,
          },
          {
            name: 'airGap',
            type: 'float8',
            isNullable: true,
          },
          {
            name: 'waterDepth',
            type: 'float8',
            isNullable: true,
          },
          {
            name: 'riserId',
            type: 'float8',
            isNullable: true,
          },
          {
            name: 'riserOD',
            type: 'float8',
            isNullable: true,
          },
          {
            name: 'chokeLineId',
            type: 'float8',
            isNullable: true,
          },
          {
            name: 'killLineId',
            type: 'float8',
            isNullable: true,
          },
          {
            name: 'boostLineId',
            type: 'float8',
            isNullable: true,
          },
          {
            name: 'rateOfPenetration',
            type: 'boolean',
            isNullable: true,
          },
          {
            name: 'revolutionsPerMinute',
            type: 'boolean',
            isNullable: true,
          },
          {
            name: 'eccentricity',
            type: 'boolean',
            isNullable: true,
          },
          {
            name: 'archived',
            type: 'boolean',
            isNullable: false,
          },
          columnCreatedAt,
          columnUpdatedAt,
          columnDeletedAt,
        ],
      }),
      false,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable(this.tableName);
  }
}
