import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IsExist } from '../../utils/validators/is-exists.validator';
import { IsNotExist } from '../../utils/validators/is-not-exists.validator';
import { NoteService } from './note.service';
import { Note } from './entities/note.entity';
import { JwtModule } from '@nestjs/jwt';
import { NoteController } from './note.controller';
import { NoteRepository } from './note.repository';
import { DailyReportRepository } from '../daily-reports/daily-report.repository';

@Module({
  imports: [TypeOrmModule.forFeature([Note]), JwtModule.register({})],
  controllers: [NoteController],
  providers: [IsExist, IsNotExist, NoteService, NoteRepository, DailyReportRepository],
  exports: [NoteService],
})
export class NoteModule {}
