import { Body, Controller, Get, HttpCode, HttpStatus, Param, Put, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { AuthGuard } from '../auth/guards/auth.guard';
import { TargetPropertyResponseDto } from './dtos/responses/target-property.response.dto';

import { toDto } from '../../common/transformers/dto.transformer';

import { RolesDecorator } from '../auth/decorators/roles.decorator';
import { UserRole } from '../roles/enums/roles.enum';
import { RolesGuard } from '../auth/guards/roles.guard';

import { TargetPropertyService } from './target-property.service';
import { UpdateTargetPropertyDto } from './dtos/requests/update-target-property.dto';
import { Responder } from '../../common/decorators/responder.decorator';

@ApiTags('TargetProperty')
@Controller('targetProperties')
@ApiBearerAuth('access-token')
@RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN, UserRole.SUPERVISOR, UserRole.ENGINEER)
@UseGuards(AuthGuard, RolesGuard)
export class TargetPropertyController {
  constructor(private readonly propertyService: TargetPropertyService) {}

  @Put('/:id')
  @ApiOperation({ description: 'Update target property' })
  @Responder.handle('Update target property')
  @HttpCode(HttpStatus.CREATED)
  async update(@Param('id') id: string, @Body() data: UpdateTargetPropertyDto): Promise<boolean> {
    return this.propertyService.update(id, data);
  }

  @Get('/:wellId')
  @ApiOperation({ description: 'Get target property' })
  @Responder.handle('Get target property')
  @HttpCode(HttpStatus.OK)
  async detail(@Param('wellId') wellId: string): Promise<TargetPropertyResponseDto> {
    const data = await this.propertyService.findOne(wellId);
    return toDto(TargetPropertyResponseDto, data);
  }
}
