import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { PaginationResponseDto } from '../../common/dtos/pagination.response.dto';
import { AuthGuard } from '../auth/guards/auth.guard';

import { PaginationDto } from '../../common/dtos/paginationDto';
import { toDto, toPaginateDtos } from '../../common/transformers/dto.transformer';

import { RolesDecorator } from '../auth/decorators/roles.decorator';
import { UserRole } from '../roles/enums/roles.enum';
import { RolesGuard } from '../auth/guards/roles.guard';
import { GetTaskDto } from './dtos/requests/get-tasks.dto';
import { TaskService } from './task.service';
import { CreateTaskDto } from './dtos/requests/create-task.dto';
import { UpdateTaskDto } from './dtos/requests/update-task.dto';
import { TaskResponseDto } from './dtos/responses/task.response.dto';
import { Responder } from '../../common/decorators/responder.decorator';
import { CurrentUser } from '../../common/decorators/current-user.decorator';
import { User } from '../users/entities/user.entity';

@ApiTags('Task')
@Controller('tasks')
@ApiBearerAuth('access-token')
@RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN, UserRole.SUPERVISOR, UserRole.ENGINEER)
@UseGuards(AuthGuard, RolesGuard)
export class TaskController {
  constructor(private readonly service: TaskService) {}

  @Get()
  @ApiOperation({ description: 'Get tasks' })
  @Responder.handle('Get tasks')
  @HttpCode(HttpStatus.OK)
  async findAll(
    @Query() query: GetTaskDto,
    @Query() paginationQuery: PaginationDto,
  ): Promise<PaginationResponseDto<TaskResponseDto>> {
    const data = await this.service.findAll(query, paginationQuery);
    return toPaginateDtos(TaskResponseDto, data);
  }

  @Post()
  @ApiOperation({ description: 'Create task' })
  @Responder.handle('Create task')
  @HttpCode(HttpStatus.CREATED)
  async create(@CurrentUser() user: User, @Body() data: CreateTaskDto): Promise<TaskResponseDto> {
    const value = await this.service.create(user, data);
    return toDto(TaskResponseDto, value);
  }

  @Put('/:id')
  @ApiOperation({ description: 'Update task' })
  @Responder.handle('Update task')
  @HttpCode(HttpStatus.CREATED)
  async update(
    @CurrentUser() user: User,
    @Param('id') id: string,
    @Body() data: UpdateTaskDto,
  ): Promise<boolean> {
    return this.service.update(user, id, data);
  }

  @Get('/:id')
  @ApiOperation({ description: 'Get task detail' })
  @Responder.handle('Get task detail')
  @HttpCode(HttpStatus.OK)
  async detail(@Param('id') id: string): Promise<TaskResponseDto> {
    const data = await this.service.findOne(id);
    return toDto(TaskResponseDto, data);
  }

  @Delete('/:id')
  @ApiOperation({ description: 'Delete task' })
  @Responder.handle('Delete detail')
  @HttpCode(HttpStatus.OK)
  delete(@CurrentUser() user: User, @Param('id') id: string): Promise<boolean> {
    return this.service.softDeleteById(user, id);
  }
}
