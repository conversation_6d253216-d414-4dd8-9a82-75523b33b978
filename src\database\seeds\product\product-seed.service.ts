import { Injectable } from '@nestjs/common';
import { faker } from '@faker-js/faker';
import { Repository } from 'typeorm';
import { Company } from '../../../modules/companies/entities/company.entity';
import { Product } from '../../../modules/products/entities/product.entity';
import { InjectRepository } from '@nestjs/typeorm';

@Injectable()
export class ProductSeedService {
  constructor(
    @InjectRepository(Company)
    private companyRepository: Repository<Company>,
    @InjectRepository(Product)
    private productRepository: Repository<Product>,
  ) {}

  async run(): Promise<void> {
    await this.fakeProducts();
  }

  private async fakeProducts(): Promise<void> {
    const company = await this.companyRepository.findOne({ where: {} });
    for (let i = 0; i < 15; i++) {
      await this.productRepository.save(
        this.productRepository.create({
          companyId: company?.id,
          name: faker.commerce.productName(),
          description: faker.lorem.text(),
          price: faker.number.float({ max: 200, precision: 0.01 }),
        }),
      );
    }
  }
}
