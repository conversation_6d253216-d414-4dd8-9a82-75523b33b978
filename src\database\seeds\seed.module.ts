import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import appConfig from 'src/config/app.config';
import databaseConfig from 'src/database/config/database.config';
import { DataSource, DataSourceOptions } from 'typeorm';
import { TypeOrmConfigService } from '../typeorm-config.service';
import { UserSeedModule } from './user/user-seed.module';
import { CompanySeedModule } from './company/company-seed.module';
import { CustomerSeedModule } from './customer/customer-seed.module';
import { CustomerContactSeedModule } from './customer-contact/customer-contact-seed.module';
import { GuideSeedModule } from './guide/guide-seed.module';
import { WellSeedModule } from './well/well-seed.module';
import { IntervalSeedModule } from './interval/interval-seed.module';
import { DailyReportSeedModule } from './daily-report/daily-report-seed.module';
import { WellInformationSeedModule } from './well-information/well-information-seed.module';
import { CasedHoleSeedModule } from './cased-hole/cased-hole-seed.module';
import { OpenHoleSeedModule } from './open-hole/open-hole-seed.module';
import { DrillStringSeedModule } from './drill-string/drill-string-seed.module';
import { DrillBitSeedModule } from './drill-bit/drill-bit-seed.module';
import { NozzleSeedModule } from './nozzle/nozzle-seed.module';
import { SolidSeedModule } from './solid/solid-seed.module';
import { SolidControlEquipmentSeedModule } from './solid-control-equipment/solid-control-equipment-seed.module';
import { SampleSeedModule } from './sample/sample-seed.module';
import { SolidControlEquipmentTimeSeedModule } from './solid-control-equipment-time/solid-control-equipment-time-seed.module';
import { SolidControlEquipmentInputSeedModule } from './solid-control-equipment-input/solid-control-equipment-input-seed.module';
import { TaskSeedModule } from './task/task-seed.module';
import { ProductAndPackageInventorySeedModule } from './product-and-package-inventory/product-and-package-inventory-seed.module';
import { ProductAndPackageInventoryItemSeedModule } from './product-and-package-inventory-item/product-and-package-inventory-item-seed.module';
import { ProductSeedModule } from './product/product-seed.module';
import { VolumeTrackingSeedModule } from './volume-tracking/volume-tracking-seed.module';
import { VolumeTrackingItemSeedModule } from './volume-tracking-item/volume-tracking-item-seed.module';
import { CostSeedModule } from './cost/cost-seed.module';
import { NoteSeedModule } from './note/note-seed.module';
import { PlanSeedModule } from './plan/plan-seed.module';
import { PumpSeedModule } from './pump/pump-seed.module';
import { CountryStateSeedModule } from './country-state/country-state-seed.module';
import { CostSettingSeedModule } from './cost-setting/cost-setting-seed.module';
import { PumpDurationSeedModule } from './pump-duration/pump-duration-seed.module';
import { SolidControlEquipmentTypeSeedModule } from './solid-control-equipment-type/solid-control-equipment-type-seed.module';
import { SolidControlEquipmentModule } from '../../modules/solid-control-equipment/solid-control-equipment.module';
import { TargetPropertySeedModule } from './target-property/target-property-seed.module';
import { ProductAndPackageInventoryReportSeedModule } from './product-and-package-inventory-report/product-and-package-inventory-report-seed.module';
import { RoleSeedModule } from './roles/role-seed.module';

@Module({
  imports: [
    UserSeedModule,
    CompanySeedModule,
    CustomerSeedModule,
    CustomerContactSeedModule,
    CountryStateSeedModule,
    GuideSeedModule,
    WellSeedModule,
    IntervalSeedModule,
    DailyReportSeedModule,
    WellInformationSeedModule,
    CasedHoleSeedModule,
    OpenHoleSeedModule,
    DrillStringSeedModule,
    DrillBitSeedModule,
    NozzleSeedModule,
    SolidSeedModule,
    SolidControlEquipmentSeedModule,
    SampleSeedModule,
    TargetPropertySeedModule,
    PlanSeedModule,
    PumpSeedModule,
    PumpDurationSeedModule,
    SolidControlEquipmentModule,
    SolidControlEquipmentTypeSeedModule,
    SolidControlEquipmentTimeSeedModule,
    SolidControlEquipmentInputSeedModule,
    TaskSeedModule,
    ProductAndPackageInventorySeedModule,
    ProductAndPackageInventoryItemSeedModule,
    ProductAndPackageInventoryReportSeedModule,
    ProductSeedModule,
    VolumeTrackingSeedModule,
    VolumeTrackingItemSeedModule,
    CostSeedModule,
    NoteSeedModule,
    CostSettingSeedModule,
    RoleSeedModule,
    ConfigModule.forRoot({
      isGlobal: true,
      load: [databaseConfig, appConfig],
      envFilePath: ['.env'],
    }),
    TypeOrmModule.forRootAsync({
      useClass: TypeOrmConfigService,
      dataSourceFactory: async (options: DataSourceOptions) => {
        return new DataSource(options).initialize();
      },
    }),
  ],
})
export class SeedModule {}
