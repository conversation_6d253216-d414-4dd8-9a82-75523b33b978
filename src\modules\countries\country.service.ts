import { Injectable } from '@nestjs/common';
import { Country } from './entities/country.entity';
import { PaginationDto } from '../../common/dtos/paginationDto';
import { Pagination } from '../../common/types/request-response.type';
import { CountryRepository } from './country.repository';
import { SelectQueryBuilder } from 'typeorm';
import { buildGetAllCountries } from './helpers/query-builders/get-all-countries.query-builder';
import { GetCountriesQueryDto } from './dtos/requests/get-countries.dto';

@Injectable()
export class CountryService {
  constructor(private repository: CountryRepository) {}

  async findAll(
    query: GetCountriesQueryDto,
    paginationQuery: PaginationDto,
  ): Promise<Pagination<Country>> {
    const queryBuilder: SelectQueryBuilder<Country> = buildGetAllCountries(this.repository, query);
    return await this.repository.paginate(queryBuilder, paginationQuery);
  }
}
