import { MigrationInterface, QueryRunner, Table } from 'typeorm';
import {
  columnCreatedAt,
  columnDeletedAt,
  columnId,
  columnUpdatedAt,
} from '../constants/columns.constant';

export class CreateWellInformationTable1702441875943 implements MigrationInterface {
  private tableName = 'well_information';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: this.tableName,
        columns: [
          columnId,
          {
            name: 'dailyReportId',
            type: 'uuid',
            isNullable: false,
          },
          {
            name: 'reportedAt',
            type: 'timestamp',
            isNullable: false,
            default: 'now()',
          },
          {
            name: 'engineerId',
            type: 'uuid',
            isNullable: false,
          },
          {
            name: 'activity',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'measuredDepth',
            type: 'float8',
            isNullable: true,
          },
          {
            name: 'trueVerticalDepth',
            type: 'float8',
            isNullable: true,
          },
          {
            name: 'inclination',
            type: 'float8',
            isNullable: true,
          },
          {
            name: 'azimuth',
            type: 'float8',
            isNullable: true,
          },
          {
            name: 'weightOnBit',
            type: 'float8',
            isNullable: true,
          },
          {
            name: 'rotaryWeight',
            type: 'float8',
            isNullable: true,
          },
          {
            name: 'standOffWeight',
            type: 'float8',
            isNullable: true,
          },
          {
            name: 'pullUpWeight',
            type: 'float8',
            isNullable: true,
          },
          {
            name: 'revolutionsPerMinute',
            type: 'float8',
            isNullable: true,
          },
          {
            name: 'rateOfPenetration',
            type: 'float8',
            isNullable: true,
          },
          {
            name: 'drillingInterval',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'formation',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'depthDrilled',
            type: 'float8',
            isNullable: true,
          },
          {
            name: 'totalStringLength',
            type: 'float8',
            isNullable: true,
          },
          {
            name: 'totalLength',
            type: 'float8',
            isNullable: true,
          },
          columnCreatedAt,
          columnUpdatedAt,
          columnDeletedAt,
        ],
      }),
      false,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable(this.tableName);
  }
}
