import { Exclude, Expose, Type } from 'class-transformer';
import { ProductInfoResponseDto } from './product-info.response.dto';
import { ProductAndPackageInventoryItemInfoResponseDto } from './product-and-package-inventory-item-info.response.dto';

@Exclude()
export class ProductAndPackageInventoryReportDetailResponseDto {
  @Expose()
  id: string;

  @Expose()
  @Type(() => ProductInfoResponseDto)
  product: ProductInfoResponseDto;

  @Expose()
  quantity: number;

  @Expose()
  totalCost: number;

  @Expose()
  @Type(() => ProductAndPackageInventoryItemInfoResponseDto)
  items: ProductAndPackageInventoryItemInfoResponseDto[];
}
