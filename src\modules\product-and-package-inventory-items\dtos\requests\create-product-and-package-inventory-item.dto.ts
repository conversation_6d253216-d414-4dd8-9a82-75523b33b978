import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsNotEmpty, IsNumber, IsOptional, IsString, IsUUID } from 'class-validator';

export class CreateProductAndPackageInventoryItemDto {
  @ApiProperty({ required: true })
  @IsUUID()
  @IsNotEmpty()
  productAndPackageInventoryReportId: string;

  @ApiProperty({ required: true, example: 'volume tracking id' })
  @IsUUID()
  @IsNotEmpty()
  locationId: string;

  @ApiProperty({ required: true })
  @IsInt()
  @IsNotEmpty()
  type: number;

  @ApiProperty({ required: true })
  @IsInt()
  quantity: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  cost: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  bolNo: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  notes: string;
}
