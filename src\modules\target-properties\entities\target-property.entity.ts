import { Column, Entity, OneToOne } from 'typeorm';
import { EntityHelper } from '../../../utils/entity-helper';
import { Well } from '../../wells/entities/well.entity';

@Entity()
export class TargetProperty extends EntityHelper {
  @OneToOne(() => Well)
  @Column({ nullable: false })
  wellId: string;

  @Column({ nullable: false })
  fluidType: number;

  @Column({ type: 'float', nullable: false })
  mudWeight: number;

  @Column({ type: 'float', nullable: false })
  funnelViscosity: number;

  @Column({ type: 'float', nullable: false })
  plasticViscosity: number;

  @Column({ type: 'float', nullable: false })
  yieldPoint: number;

  @Column({ type: 'float', nullable: false })
  apiFiltrate: number;

  @Column({ type: 'float', nullable: false })
  apiCakeThickness: number;

  @Column({ type: 'float', nullable: false })
  pH: number;

  @Column({ type: 'float', nullable: false })
  mudAlkalinity: number;

  @Column({ type: 'float', nullable: false })
  filtrateAlkalinity: number;

  @Column({ type: 'float', nullable: false })
  chlorides: number;

  @Column({ type: 'float', nullable: false })
  totalHardness: number;

  @Column({ type: 'float', nullable: false })
  linearGelStrengthPercent: number;

  @Column({ type: 'float', nullable: false })
  rpm: number;
}
