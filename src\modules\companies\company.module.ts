import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IsExist } from '../../utils/validators/is-exists.validator';
import { IsNotExist } from '../../utils/validators/is-not-exists.validator';
import { CompanyService } from './company.service';
import { Company } from './entities/company.entity';
import { CompanyRepository } from './company.repository';
import { CompanyController } from './company.controller';
import { JwtModule } from '@nestjs/jwt';
import { UserRepository } from '../users/user.repository';
import { RoleRepository } from '../roles/role.repository';

@Module({
  imports: [TypeOrmModule.forFeature([Company]), JwtModule.register({})],
  providers: [
    IsExist,
    IsNotExist,
    CompanyService,
    CompanyRepository,
    UserRepository,
    RoleRepository,
  ],
  exports: [CompanyService],
  controllers: [CompanyController],
})
export class CompanyModule {}
