import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { PaginationResponseDto } from '../../common/dtos/pagination.response.dto';
import { AuthGuard } from '../auth/guards/auth.guard';
import { PlanResponseDto } from './dtos/responses/plan.response.dto';

import { PaginationDto } from '../../common/dtos/paginationDto';
import { toDto, toPaginateDtos } from '../../common/transformers/dto.transformer';

import { RolesDecorator } from '../auth/decorators/roles.decorator';
import { UserRole } from '../roles/enums/roles.enum';
import { RolesGuard } from '../auth/guards/roles.guard';

import { PlanService } from './plan.service';
import { GetPlansQueryDto } from './dtos/requests/get-plans.dto';
import { CreatePlanDto } from './dtos/requests/create-plan.dto';
import { UpdatePlanDto } from './dtos/requests/update-plan.dto';
import { Responder } from '../../common/decorators/responder.decorator';

@ApiTags('Plan')
@Controller('plans')
@ApiBearerAuth('access-token')
@RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN, UserRole.SUPERVISOR, UserRole.ENGINEER)
@UseGuards(AuthGuard, RolesGuard)
export class PlanController {
  constructor(private readonly planService: PlanService) {}

  @Get()
  @ApiOperation({ description: 'Get plans' })
  @Responder.handle('Get plans')
  @HttpCode(HttpStatus.OK)
  async findAll(
    @Query() query: GetPlansQueryDto,
    @Query() paginationQuery: PaginationDto,
  ): Promise<PaginationResponseDto<PlanResponseDto>> {
    const data = await this.planService.findAll(query, paginationQuery);
    return toPaginateDtos(PlanResponseDto, data);
  }

  @Post()
  @ApiOperation({ description: 'Create plan' })
  @Responder.handle('Create plan')
  @HttpCode(HttpStatus.CREATED)
  async create(@Body() data: CreatePlanDto): Promise<PlanResponseDto> {
    const value = await this.planService.create(data);
    return toDto(PlanResponseDto, value);
  }

  @Put('/:id')
  @ApiOperation({ description: 'Update plan' })
  @Responder.handle('Update plan')
  @HttpCode(HttpStatus.CREATED)
  async update(@Param('id') id: string, @Body() data: UpdatePlanDto): Promise<boolean> {
    return this.planService.update(id, data);
  }

  @Get('/:id')
  @ApiOperation({ description: 'Get plan detail' })
  @Responder.handle('Get plan detail')
  @HttpCode(HttpStatus.OK)
  async detail(@Param('id') id: string): Promise<PlanResponseDto> {
    const data = await this.planService.findOne(id);
    return toDto(PlanResponseDto, data);
  }

  @Delete('/:id')
  @ApiOperation({ description: 'Delete plan' })
  @Responder.handle('Delete plan')
  @HttpCode(HttpStatus.OK)
  delete(@Param('id') id: string): Promise<boolean> {
    return this.planService.softDeleteById(id);
  }
}
