import { Injectable } from '@nestjs/common';
import { SelectQueryBuilder } from 'typeorm';
import { PaginationDto } from '../../common/dtos/paginationDto';
import { PaginationResponseDto } from '../../common/dtos/pagination.response.dto';

import { VolumeTrackingRepository } from './volume-tracking.repository';
import { GetVolumeTrackingDto } from './dtos/requests/get-volume-tracking.dto';
import { VolumeTracking } from './entities/volume-tracking.entity';
import { CreateVolumeTrackingDto } from './dtos/requests/create-volume-tracking.dto';
import { UpdateVolumeTrackingDto } from './dtos/requests/update-volume-tracking.dto';
import { DailyReportRepository } from '../daily-reports/daily-report.repository';
import { User } from '../users/entities/user.entity';

@Injectable()
export class VolumeTrackingService {
  constructor(
    private volumeTrackingRepository: VolumeTrackingRepository,
    private reportRepository: DailyReportRepository,
  ) {}

  async findAll(
    query: GetVolumeTrackingDto,
    paginationQuery: PaginationDto,
  ): Promise<PaginationResponseDto<VolumeTracking>> {
    const queryBuilder: SelectQueryBuilder<VolumeTracking> = this.volumeTrackingRepository
      .createQueryBuilder('volumeTracking')
      .where('volumeTracking.dailyReportId = :dailyReportId', {
        dailyReportId: query.dailyReportId,
      })
      .orderBy('volumeTracking.name', 'ASC')
      .addOrderBy('volumeTracking.createdAt', 'DESC');
    return this.volumeTrackingRepository.paginate(queryBuilder, paginationQuery);
  }

  async create(user: User, data: CreateVolumeTrackingDto): Promise<VolumeTracking> {
    await this.reportRepository.updateReportUpdatedBy(user, data.dailyReportId);
    const volume = this.volumeTrackingRepository.create(data);
    return this.volumeTrackingRepository.save(volume);
  }

  async update(user: User, id: string, data: UpdateVolumeTrackingDto): Promise<boolean> {
    await this.reportRepository.updateReportUpdatedBy(user, data.dailyReportId);
    await this.volumeTrackingRepository.update({ id }, data);
    return true;
  }

  async findOne(id: string): Promise<VolumeTracking | null> {
    return this.volumeTrackingRepository
      .createQueryBuilder('volumeTracking')
      .leftJoinAndSelect('volumeTracking.volumeItems', 'volumeItems')
      .where('volumeTracking.id = :id', { id })
      .orderBy('volumeItems.createdAt', 'DESC')
      .getOne();
  }

  async softDeleteById(user: User, id: string): Promise<boolean> {
    const item = await this.volumeTrackingRepository
      .createQueryBuilder('volume')
      .where('volume.id = :id', { id })
      .getOne();
    if (item?.dailyReportId) {
      await this.reportRepository.updateReportUpdatedBy(user, item!.dailyReportId);
    }
    await this.volumeTrackingRepository.softDelete({ id });
    return true;
  }
}
