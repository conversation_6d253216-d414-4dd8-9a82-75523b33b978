import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { PaginationResponseDto } from '../../common/dtos/pagination.response.dto';
import { AuthGuard } from '../auth/guards/auth.guard';

import { PaginationDto } from '../../common/dtos/paginationDto';
import { toDto, toPaginateDtos } from '../../common/transformers/dto.transformer';

import { RolesDecorator } from '../auth/decorators/roles.decorator';
import { UserRole } from '../roles/enums/roles.enum';
import { RolesGuard } from '../auth/guards/roles.guard';
import { CostService } from './cost.service';
import { GetCostsQueryDto } from './dtos/requests/get-costs.dto';
import { CostResponseDto } from './dtos/responses/cost.response.dto';
import { CreateCostDto } from './dtos/requests/create-cost.dto';
import { UpdateCostDto } from './dtos/requests/update-cost.dto';
import { Responder } from '../../common/decorators/responder.decorator';
import { CurrentUser } from '../../common/decorators/current-user.decorator';
import { User } from '../users/entities/user.entity';
import { CostSummaryResponseDto } from './dtos/responses/cost-summary.response.dto';
import { GetCostSummaryDto } from './dtos/requests/get-cost-summary.dto';

@ApiTags('Cost')
@Controller('costs')
@ApiBearerAuth('access-token')
@RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN, UserRole.SUPERVISOR, UserRole.ENGINEER)
@UseGuards(AuthGuard, RolesGuard)
export class CostController {
  constructor(private readonly costService: CostService) {}

  @Get()
  @ApiOperation({ description: 'Get costs' })
  @Responder.handle('Get costs')
  @HttpCode(HttpStatus.OK)
  async findAll(
    @Query() query: GetCostsQueryDto,
    @Query() paginationQuery: PaginationDto,
  ): Promise<PaginationResponseDto<CostResponseDto>> {
    const data = await this.costService.findAll(query, paginationQuery);
    return toPaginateDtos(CostResponseDto, data);
  }

  @Post()
  @ApiOperation({ description: 'Create cost' })
  @Responder.handle('Create cost')
  @HttpCode(HttpStatus.CREATED)
  async create(@CurrentUser() user: User, @Body() data: CreateCostDto): Promise<CostResponseDto> {
    const value = await this.costService.create(user, data);
    return toDto(CostResponseDto, value);
  }

  @Put('/:id')
  @ApiOperation({ description: 'Update cost' })
  @Responder.handle('Update cost')
  @HttpCode(HttpStatus.CREATED)
  async update(
    @CurrentUser() user: User,
    @Param('id') id: string,
    @Body() data: UpdateCostDto,
  ): Promise<boolean> {
    return this.costService.update(user, id, data);
  }

  @Get('/:id')
  @ApiOperation({ description: 'Get cost detail' })
  @Responder.handle('Get cost detail')
  @HttpCode(HttpStatus.OK)
  async detail(@Param('id') id: string): Promise<CostResponseDto> {
    const data = await this.costService.findOne(id);
    return toDto(CostResponseDto, data);
  }

  @Get('/detail/costSummary')
  @ApiOperation({ description: 'Get cost summary' })
  @Responder.handle('Get cost summary')
  @HttpCode(HttpStatus.OK)
  async costSummary(@Query() query: GetCostSummaryDto): Promise<CostSummaryResponseDto> {
    const data = await this.costService.findCostSummary(query);
    return toDto(CostSummaryResponseDto, data);
  }

  @Delete('/:id')
  @ApiOperation({ description: 'Delete cost' })
  @Responder.handle('Delete cost')
  @HttpCode(HttpStatus.OK)
  delete(@CurrentUser() user: User, @Param('id') id: string): Promise<boolean> {
    return this.costService.softDeleteById(user, id);
  }
}
