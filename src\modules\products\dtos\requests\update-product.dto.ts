import { ApiProperty } from '@nestjs/swagger';
import { IsN<PERSON>ber, IsOptional, IsString, IsUUID } from "class-validator";

export class UpdateProductDto {
  @ApiProperty()
  @IsString()
  @IsOptional()
  name: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  price?: number;

  @ApiProperty({ required: false, example: 'xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx' })
  @IsUUID()
  @IsOptional()
  @IsString()
  companyId?: string;
}
