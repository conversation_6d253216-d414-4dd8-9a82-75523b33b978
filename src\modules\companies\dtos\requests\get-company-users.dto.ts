import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString, IsUUID } from 'class-validator';
import { UserRole } from '../../../roles/enums/roles.enum';
import { Transform } from 'class-transformer';

export class GetCompanyUsersDto {
  @ApiProperty({ required: true })
  @IsUUID()
  @IsNotEmpty()
  companyId: string;

  @ApiProperty({
    required: true,
    enum: UserRole,
    example: 2,
    description: 'supervisor: 2, engineer: 3, companyAdmin: 4',
  })
  @Transform(params => parseInt(params.value))
  @IsNumber()
  role: number;

  @ApiProperty({required: false})
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  name?: string;

  @ApiProperty({
    required: false,
    example: 'name',
    description: 'name | assignedDate | email',
  })
  @IsOptional()
  @IsString()
  sortBy?: string;

  @ApiProperty({
    required: false,
    description: 'ASC | DESC',
  })
  @IsOptional()
  @IsString()
  sortDirection?: string;
}
