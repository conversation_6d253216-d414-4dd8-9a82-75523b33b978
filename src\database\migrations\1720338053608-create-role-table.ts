import { MigrationInterface, QueryRunner, Table } from 'typeorm';
import { columnId } from '../constants/columns.constant';

export class CreateRoleTable1720338053608 implements MigrationInterface {
  private tableName = 'role';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: this.tableName,
        columns: [
          columnId,
          {
            name: 'value',
            type: 'int',
            isNullable: false,
          },
        ],
      }),
      false,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable(this.tableName);
  }
}
