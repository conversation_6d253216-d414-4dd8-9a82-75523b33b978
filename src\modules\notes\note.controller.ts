import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { PaginationResponseDto } from '../../common/dtos/pagination.response.dto';
import { AuthGuard } from '../auth/guards/auth.guard';

import { PaginationDto } from '../../common/dtos/paginationDto';
import { toDto, toPaginateDtos } from '../../common/transformers/dto.transformer';

import { RolesDecorator } from '../auth/decorators/roles.decorator';
import { UserRole } from '../roles/enums/roles.enum';
import { RolesGuard } from '../auth/guards/roles.guard';
import { GetNoteDto } from './dtos/requests/get-note.dto';
import { NoteService } from './note.service';
import { NoteResponseDto } from './dtos/responses/note.response.dto';
import { CreateNoteDto } from './dtos/requests/create-note.dto';
import { UpdateNoteDto } from './dtos/requests/update-note.dto';
import { Responder } from '../../common/decorators/responder.decorator';
import { CurrentUser } from '../../common/decorators/current-user.decorator';
import { User } from '../users/entities/user.entity';

@ApiTags('Note')
@Controller('notes')
@ApiBearerAuth('access-token')
@RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN, UserRole.SUPERVISOR, UserRole.ENGINEER)
@UseGuards(AuthGuard, RolesGuard)
export class NoteController {
  constructor(private readonly service: NoteService) {}

  @Get()
  @ApiOperation({ description: 'Get note' })
  @Responder.handle('Get note')
  @HttpCode(HttpStatus.OK)
  async findAll(
    @Query() query: GetNoteDto,
    @Query() paginationQuery: PaginationDto,
  ): Promise<PaginationResponseDto<NoteResponseDto>> {
    const data = await this.service.findAll(query, paginationQuery);
    return toPaginateDtos(NoteResponseDto, data);
  }

  @Post()
  @ApiOperation({ description: 'Create note' })
  @Responder.handle('Create note')
  @HttpCode(HttpStatus.CREATED)
  async create(@CurrentUser() user: User, @Body() data: CreateNoteDto): Promise<NoteResponseDto> {
    const value = await this.service.create(user, data);
    return toDto(NoteResponseDto, value);
  }

  @Put('/:id')
  @ApiOperation({ description: 'Update note' })
  @Responder.handle('Update note')
  @HttpCode(HttpStatus.CREATED)
  async update(
    @CurrentUser() user: User,
    @Param('id') id: string,
    @Body() data: UpdateNoteDto,
  ): Promise<boolean> {
    return this.service.update(user, id, data);
  }

  @Get('/:id')
  @ApiOperation({ description: 'Get note detail' })
  @Responder.handle('Get note detail')
  @HttpCode(HttpStatus.OK)
  async detail(@Param('id') id: string): Promise<NoteResponseDto> {
    const data = await this.service.findOne(id);
    return toDto(NoteResponseDto, data);
  }

  @Delete('/:id')
  @ApiOperation({ description: 'Delete note' })
  @Responder.handle('Delete note')
  @HttpCode(HttpStatus.OK)
  delete(@CurrentUser() user: User, @Param('id') id: string): Promise<boolean> {
    return this.service.softDeleteById(user, id);
  }
}
