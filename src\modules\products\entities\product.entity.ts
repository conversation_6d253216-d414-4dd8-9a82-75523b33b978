import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany } from 'typeorm';
import { EntityHelper } from '../../../utils/entity-helper';
import { Company } from '../../companies/entities/company.entity';
import { ProductAndPackageInventoryReport } from '../../product-and-package-inventory-reports/entities/product-and-package-inventory-report.entity';

@Entity()
export class Product extends EntityHelper {
  @ManyToOne(() => Company, { nullable: false })
  @JoinColumn({ name: 'companyId' })
  @Column({ type: String, nullable: false })
  companyId: string | null;

  @ManyToOne(() => Company, { nullable: false })
  company: Company;

  @Column({ type: String, nullable: false })
  name: string;

  @Column({ type: String, nullable: false })
  description: string;

  @Column({ type: 'float', nullable: false })
  price: number;

  @OneToMany(() => ProductAndPackageInventoryReport, report => report.product)
  reports: ProductAndPackageInventoryReport[];
}
