import { Injectable } from '@nestjs/common';
import { faker } from '@faker-js/faker';
import { InjectRepository } from '@nestjs/typeorm';
import { CasedHole } from '../../../modules/cased-holes/entities/cased-hole.entity';
import { Repository } from 'typeorm';
import { DailyReport } from '../../../modules/daily-reports/entities/daily-report.entity';

@Injectable()
export class CasedHoleSeedService {
  constructor(
    @InjectRepository(DailyReport)
    private dailyReportRepository: Repository<DailyReport>,
    @InjectRepository(CasedHole)
    private casedHoleRepository: Repository<CasedHole>,
  ) {}

  async run(): Promise<void> {
    await this.fakeCasedHoles();
  }

  private async fakeCasedHoles(): Promise<void> {
    const reports = await this.dailyReportRepository.find({ where: {} });
    for (let i = 0; i < reports.length; i++) {
      for (let j = 0; j < 20; j++) {
        await this.casedHoleRepository.save(
          this.casedHoleRepository.create({
            dailyReport: { id: reports[i]?.id },
            casingLength: faker.number.float({ max: 200, precision: 0.01 }),
            casingShoeDepth: faker.number.float({ max: 200, precision: 0.01 }),
            description: faker.lorem.text(),
            insideDiameter: faker.number.float({ max: 200, precision: 0.01 }),
            outsideDiameter: faker.number.float({ max: 200, precision: 0.01 }),
            weight: faker.number.float({ max: 200, precision: 0.01 }),
            topDepth: faker.number.float({ max: 200, precision: 0.01 }),
          }),
        );
      }
    }
  }
}
