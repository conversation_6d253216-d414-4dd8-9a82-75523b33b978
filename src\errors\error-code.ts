export enum ErrorCode {
  INTERNAL_ERROR = 'INTERNAL_ERROR',
  USER_NOT_FOUND = 'USER_NOT_FOUND',
  INCORRECT_EMAIL_PASSWORD = 'INCORRECT_EMAIL_PASSWORD',
  EMAIL_EXISTS = 'EMAIL_EXISTS',
  WELL_NOT_FOUND = 'WELL_NOT_FOUND',
  REPORT_PRODUCT_EXIST = 'REPORT_PRODUCT_EXIST',
  USER_EXIST = 'USER_EXIST',
  NOT_PERMITTED_ROLE = 'NOT_PERMITTED_ROLE',
  IN_VALID_ENGINEER_OR_SUPERVISOR = 'IN_VALID_ENGINEER_OR_SUPERVISOR',
  USER_IN_ACTIVE = 'USER_IN_ACTIVE',
  REPORT_ITEM_INITIAL_EXIST = 'REPORT_ITEM_INITIAL_EXIST',
  INCORRECT_CURRENT_PASSWORD = 'INCORRECT_CURRENT_PASSWORD',
  INVALID_OTP = 'INVALID_OTP',
  ACCEPT_INVITATION_FAILED = 'ACCEPT_INVITATION_FAILED',
  EMAIL_NOT_FOUND = 'EMAIL_NOT_FOUND',
  COMPANY_EXIST = 'COMPANY_EXIST',
  COMPANY_NOT_FOUND = 'COMPANY_NOT_FOUND',
  PRODUCT_EXIST = 'PRODUCT_EXIST',
  COST_EXIST = 'COST_EXIST',
  INVITE_LINK_IS_NOT_VALID = 'INVITE_LINK_IS_NOT_VALID',
  PRODUCT_BEING_USED = 'PRODUCT_BEING_USED',
  COST_SETTING_BEING_USED = 'COST_SETTING_BEING_USED',
  EXIST_USER_COMPANY_INVALID = 'EXIST_USER_COMPANY_INVALID',
  CURRENT_REPORT_NOT_FOUND = 'CURRENT_REPORT_NOT_FOUND',
}
