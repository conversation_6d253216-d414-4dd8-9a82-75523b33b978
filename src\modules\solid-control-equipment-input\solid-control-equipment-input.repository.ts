import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { BaseRepository } from '../../common/bases/base.repository';
import { SolidControlEquipmentInput } from './entities/solid-control-equipment-input.entity';

@Injectable()
export class SolidControlEquipmentInputRepository extends BaseRepository<SolidControlEquipmentInput> {
  constructor(dataSource: DataSource) {
    super(SolidControlEquipmentInput, dataSource);
  }
}
