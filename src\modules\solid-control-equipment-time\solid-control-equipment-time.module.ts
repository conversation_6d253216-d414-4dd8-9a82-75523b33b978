import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IsExist } from '../../utils/validators/is-exists.validator';
import { IsNotExist } from '../../utils/validators/is-not-exists.validator';
import { SolidControlEquipmentTimeService } from './solid-control-equipment-time.service';
import { JwtModule } from '@nestjs/jwt';
import { SolidControlEquipmentTimeRepository } from './solid-control-equipment-time.repository';
import { SolidControlEquipmentTimeController } from './solid-control-equipment-time.controller';
import { SolidControlEquipmentTime } from './entities/solid-control-equipment-time.entity';
import { SolidControlEquipmentRepository } from '../solid-control-equipment/solid-control-equipment.repository';

@Module({
  imports: [TypeOrmModule.forFeature([SolidControlEquipmentTime]), JwtModule.register({})],
  controllers: [SolidControlEquipmentTimeController],
  providers: [
    IsExist,
    IsNotExist,
    SolidControlEquipmentTimeService,
    SolidControlEquipmentTimeRepository,
    SolidControlEquipmentRepository,
  ],
  exports: [SolidControlEquipmentTimeService],
})
export class SolidControlEquipmentTimeModule {}
