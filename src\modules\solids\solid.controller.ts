import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { AuthGuard } from '../auth/guards/auth.guard';
import { SolidResponseDto } from './dtos/responses/solid.response.dto';

import { toDto } from '../../common/transformers/dto.transformer';

import { RolesDecorator } from '../auth/decorators/roles.decorator';
import { UserRole } from '../roles/enums/roles.enum';
import { RolesGuard } from '../auth/guards/roles.guard';

import { SolidService } from './solid.service';
import { CreateSolidDto } from './dtos/requests/create-solid.dto';
import { UpdateSolidDto } from './dtos/requests/update-solid.dto';
import { Responder } from '../../common/decorators/responder.decorator';
import { CurrentUser } from '../../common/decorators/current-user.decorator';
import { User } from '../users/entities/user.entity';

@ApiTags('Solid')
@Controller('solids')
@ApiBearerAuth('access-token')
@RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN, UserRole.SUPERVISOR, UserRole.ENGINEER)
@UseGuards(AuthGuard, RolesGuard)
export class SolidController {
  constructor(private readonly planService: SolidService) {}

  @Post()
  @ApiOperation({ description: 'Create solid' })
  @Responder.handle('Create solid')
  @HttpCode(HttpStatus.CREATED)
  async create(@CurrentUser() user: User, @Body() data: CreateSolidDto): Promise<SolidResponseDto> {
    const value = await this.planService.create(user, data);
    return toDto(SolidResponseDto, value);
  }

  @Put('/:id')
  @ApiOperation({ description: 'Update solid' })
  @Responder.handle('Update solid')
  @HttpCode(HttpStatus.CREATED)
  async update(
    @CurrentUser() user: User,
    @Param('id') id: string,
    @Body() data: UpdateSolidDto,
  ): Promise<boolean> {
    return this.planService.update(user, id, data);
  }

  @Get('/:dailyReportId')
  @ApiOperation({ description: 'Get solid by report' })
  @Responder.handle('Get solid by report')
  @HttpCode(HttpStatus.OK)
  async detail(@Param('dailyReportId') dailyReportId: string): Promise<SolidResponseDto> {
    const data = await this.planService.findByReport(dailyReportId);
    return toDto(SolidResponseDto, data);
  }

  @Delete('/:id')
  @ApiOperation({ description: 'Delete solid' })
  @Responder.handle('Delete solid')
  @HttpCode(HttpStatus.OK)
  delete(@CurrentUser() user: User, @Param('id') id: string): Promise<boolean> {
    return this.planService.softDeleteById(user, id);
  }
}
