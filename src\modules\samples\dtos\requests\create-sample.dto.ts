import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsInt, IsNotEmpty, IsNumber, IsString, IsUUID } from 'class-validator';

export class CreateSampleDto {
  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  dailyReportId?: string;

  @ApiProperty({
    required: true,
    description: 'water: 1, oil:2, synthetic: 3',
    default: 1,
  })
  @IsInt()
  fluidType: number;

  @ApiProperty({ required: true })
  @IsBoolean()
  weightedMud: boolean;

  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty()
  sampleFrom: string;

  @ApiProperty({ required: true, example: '10:30:40' })
  @IsString()
  @IsNotEmpty()
  timeSampleTaken: string;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  flowlineTemperature: number;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  measuredDepth: number;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  mudWeight: number;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  funnelViscosity: number;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  temperatureForPlasticViscosity: number;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  plasticViscosity: number;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  yieldPoint: number;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  gelStrength10s: number;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  gelStrength10m: number;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  gelStrength30m: number;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  apiFiltrate: number;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  apiCakeThickness: number;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  temperatureForHTHP: number;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  hthpFiltrate: number;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  hthpCakeThickness: number;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  solids: number;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  oil: number;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  water: number;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  sandContent: number;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  mbtCapacity: number;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  pH: number;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  mudAlkalinity: number;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  filtrateAlkalinity: number;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  calcium: number;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  chlorides: number;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  totalHardness: number;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  excessLime: number;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  kPlus: number;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  makeUpWater: number;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  solidsAdjustedForSalt: number;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  fineLCM: number;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  coarseLCM: number;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  linearGelStrengthPercent?: number;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  linearGelStrengthLbBbl?: number;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  highGelStrengthPercent?: number;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  highGelStrengthLbBbl?: number;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  bentoniteConcentrationPercent?: number;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  bentoniteConcentrationLbBbl?: number;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  drillSolidsConcentrationPercent?: number;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  drillSolidsConcentrationLbBbl?: number;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  drillSolidsToBentoniteRatio?: number;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  averageSpecificGravityOfSolids?: number;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  shearRate600: number;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  shearRate300: number;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  shearRate200: number;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  shearRate100: number;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  shearRate6: number;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  shearRate3: number;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  apparentViscosity?: number;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  shearRate: number;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  shearStress: number;
}
