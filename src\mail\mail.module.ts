import { MailerModule } from '@nestjs-modules/mailer';
import { HandlebarsAdapter } from '@nestjs-modules/mailer/dist/adapters/handlebars.adapter';
import { Module } from '@nestjs/common';
import { MailService } from './mail.service';
import { join } from 'path';
import { ConfigService } from '@nestjs/config';
import { AllConfigType } from '../config/config.type';
import { MailjetService } from "./mailjet.service";

@Module({
  imports: [
    MailerModule.forRootAsync({
      useFactory: (config: ConfigService<AllConfigType>) => ({
        transport: {
          host: config.getOrThrow('mail.host', { infer: true }),
          secure: false,
          auth: {
            user: config.getOrThrow('mail.user', { infer: true }),
            pass: config.getOrThrow('mail.password', { infer: true }),
          },
        },
        defaults: {
          from: `"No Reply" <${config.getOrThrow('mail.defaultEmail', { infer: true })}>`,
        },
        template: {
          dir: join(__dirname, 'templates'),
          adapter: new HandlebarsAdapter(),
          options: {
            strict: true,
          },
        },
      }),
      inject: [ConfigService<AllConfigType>],
    }),
  ],
  providers: [MailService, MailjetService],
  exports: [MailService, MailjetService],
})
export class MailModule {}
