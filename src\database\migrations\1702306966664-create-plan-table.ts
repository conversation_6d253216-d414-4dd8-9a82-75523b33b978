import { MigrationInterface, QueryRunner, Table } from 'typeorm';
import {
  columnCreatedAt,
  columnDeletedAt,
  columnId,
  columnUpdatedAt,
} from '../constants/columns.constant';

export class CreatePlanTable1702306966664 implements MigrationInterface {
  private tableName = 'plan';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: this.tableName,
        columns: [
          columnId,
          {
            name: 'wellId',
            type: 'uuid',
            isNullable: false,
          },
          {
            name: 'mudDepth',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'day',
            type: 'int',
            isNullable: false,
          },
          {
            name: 'cost',
            type: 'float8',
            isNullable: false,
          },
          columnCreatedAt,
          columnUpdatedAt,
          columnDeletedAt,
        ],
      }),
      false,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable(this.tableName);
  }
}
