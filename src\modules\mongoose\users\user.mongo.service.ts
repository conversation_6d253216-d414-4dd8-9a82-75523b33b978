import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { User } from './user.schema';
import { CreateUserDto } from '../../users/dtos/requests/create-user.dto';
import bcrypt from 'bcryptjs';

@Injectable()
export class MongoUserService {
  constructor(@InjectModel('User') private readonly userModel: Model<User>) {}

  async findAll(): Promise<User[]> {
    return this.userModel.find().exec();
  }

  async findOne(id: string): Promise<User | null> {
    return this.userModel.findById(id).exec();
  }

  async findByEmail(email: string): Promise<User | null> {
    return this.userModel.findOne({ email }).exec();
  }

  async create(user: CreateUserDto): Promise<User> {
    const defaultPassword = await bcrypt.hash('0123456789', 10);
    const newUser = new this.userModel({ ...user, password: defaultPassword });
    return newUser.save();
  }

  async update(id: string, user: User): Promise<User | null> {
    return this.userModel.findByIdAndUpdate(id, user, { new: true }).exec();
  }

  async delete(id: string): Promise<void> {
    await this.userModel.findByIdAndDelete(id).exec();
  }

  async findBySupervisor(supervisorId: string): Promise<User[] | null> {
    return this.userModel.find({ supervisor: supervisorId }).exec();
  }

  async assignSupervisor(userId: string, supervisorId: string): Promise<User | null> {
    return this.userModel
      .findByIdAndUpdate(userId, { supervisor: supervisorId }, { new: true })
      .exec();
  }
}
