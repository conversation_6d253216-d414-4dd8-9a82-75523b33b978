import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';
import { UserRole } from '../enums/roles.enum';

@Schema()
export class Role extends Document {
  @Prop({ required: true })
  value: UserRole;

  @Prop({ type: [{ type: MongooseSchema.Types.ObjectId, ref: 'User' }] })
  users: string[];
}

export const RoleSchema = SchemaFactory.createForClass(Role);
