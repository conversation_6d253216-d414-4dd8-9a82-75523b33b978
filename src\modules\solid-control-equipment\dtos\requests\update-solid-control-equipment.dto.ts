import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsUUID } from 'class-validator';

export class UpdateSolidControlEquipmentDto {
  @ApiProperty({ required: true })
  @IsUUID()
  @IsNotEmpty()
  dailyReportId: string;

  @ApiProperty({ required: true })
  @IsUUID()
  @IsNotEmpty()
  typeId?: string;

  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  screen: string;
}
