import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsUUID } from 'class-validator';
import { IsUuidArray } from '../../../../common/decorators/class-validator/is-array-uuid.decorator';
import { UserRole } from '../../../roles/enums/roles.enum';
import { Transform } from 'class-transformer';

export class AssignAdminsDto {
  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsUUID()
  companyId: string;

  @ApiProperty({
    required: true,
    enum: UserRole,
    example: 2,
    description: 'supervisor: 2, engineer: 3, companyAdmin: 4',
  })
  @Transform(params => parseInt(params.value))
  @IsNumber()
  role: number;

  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsUuidArray()
  userIds: string[];
}
