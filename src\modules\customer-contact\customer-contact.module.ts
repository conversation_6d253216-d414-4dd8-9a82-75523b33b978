import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IsExist } from '../../utils/validators/is-exists.validator';
import { IsNotExist } from '../../utils/validators/is-not-exists.validator';
import { CustomerContactService } from './customer-contact.service';
import { CustomerContact } from './entities/customer-contact.entity';
import { CustomerContactController } from './customer-contact.controller';
import { JwtModule } from '@nestjs/jwt';
import { CustomerContactRepository } from './customer-contact.repository';

@Module({
  imports: [TypeOrmModule.forFeature([CustomerContact]), JwtModule.register({})],
  providers: [
    IsExist,
    IsNotExist,
    CustomerContactService,
    CustomerContact,
    CustomerContactRepository,
  ],
  exports: [CustomerContactService],
  controllers: [CustomerContactController],
})
export class CustomerContactModule {}
