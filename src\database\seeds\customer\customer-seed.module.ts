import { Module } from '@nestjs/common';
import { CustomerSeedService } from './customer-seed.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Customer } from '../../../modules/customers/entities/customer.entity';
import { Company } from '../../../modules/companies/entities/company.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Customer, Company])],
  providers: [CustomerSeedService],
  exports: [CustomerSeedService],
})
export class CustomerSeedModule {}
