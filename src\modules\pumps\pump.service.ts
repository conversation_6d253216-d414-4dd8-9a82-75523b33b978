import { Injectable } from '@nestjs/common';

import { SelectQueryBuilder } from 'typeorm';

import { PaginationResponseDto } from '../../common/dtos/pagination.response.dto';

import { PaginationDto } from '../../common/dtos/paginationDto';
import { PumpRepository } from './pump.repository';
import { GetPumpsQueryDto } from './dtos/requests/get-pumps.dto';
import { Pump } from './entities/pump.entity';
import { CreatePumpDto } from './dtos/requests/create-pump.dto';
import { UpdatePumpDto } from './dtos/requests/update-pump.dto';
import { DailyReportRepository } from '../daily-reports/daily-report.repository';
import { User } from '../users/entities/user.entity';
import { PumpSummaryEntity } from './entities/pump-summary.entity';

@Injectable()
export class PumpService {
  constructor(
    private repository: PumpRepository,
    private reportRepository: DailyReportRepository,
  ) {}

  async findAll(
    query: GetPumpsQueryDto,
    paginationQuery: PaginationDto,
  ): Promise<PaginationResponseDto<Pump>> {
    const queryBuilder: SelectQueryBuilder<Pump> = this.repository
      .createQueryBuilder('pump')
      .where('pump.dailyReportId = :dailyReportId', { dailyReportId: query.dailyReportId })
      .orderBy('pump.createdAt', 'DESC');
    return this.repository.paginate(queryBuilder, paginationQuery);
  }

  async create(user: User, data: CreatePumpDto): Promise<Pump> {
    await this.reportRepository.updateReportUpdatedBy(user, data.dailyReportId!);
    const pump = await this.repository.create(data);
    return this.repository.save(pump);
  }

  async update(user: User, id: string, data: UpdatePumpDto): Promise<boolean> {
    await this.reportRepository.updateReportUpdatedBy(user, data.dailyReportId!);
    await this.repository.update({ id }, data);
    return true;
  }

  async findOne(id: string): Promise<Pump | null> {
    return this.repository
      .createQueryBuilder('pump')
      .where('pump.id = :id', { id })
      .leftJoinAndSelect('pump.durations', 'durations')
      .orderBy('durations.createdAt', 'DESC')
      .getOne();
  }

  async summaryInfo(dailyReportId: string): Promise<PumpSummaryEntity> {
    const activePumps = await this.repository
      .createQueryBuilder('pump')
      .where('pump.dailyReportId = :dailyReportId', { dailyReportId })
      .andWhere('pump.inUse = true')
      .getMany();
    if (!activePumps.length) {
      return { totalRate: 0, pumpPressure: 0 };
    }
    const totalRate = activePumps.map(e => e.rate).reduce((a, b) => a + b);

    let totalPressure = 0;
    for (const pump of activePumps) {
      const pressure =
        pump.efficiency === 0 || pump.stroke === 0
          ? 0
          : (pump.displacement * pump.rate * 8.33) / (pump.efficiency * 2 * pump.stroke);
      totalPressure += pressure;
    }
    return {
      totalRate: parseFloat(totalRate.toFixed(2)),
      pumpPressure: parseFloat(totalPressure.toFixed(2)),
    };
  }

  async softDeleteById(user: User, id: string): Promise<boolean> {
    const item = await this.repository
      .createQueryBuilder('pump')
      .where('pump.id = :id', { id })
      .getOne();
    if (item?.dailyReportId) {
      await this.reportRepository.updateReportUpdatedBy(user, item!.dailyReportId);
    }
    await this.repository.softDelete({ id });
    return true;
  }
}
