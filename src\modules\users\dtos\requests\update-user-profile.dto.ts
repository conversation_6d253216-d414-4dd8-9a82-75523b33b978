import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsOptional, Validate, IsArray, Length, Matches, IsNumber, IsNotEmpty, IsUUID } from "class-validator";
import { Transform } from 'class-transformer';
import { lowerCaseTransformer } from 'src/common/transformers/lower-case.transformer';
import { IsEmailUniqueWithoutSelf } from 'src/common/decorators';
import { UserStatus } from "../../enums/statuses.enum";

export class UpdateUserProfileDto {
  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsUUID()
  @IsOptional()
  companyId?: string;

  @ApiProperty({ example: '<EMAIL>' })
  @Transform(lowerCaseTransformer)
  @IsOptional()
  @IsEmail()
  @Validate(IsEmailUniqueWithoutSelf)
  email: string;

  @ApiProperty({ example: 'First' })
  @IsOptional()
  firstName: string;

  @ApiProperty({ example: 'Last' })
  @IsOptional()
  lastName: string;

  @ApiProperty({ example: '123 ADB' })
  @IsOptional()
  address: string;

  @ApiProperty({ example: 'test' })
  @IsOptional()
  note: string;

  @ApiProperty({ example: '+44 20 7123 8888' })
  @IsOptional()
  @Length(10, 15, { message: 'Phone number must be between 10 and 15 characters long' })
  @Matches(/^\d+$/, { message: 'Phone number must contain only digits' })
  // @IsPhoneNumber()
  officePhone: string;

  @ApiProperty({ example: '+44 20 7123 8888' })
  @IsOptional()
  @Length(10, 15, { message: 'Phone number must be between 10 and 15 characters long' })
  @Matches(/^\d+$/, { message: 'Phone number must contain only digits' })
  // @IsPhoneNumber()
  mobilePhone: string;

  // @ApiProperty({ example: '81fe23b8-d294-4edb-a9d6-a656cfaad6d2' })
  // @IsOptional()
  // @IsUUID()
  // companyId: string;

  @ApiProperty({
    required: false,
    description: 'admin: 1, supervisor: 2, engineer: 3, companyAdmin: 4',
    example: [1],
  })
  @IsOptional()
  @IsArray()
  userRoles?: number[];

  @ApiProperty({
    required: false,
    enum: UserStatus,
    description: 'active: 1, inactive: 2',
  })
  @IsOptional()
  @IsNumber()
  @Transform(params => parseInt(params.value))
  status?: number;
}
