import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IsExist } from '../../utils/validators/is-exists.validator';
import { IsNotExist } from '../../utils/validators/is-not-exists.validator';
import { SolidControlEquipmentService } from './solid-control-equipment.service';
import { JwtModule } from '@nestjs/jwt';
import { SolidControlEquipmentController } from './solid-control-equipment.controller';
import { SolidControlEquipmentRepository } from './solid-control-equipment.repository';
import { SolidControlEquipment } from './entities/solid-control-equipment.entity';
import { DailyReportRepository } from '../daily-reports/daily-report.repository';

@Module({
  imports: [TypeOrmModule.forFeature([SolidControlEquipment]), JwtModule.register({})],
  controllers: [SolidControlEquipmentController],
  providers: [
    IsExist,
    IsNotExist,
    SolidControlEquipmentService,
    SolidControlEquipmentRepository,
    DailyReportRepository,
  ],
  exports: [SolidControlEquipmentService],
})
export class SolidControlEquipmentModule {}
