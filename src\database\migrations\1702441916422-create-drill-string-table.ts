import { MigrationInterface, QueryRunner, Table } from 'typeorm';
import {
  columnCreatedAt,
  columnDeletedAt,
  columnId,
  columnUpdatedAt,
} from '../constants/columns.constant';

export class CreateDrillStringTable1702441916422 implements MigrationInterface {
  private tableName = 'drill_string';
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: this.tableName,
        columns: [
          columnId,
          {
            name: 'dailyReportId',
            type: 'uuid',
            isNullable: false,
          },
          {
            name: 'description',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'outsideDiameter',
            type: 'float8',
            isNullable: true,
          },
          {
            name: 'weight',
            type: 'float8',
            isNullable: true,
          },
          {
            name: 'insideDiameter',
            type: 'float8',
            isNullable: true,
          },
          {
            name: 'length',
            type: 'float8',
            isNullable: true,
          },
          columnCreatedAt,
          columnUpdatedAt,
          columnDeletedAt,
        ],
      }),
      false,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable(this.tableName);
  }
}
