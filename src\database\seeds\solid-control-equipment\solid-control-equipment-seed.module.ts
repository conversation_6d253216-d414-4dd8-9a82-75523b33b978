import { Module } from '@nestjs/common';
import { SolidControlEquipmentSeedService } from './solid-control-equipment-seed.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DailyReport } from '../../../modules/daily-reports/entities/daily-report.entity';
import { SolidControlEquipment } from '../../../modules/solid-control-equipment/entities/solid-control-equipment.entity';
import { SolidControlEquipmentType } from '../../../modules/solid-control-equipment-type/entities/solid-control-equipment-type.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([DailyReport, SolidControlEquipment, SolidControlEquipmentType]),
  ],
  providers: [SolidControlEquipmentSeedService],
  exports: [SolidControlEquipmentSeedService],
})
export class SolidControlEquipmentSeedModule {}
