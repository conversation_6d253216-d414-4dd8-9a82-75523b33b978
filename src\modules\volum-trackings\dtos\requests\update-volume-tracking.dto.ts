import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsNotEmpty, IsNumber, IsOptional, IsString, IsUUID } from 'class-validator';

export class UpdateVolumeTrackingDto {
  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  dailyReportId: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  storageType?: string;

  @ApiProperty()
  @IsInt()
  @IsOptional()
  status?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  measuredVolume?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  mudWeight?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  mudType?: number;
}
