import { MigrationInterface, QueryRunner, Table } from 'typeorm';
import {
  columnCreatedAt,
  columnDeletedAt,
  columnId,
  columnUpdatedAt,
} from '../constants/columns.constant';

export class CreateCustomerContactTable1702306938065 implements MigrationInterface {
  private tableName = 'customer_contact';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: this.tableName,
        columns: [
          columnId,
          {
            name: 'customerId',
            type: 'uuid',
            isNullable: false,
          },
          {
            name: 'name',
            type: 'varchar',
            isNullable: false,
          },
          {
            name: 'address',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'officePhone',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'mobilePhone',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'emailAddress',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'notes',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'primaryContact',
            type: 'boolean',
            default: false,
          },
          {
            name: 'notifyOnNewReport',
            type: 'boolean',
            default: false,
          },
          columnCreatedAt,
          columnUpdatedAt,
          columnDeletedAt,
        ],
      }),
      false,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable(this.tableName);
  }
}
