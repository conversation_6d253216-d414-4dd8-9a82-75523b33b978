import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { AuthGuard } from '../auth/guards/auth.guard';
import { toDto, toPaginateDtos } from '../../common/transformers/dto.transformer';

import { RolesDecorator } from '../auth/decorators/roles.decorator';
import { UserRole } from '../roles/enums/roles.enum';
import { RolesGuard } from '../auth/guards/roles.guard';
import { ProductAndPackageInventoryItemService } from './product-and-package-inventory-item.service';
import { CreateProductAndPackageInventoryItemDto } from './dtos/requests/create-product-and-package-inventory-item.dto';
import { ProductAndPackageInventoryItemResponseDto } from './dtos/responses/product-and-package-inventory-item.response.dto';
import { UpdateProductAndPackageInventoryItemDto } from './dtos/requests/update-product-and-package-inventory-item.dto';
import { Responder } from '../../common/decorators/responder.decorator';
import { PaginationDto } from '../../common/dtos/paginationDto';
import { PaginationResponseDto } from '../../common/dtos/pagination.response.dto';
import { GetProductAndPackageInventoryItemsDto } from './dtos/requests/get-product-and-package-inventory-items.dto';
import { CurrentUser } from '../../common/decorators/current-user.decorator';
import { User } from '../users/entities/user.entity';

@ApiTags('ProductAndPackageInventoryItem')
@Controller('productAndPackageInventoryItems')
@ApiBearerAuth('access-token')
@RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN, UserRole.SUPERVISOR, UserRole.ENGINEER)
@UseGuards(AuthGuard, RolesGuard)
export class ProductAndPackageInventoryItemController {
  constructor(private readonly service: ProductAndPackageInventoryItemService) {}

  @Get()
  @ApiOperation({ description: 'Get product and package inventory items' })
  @Responder.handle('Get product and package inventory items')
  @HttpCode(HttpStatus.OK)
  async findAll(
    @Query() query: GetProductAndPackageInventoryItemsDto,
    @Query() paginationQuery: PaginationDto,
  ): Promise<PaginationResponseDto<ProductAndPackageInventoryItemResponseDto>> {
    const data = await this.service.findAll(query, paginationQuery);
    return toPaginateDtos(ProductAndPackageInventoryItemResponseDto, data);
  }

  @Post()
  @ApiOperation({ description: 'Create product and package inventory item' })
  @Responder.handle('Create product and package inventory item')
  @HttpCode(HttpStatus.CREATED)
  async create(
    @CurrentUser() user: User,
    @Body() data: CreateProductAndPackageInventoryItemDto,
  ): Promise<ProductAndPackageInventoryItemResponseDto> {
    const value = await this.service.create(user, data);
    return toDto(ProductAndPackageInventoryItemResponseDto, value);
  }

  @Put('/:id')
  @ApiOperation({ description: 'Update product and package inventory item' })
  @Responder.handle('Update product and package inventory item')
  @HttpCode(HttpStatus.CREATED)
  async update(
    @CurrentUser() user: User,
    @Param('id') id: string,
    @Body() data: UpdateProductAndPackageInventoryItemDto,
  ): Promise<boolean> {
    return this.service.update(user, id, data);
  }

  @Get('/:id')
  @ApiOperation({ description: 'Get product and package inventory item detail' })
  @Responder.handle('Get product and package inventory item detail')
  @HttpCode(HttpStatus.OK)
  async detail(@Param('id') id: string): Promise<ProductAndPackageInventoryItemResponseDto> {
    const data = await this.service.findOne(id);
    return toDto(ProductAndPackageInventoryItemResponseDto, data);
  }

  @Delete('/:id')
  @ApiOperation({ description: 'Delete product and package inventory item' })
  @Responder.handle('Delete product and package inventory item')
  @HttpCode(HttpStatus.OK)
  delete(@CurrentUser() user: User, @Param('id') id: string): Promise<boolean> {
    return this.service.softDeleteById(user, id);
  }
}
