import { registerDecorator, ValidationOptions } from 'class-validator';
import { isArray } from 'util';

export function IsArrayEnum(vClassType: any, validationOptions?: ValidationOptions) {
  return (object: any, propertyName: string) => {
    registerDecorator({
      name: 'IsArrayEnum',
      target: object.constructor,
      propertyName,
      constraints: [],
      options: Object.assign(
        {
          message: `${propertyName} must be an array enum`,
        },
        validationOptions,
      ),
      validator: {
        validate(value: any) {
          const array = isArray(value);
          if (!array) {
            return false;
          }
          const values = value as [];
          return !values.some(v => {
            return !Object.values(vClassType).some(item => {
              return v === item;
            });
          });
        },
      },
    });
  };
}
