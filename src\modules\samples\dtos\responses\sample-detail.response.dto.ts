import { Expose, Transform } from 'class-transformer';
import { FluidTypeEnum } from '../../enums/fluid-type.enum';

export class SampleDetailResponseDto {
  @Expose()
  id: string;

  @Expose()
  fluidType: FluidTypeEnum;

  @Expose()
  weightedMud: boolean;

  @Expose()
  sampleFrom: string;

  @Expose()
  timeSampleTaken: string;

  @Expose()
  flowlineTemperature: number;

  @Expose()
  measuredDepth: number;

  @Expose()
  mudWeight: number;

  @Expose()
  funnelViscosity: number;

  @Expose()
  temperatureForPlasticViscosity: number;

  @Expose()
  plasticViscosity: number;

  @Expose()
  yieldPoint: number;

  @Expose()
  gelStrength10s: number;

  @Expose()
  gelStrength10m: number;

  @Expose()
  gelStrength30m: number;

  @Expose()
  apiFiltrate: number;

  @Expose()
  apiCakeThickness: number;

  @Expose()
  temperatureForHTHP: number;

  @Expose()
  hthpFiltrate: number;

  @Expose()
  hthpCakeThickness: number;

  @Expose()
  solids: number;

  @Expose()
  oil: number;

  @Expose()
  water: number;

  @Expose()
  sandContent: number;

  @Expose()
  mbtCapacity: number;

  @Expose()
  pH: number;

  @Expose()
  mudAlkalinity: number;

  @Expose()
  filtrateAlkalinity: number;

  @Expose()
  calcium: number;

  @Expose()
  chlorides: number;

  @Expose()
  totalHardness: number;

  @Expose()
  excessLime: number;

  @Expose()
  kPlus: number;

  @Expose()
  makeUpWater: number;

  @Expose()
  solidsAdjustedForSalt: number;

  @Expose()
  fineLCM: number;

  @Expose()
  coarseLCM: number;

  @Expose()
  @Transform(data => parseFloat(parseFloat(data.value).toFixed(2)))
  linearGelStrengthPercent: number;

  @Expose()
  @Transform(data => parseFloat(parseFloat(data.value).toFixed(2)))
  linearGelStrengthLbBbl: number;

  @Expose()
  @Transform(data => parseFloat(parseFloat(data.value).toFixed(2)))
  highGelStrengthPercent: number;

  @Expose()
  @Transform(data => parseFloat(parseFloat(data.value).toFixed(2)))
  highGelStrengthLbBbl: number;

  @Expose()
  @Transform(data => parseFloat(parseFloat(data.value).toFixed(2)))
  bentoniteConcentrationPercent: number;

  @Expose()
  @Transform(data => parseFloat(parseFloat(data.value).toFixed(2)))
  bentoniteConcentrationLbBbl: number;

  @Expose()
  @Transform(data => parseFloat(parseFloat(data.value).toFixed(2)))
  drillSolidsConcentrationPercent: number;

  @Expose()
  @Transform(data => parseFloat(parseFloat(data.value).toFixed(2)))
  drillSolidsConcentrationLbBbl: number;

  @Expose()
  @Transform(data => parseFloat(parseFloat(data.value).toFixed(2)))
  drillSolidsToBentoniteRatio: number;

  @Expose()
  @Transform(data => parseFloat(parseFloat(data.value).toFixed(2)))
  averageSpecificGravityOfSolids: number;

  @Expose()
  shearRate600: number;

  @Expose()
  shearRate300: number;

  @Expose()
  shearRate200: number;

  @Expose()
  shearRate100: number;

  @Expose()
  shearRate6: number;

  @Expose()
  shearRate3: number;

  @Expose()
  @Transform(data => parseFloat(parseFloat(data.value).toFixed(2)))
  apparentViscosity: number;

  @Expose()
  shearRate: number;

  @Expose()
  shearStress: number;
}
