import { Exclude, Expose, Type } from 'class-transformer';
import { UserResponseDto } from '../../../users/dtos/responses/user.response.dto';
import { DailyReportResponseDto } from '../../../daily-reports/dtos/responses/daily-report.response.dto';
import { WellResponseDto } from '../../../wells/dtos/responses/well.response.dto';

@Exclude()
export class WellInformationResponseDto {
  @Expose()
  id: string;

  @Expose()
  @Type(() => DailyReportResponseDto)
  dailyReport: DailyReportResponseDto;

  @Expose()
  @Type(() => WellResponseDto)
  well: WellResponseDto;

  @Expose()
  nameOrNo: string;

  @Expose()
  reportedAt: Date;

  @Expose()
  @Type(() => UserResponseDto)
  engineer: UserResponseDto;

  @Expose()
  activity: string;

  @Expose()
  measuredDepth: number;

  @Expose()
  trueVerticalDepth: number;

  @Expose()
  inclination: number;

  @Expose()
  azimuth: number;

  @Expose()
  weightOnBit: number;

  @Expose()
  rotaryWeight: number;

  @Expose()
  standOffWeight: number;

  @Expose()
  pullUpWeight: number;

  @Expose()
  revolutionsPerMinute: number;

  @Expose()
  rateOfPenetration: number;

  @Expose()
  drillingInterval: string;

  @Expose()
  formation: string;

  @Expose()
  depthDrilled: number;

  @Expose()
  totalStringLength: number;

  @Expose()
  totalLength: number;

  @Expose()
  tfa: number;
}
