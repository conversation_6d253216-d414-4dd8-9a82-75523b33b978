import { Module } from '@nestjs/common';
import { DrillStringSeedService } from './drill-string-seed.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DailyReport } from '../../../modules/daily-reports/entities/daily-report.entity';
import { DrillString } from '../../../modules/drill-strings/entities/drill-string.entity';

@Module({
  imports: [TypeOrmModule.forFeature([DailyReport, DrillString])],
  providers: [DrillStringSeedService],
  exports: [DrillStringSeedService],
})
export class DrillStringSeedModule {}
