import { Injectable } from '@nestjs/common';
import { State } from './entities/state.entity';
import { StateRepository } from './state.repository';
import { GetStatesQueryDto } from './dtos/requests/get-states.dto';
import { Pagination } from '../../common/types/request-response.type';
import { PaginationDto } from '../../common/dtos/paginationDto';
import { SelectQueryBuilder } from 'typeorm';

@Injectable()
export class StateService {
  constructor(private repository: StateRepository) {}

  async findAll(
    query: GetStatesQueryDto,
    paginationQuery: PaginationDto,
  ): Promise<Pagination<State>> {
    const { keyword, countryId } = query;
    let queryBuilder: SelectQueryBuilder<State> = this.repository
      .createQueryBuilder('state')
      .where('state.countryId = :countryId', { countryId: countryId });
    keyword &&
      (queryBuilder = queryBuilder.andWhere('state.name ILIKE :name', { name: `%${keyword}%` }));
    return await this.repository.paginate(queryBuilder, paginationQuery);
  }
}
