import { Injectable } from '@nestjs/common';

import { CreatePumpDurationDto } from './dtos/requests/create-pump-duration.dto';
import { PumpDuration } from './entities/pump-duration.entity';
import { PumpDurationRepository } from './pump-duration.repository';
import { UpdatePumpDurationDto } from './dtos/requests/update-pump-duration.dto';
import { Pump } from '../pumps/entities/pump.entity';
import { PumpRepository } from '../pumps/pump.repository';

@Injectable()
export class PumpDurationService {
  constructor(
    private repository: PumpDurationRepository,
    private pumpRepository: PumpRepository,
  ) {}

  async create(data: CreatePumpDurationDto): Promise<PumpDuration> {
    const duration = await this.repository.create(data);
    duration.pump = { id: data.pumpId } as Pump;
    const result = await this.repository.save(duration);
    const totalDurations = await this.repository.getTotalDurations(data.pumpId!);
    await this.pumpRepository.update({ id: data.pumpId }, { totalDurations });
    return result;
  }

  async update(id: string, data: UpdatePumpDurationDto): Promise<boolean> {
    const pump = { id: data.pumpId } as Pump;
    const dataUpdate = { pump, ...data };
    delete dataUpdate.pumpId;
    await this.repository.update({ id }, dataUpdate);
    const totalDurations = await this.repository.getTotalDurations(data.pumpId!);
    await this.pumpRepository.update({ id: data.pumpId }, { totalDurations });
    return true;
  }

  async findOne(id: string): Promise<PumpDuration | null> {
    return this.repository.findOne({ where: { id } });
  }

  async softDeleteById(id: string): Promise<boolean> {
    const pumpDuration = await this.repository.findOne({ where: { id }, relations: ['pump'] });
    await this.repository.softDelete({ id });
    if (pumpDuration?.pump?.id) {
      const totalDurations = await this.repository.getTotalDurations(pumpDuration!.pump.id);
      await this.pumpRepository.update({ id: pumpDuration!.pump.id }, { totalDurations });
    }
    return true;
  }
}
