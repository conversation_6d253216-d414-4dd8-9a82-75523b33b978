import { Exclude, Expose, Type } from 'class-transformer';
import { RoleResponseDto } from '../../../roles/dtos/responses/role.response.dto.js';
import { CompanyResponseDto } from '../../../companies/dtos/responses/company.response.dto';

@Exclude()
export class UserResponseDto {
  @Expose()
  id: string;

  @Expose()
  email: string;

  @Expose()
  firstName: string;

  @Expose()
  lastName: string;

  @Expose()
  address: string;

  @Expose()
  officePhone: string;

  @Expose()
  mobilePhone: string;

  @Expose()
  avatar: string;

  @Expose()
  status: string;

  @Expose()
  @Type(() => CompanyResponseDto)
  company: CompanyResponseDto;

  @Expose()
  @Type(() => RoleResponseDto)
  roles: RoleResponseDto[];

  @Expose()
  assignedDate: string;
}
