import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { PaginationResponseDto } from '../../common/dtos/pagination.response.dto';
import { AuthGuard } from '../auth/guards/auth.guard';

import { PaginationDto } from '../../common/dtos/paginationDto';
import { toDto, toPaginateDtos } from '../../common/transformers/dto.transformer';

import { RolesDecorator } from '../auth/decorators/roles.decorator';
import { UserRole } from '../roles/enums/roles.enum';
import { RolesGuard } from '../auth/guards/roles.guard';
import { NozzleService } from './nozzle.service';
import { GetNozzleDto } from './dtos/requests/get-nozzle.dto';
import { NozzleResponseDto } from './dtos/responses/nozzle.response.dto';
import { CreateNozzleDto } from './dtos/requests/create-nozzle.dto';
import { UpdateNozzleDto } from './dtos/requests/update-nozzle.dto';
import { Responder } from '../../common/decorators/responder.decorator';
import { CurrentUser } from '../../common/decorators/current-user.decorator';
import { User } from '../users/entities/user.entity';

@ApiTags('Nozzle')
@Controller('nozzles')
@ApiBearerAuth('access-token')
@RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN, UserRole.SUPERVISOR, UserRole.ENGINEER)
@UseGuards(AuthGuard, RolesGuard)
export class NozzleController {
  constructor(private readonly service: NozzleService) {}

  @Get()
  @ApiOperation({ description: 'Get nozzles' })
  @Responder.handle('Get nozzles')
  @HttpCode(HttpStatus.OK)
  async findAll(
    @Query() query: GetNozzleDto,
    @Query() paginationQuery: PaginationDto,
  ): Promise<PaginationResponseDto<NozzleResponseDto>> {
    const data = await this.service.findAll(query, paginationQuery);
    return toPaginateDtos(NozzleResponseDto, data);
  }

  @Get('/tfa/:dailyReportId')
  @ApiOperation({ description: 'Get TFA value' })
  @Responder.handle('Get TFA value')
  @HttpCode(HttpStatus.OK)
  async getTfa(
    @Param('dailyReportId') dailyReportId: string,
  ): Promise<number> {
    return this.service.getTfa(dailyReportId);
  }

  @Post()
  @ApiOperation({ description: 'Create nozzles' })
  @Responder.handle('Create nozzles')
  @HttpCode(HttpStatus.CREATED)
  async create(
    @CurrentUser() user: User,
    @Body() data: CreateNozzleDto,
  ): Promise<NozzleResponseDto> {
    const value = await this.service.create(user, data);
    return toDto(NozzleResponseDto, value);
  }

  @Put('/:id')
  @ApiOperation({ description: 'Update nozzle' })
  @Responder.handle('Update nozzle')
  @HttpCode(HttpStatus.CREATED)
  async update(
    @CurrentUser() user: User,
    @Param('id') id: string,
    @Body() data: UpdateNozzleDto,
  ): Promise<boolean> {
    return this.service.update(user, id, data);
  }

  @Get('/:id')
  @ApiOperation({ description: 'Get nozzle detail' })
  @Responder.handle('Get nozzle detail')
  @HttpCode(HttpStatus.OK)
  async detail(@Param('id') id: string): Promise<NozzleResponseDto> {
    const data = await this.service.findOne(id);
    return toDto(NozzleResponseDto, data);
  }

  @Delete('/:id')
  @ApiOperation({ description: 'Delete nozzle' })
  @Responder.handle('Delete nozzle')
  @HttpCode(HttpStatus.OK)
  delete(@CurrentUser() user: User, @Param('id') id: string): Promise<boolean> {
    return this.service.softDeleteById(user, id);
  }
}
