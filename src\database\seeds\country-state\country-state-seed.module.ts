import { Module } from '@nestjs/common';
import { CountryStateSeedService } from './country-state-seed.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { State } from '../../../modules/states/entities/state.entity';
import { Country } from '../../../modules/countries/entities/country.entity';

@Module({
  imports: [TypeOrmModule.forFeature([State, Country])],
  providers: [CountryStateSeedService],
  exports: [CountryStateSeedService],
})
export class CountryStateSeedModule {}
