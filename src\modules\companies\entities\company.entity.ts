import { Column, Entity, OneToMany } from 'typeorm';
import { EntityHelper } from '../../../utils/entity-helper';
import { User } from '../../users/entities/user.entity';
import { Customer } from '../../customers/entities/customer.entity';

@Entity()
export class Company extends EntityHelper {
  @Column({ type: String, nullable: false })
  name: string;

  @Column({ type: String, nullable: true })
  registerNumber: string;

  @Column({ type: String, nullable: true })
  description: string;

  @OneToMany(() => User, user => user.company)
  users: User[];

  @OneToMany(() => Customer, customer => customer.company)
  customers: Customer[];
}
