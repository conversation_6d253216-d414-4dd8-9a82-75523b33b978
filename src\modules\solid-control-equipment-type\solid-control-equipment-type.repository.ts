import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { BaseRepository } from '../../common/bases/base.repository';
import { SolidControlEquipmentType } from './entities/solid-control-equipment-type.entity';

@Injectable()
export class SolidControlEquipmentTypeRepository extends BaseRepository<SolidControlEquipmentType> {
  constructor(dataSource: DataSource) {
    super(SolidControlEquipmentType, dataSource);
  }
}
