import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IsExist } from '../../utils/validators/is-exists.validator';
import { IsNotExist } from '../../utils/validators/is-not-exists.validator';
import { VolumeTrackingItemService } from './volume-tracking-item.service';
import { VolumeTrackingItem } from './entities/volume-tracking-item.entity';
import { JwtModule } from '@nestjs/jwt';
import { VolumeTrackingItemRepository } from './volume-tracking-item.repository';
import { VolumeTrackingItemController } from './volume-tracking-item.controller';
import { VolumeTrackingRepository } from '../volum-trackings/volume-tracking.repository';

@Module({
  imports: [TypeOrmModule.forFeature([VolumeTrackingItem]), JwtModule.register({})],
  controllers: [VolumeTrackingItemController],
  providers: [
    IsExist,
    IsNotExist,
    VolumeTrackingItemService,
    VolumeTrackingItemRepository,
    VolumeTrackingRepository,
  ],
  exports: [VolumeTrackingItemService],
})
export class VolumeTrackingItemModule {}
