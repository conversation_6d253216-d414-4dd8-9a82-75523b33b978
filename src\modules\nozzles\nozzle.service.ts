import { Injectable } from '@nestjs/common';
import { SelectQueryBuilder } from 'typeorm';
import { PaginationDto } from '../../common/dtos/paginationDto';
import { PaginationResponseDto } from '../../common/dtos/pagination.response.dto';

import { NozzleRepository } from './nozzle.repository';
import { GetNozzleDto } from './dtos/requests/get-nozzle.dto';
import { Nozzle } from './entities/nozzle.entity';
import { CreateNozzleDto } from './dtos/requests/create-nozzle.dto';
import { UpdateNozzleDto } from './dtos/requests/update-nozzle.dto';
import { DailyReportRepository } from '../daily-reports/daily-report.repository';
import { User } from '../users/entities/user.entity';

@Injectable()
export class NozzleService {
  constructor(
    private nozzleRepository: NozzleRepository,
    private reportRepository: DailyReportRepository,
  ) {}

  async findAll(
    query: GetNozzleDto,
    paginationQuery: PaginationDto,
  ): Promise<PaginationResponseDto<Nozzle>> {
    const queryBuilder: SelectQueryBuilder<Nozzle> = this.nozzleRepository
      .createQueryBuilder('nozzle')
      .where('nozzle.dailyReportId = :dailyReportId', { dailyReportId: query.dailyReportId })
      .orderBy('nozzle.createdAt', 'DESC')
      .addOrderBy('nozzle.identificationNumber', 'ASC');
    return this.nozzleRepository.paginate(queryBuilder, paginationQuery);
  }

  async getTfa(dailyReportId: string): Promise<number> {
    const nozzles = await this.nozzleRepository.find({where: {dailyReportId}});
    let total = 0;
    if (nozzles.length) {
      nozzles.forEach((value) => {
        total = total + Math.PI * (value.orificeSize / 2) * (value.orificeSize / 2);
      });
    }
    return total;
  }

  async create(user: User, data: CreateNozzleDto): Promise<Nozzle> {
    await this.reportRepository.updateReportUpdatedBy(user, data.dailyReportId);
    return this.nozzleRepository.save(this.nozzleRepository.create(data));
  }

  async update(user: User, id: string, data: UpdateNozzleDto): Promise<boolean> {
    await this.reportRepository.updateReportUpdatedBy(user, data.dailyReportId);
    await this.nozzleRepository.update({ id }, data);
    return true;
  }

  async findOne(id: string): Promise<Nozzle | null> {
    return this.nozzleRepository.findOne({ where: { id } });
  }

  async softDeleteById(user: User, id: string): Promise<boolean> {
    const item = await this.nozzleRepository
      .createQueryBuilder('nozzle')
      .where('nozzle.id = :id', { id })
      .getOne();
    if (item?.dailyReportId) {
      await this.reportRepository.updateReportUpdatedBy(user, item!.dailyReportId!);
    }
    await this.nozzleRepository.softDelete({ id });
    return true;
  }
}
