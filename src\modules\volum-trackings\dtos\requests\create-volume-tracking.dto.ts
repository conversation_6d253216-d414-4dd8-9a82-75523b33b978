import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsNotEmpty, IsNumber, IsString, IsUUID } from 'class-validator';

export class CreateVolumeTrackingDto {
  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  dailyReportId: string;

  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty()
  storageType: string;

  @ApiProperty({ required: true, default: 1 })
  @IsNotEmpty()
  @IsInt()
  status: number = 1;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  measuredVolume: number;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  mudWeight: number;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  mudType: number;
}
