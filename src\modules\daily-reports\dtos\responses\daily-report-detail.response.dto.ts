import { Expose, Type } from 'class-transformer';
import { WellInformationResponseDto } from '../../../well-informations/dtos/responses/well-information.response.dto';
import { NoteResponseDto } from '../../../notes/dtos/responses/note.response.dto';
import { UserResponseDto } from '../../../users/dtos/responses/user.response.dto';
import { WellInfoResponseDto } from './well-info.response.dto';

export class DailyReportDetailResponseDto {
  @Expose()
  id: string;

  @Expose()
  companyId: string;

  // @Expose()
  // reportDate: string;

  @Expose()
  createdAt: string;

  @Expose()
  @Type(() => WellInfoResponseDto)
  well: WellInfoResponseDto;

  @Expose()
  @Type(() => WellInformationResponseDto)
  wellInformation?: WellInformationResponseDto;

  @Expose()
  @Type(() => NoteResponseDto)
  notes?: NoteResponseDto[];

  @Expose()
  @Type(() => UserResponseDto)
  createdBy: UserResponseDto;

  @Expose()
  @Type(() => UserResponseDto)
  updatedBy: UserResponseDto;
}
