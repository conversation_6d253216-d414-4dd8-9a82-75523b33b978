import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { BaseRepository } from '../../common/bases/base.repository';
import { ProductAndPackageInventory } from './entities/product-and-package-inventory.entity';

@Injectable()
export class ProductAndPackageInventoryRepository extends BaseRepository<ProductAndPackageInventory> {
  constructor(dataSource: DataSource) {
    super(ProductAndPackageInventory, dataSource);
  }
}
