import { Injectable } from '@nestjs/common';

import { SolidControlEquipmentTypeRepository } from './solid-control-equipment-type.repository';
import { SolidControlEquipmentType } from './entities/solid-control-equipment-type.entity';
import { User } from '../users/entities/user.entity';

@Injectable()
export class SolidControlEquipmentTypeService {
  constructor(private taskRepository: SolidControlEquipmentTypeRepository) {}

  async findAll(user: User): Promise<SolidControlEquipmentType[]> {
    return this.taskRepository
      .createQueryBuilder('type')
      .where('type.companyId = :companyId', { companyId: user.companyId })
      .orderBy('type.createdAt', 'DESC')
      .getMany();
  }
}
