import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm';
import { EntityHelper } from '../../../utils/entity-helper';
import { DailyReport } from '../../daily-reports/entities/daily-report.entity';

@Entity()
export class DrillBit extends EntityHelper {
  @ManyToOne(() => DailyReport, { nullable: false })
  @JoinColumn({ name: 'dailyReportId' })
  @Column({ type: String, nullable: false })
  dailyReportId: string;

  @Column({ type: String, nullable: false })
  bitNo: string;

  @Column({ type: String, nullable: false })
  type: string;

  @Column({ type: String, nullable: false })
  iadcType: string;

  @Column({ type: 'float', nullable: false })
  bitSize: number;

  @Column({ type: 'float', nullable: false })
  depth: number;

  @Column({ type: 'float', nullable: false })
  bitRunDuration: number;
}
