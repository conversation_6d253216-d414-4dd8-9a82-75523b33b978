import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IsExist } from '../../utils/validators/is-exists.validator';
import { IsNotExist } from '../../utils/validators/is-not-exists.validator';
import { IntervalService } from './interval.service';
import { Interval } from './entities/interval.entity';
import { JwtModule } from '@nestjs/jwt';
import { IntervalRepository } from './interval.repository';
import { IntervalController } from './interval.controller';

@Module({
  imports: [TypeOrmModule.forFeature([Interval]), JwtModule.register({})],
  controllers: [IntervalController],
  providers: [IsExist, IsNotExist, IntervalService, IntervalRepository],
  exports: [IntervalService],
})
export class IntervalModule {}
