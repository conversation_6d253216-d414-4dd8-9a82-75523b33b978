import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, IsUUID } from "class-validator";
import { IsUuidArray } from '../../../../common/decorators/class-validator/is-array-uuid.decorator';

export class CreateDailyReportDto {
  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  wellId: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  @IsOptional()
  wellInformationId?: string;

  @ApiProperty()
  @IsString()
  @IsUuidArray()
  @IsOptional()
  casedHoleIds?: string[];

  @ApiProperty()
  @IsString()
  @IsUuidArray()
  @IsOptional()
  openHoleIds?: string[];

  @ApiProperty()
  @IsString()
  @IsUuidArray()
  @IsOptional()
  drillStringIds?: string[];

  @ApiProperty()
  @IsString()
  @IsUuidArray()
  @IsOptional()
  drillBitIds?: string[];

  @ApiProperty()
  @IsString()
  @IsUuidArray()
  @IsOptional()
  nozzleIds?: string[];

  @ApiProperty()
  @IsString()
  @IsUuidArray()
  @IsOptional()
  sampleIds?: string[];

  @ApiProperty()
  @IsString()
  @IsUUID()
  @IsOptional()
  solidId?: string;

  @ApiProperty()
  @IsString()
  @IsUUID()
  @IsOptional()
  siteEquipmentId?: string;

  @ApiProperty()
  @IsString()
  @IsUuidArray()
  @IsOptional()
  taskIds?: string[];

  @ApiProperty()
  @IsString()
  @IsUUID()
  @IsOptional()
  productAndPackageId?: string;

  @ApiProperty()
  @IsString()
  @IsUUID()
  @IsOptional()
  volumeTrackingId?: string;

  @ApiProperty()
  @IsString()
  @IsUuidArray()
  @IsOptional()
  costIds?: string[];

  @ApiProperty()
  @IsString()
  @IsUuidArray()
  @IsOptional()
  noteIds?: string[];
}
