import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsN<PERSON>ber, IsString, IsUUID } from 'class-validator';

export class UpdateProductAndPackageInventoryDto {
  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  dailyReportId: string;

  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsNumber()
  totalCost: number;

  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsNumber()
  totalProductVolume: number;

  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsNumber()
  weightMaterials: number;

  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsNumber()
  baseFluid: number;

  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsNumber()
  addWater: number;

  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsNumber()
  totalVolume: number;
}
