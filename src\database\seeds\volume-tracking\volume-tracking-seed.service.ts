import { Injectable } from '@nestjs/common';
import { faker } from '@faker-js/faker';
import { VolumeTrackingStatusEnum } from '../../../modules/volum-trackings/enums/volume-tracking-status.enum';
import { Repository } from 'typeorm';
import { DailyReport } from '../../../modules/daily-reports/entities/daily-report.entity';
import { VolumeTracking } from '../../../modules/volum-trackings/entities/volume-tracking.entity';
import { InjectRepository } from '@nestjs/typeorm';

@Injectable()
export class VolumeTrackingSeedService {
  constructor(
    @InjectRepository(DailyReport)
    private dailyReportService: Repository<DailyReport>,
    @InjectRepository(VolumeTracking)
    private volumeTrackingService: Repository<VolumeTracking>,
  ) {}

  async run(): Promise<void> {
    await this.fakeVolumeTrackings();
  }

  private async fakeVolumeTrackings(): Promise<void> {
    const reports = await this.dailyReportService.find({ where: {} });
    for (let i = 0; i < reports.length; i++) {
      await this.volumeTrackingService.save(
        this.volumeTrackingService.create({
          dailyReportId: reports[i]?.id,
          name: faker.commerce.productName(),
          storageType: faker.commerce.product(),
          measuredVolume: faker.number.float({ max: 200, precision: 0.01 }),
          status: VolumeTrackingStatusEnum.active,
          mudType: faker.number.float({ max: 200, precision: 0.01 }),
          mudWeight: faker.number.float({ max: 200, precision: 0.01 }),
          totalAdditions: 100,
          totalLosses: 20,
          totalTransfers: 30,
          calculatedVolume: 50,
        }),
      );
    }
  }
}
