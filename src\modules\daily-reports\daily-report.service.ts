import { Injectable } from "@nestjs/common";

import { SelectQueryBuilder } from "typeorm";

import { PaginationResponseDto } from "../../common/dtos/pagination.response.dto";

import { PaginationDto } from "../../common/dtos/paginationDto";
import { DailyReportRepository } from "./daily-report.repository";
import { GetDailyReportsQueryDto } from "./dtos/requests/get-daily-reports.dto";
import { DailyReport } from "./entities/daily-report.entity";
import { CreateDailyReportDto } from "./dtos/requests/create-daily-report.dto";
import { UpdateDailyReportDto } from "./dtos/requests/update-daily-report.dto";
import { User } from "../users/entities/user.entity";
import { CasedHole } from "../cased-holes/entities/cased-hole.entity";
import { OpenHole } from "../open-holes/entities/open-hole.entity";
import { setTimeOfDate } from "../../utils/datetime-helper";
import { VolumeTrackingRepository } from "../volum-trackings/volume-tracking.repository";
import { VolumeTrackingStatusEnum } from "../volum-trackings/enums/volume-tracking-status.enum";
import { HttpBadRequestError } from "../../errors/bad-request.error";
import { ErrorCode } from "../../errors/error-code";
import { GetPreviousReportsQueryDto } from "./dtos/requests/get-previous-reports.dto";

@Injectable()
export class DailyReportService {
  constructor(
    private repository: DailyReportRepository,
    private volumeTrackingRepository: VolumeTrackingRepository
  ) {
  }

  async findAll(
    user: User,
    query: GetDailyReportsQueryDto,
    paginationQuery: PaginationDto
  ): Promise<PaginationResponseDto<DailyReport>> {
    let queryBuilder: SelectQueryBuilder<DailyReport> = this.repository
      .createQueryBuilder("dailyReport")
      .withDeleted()
      .where('dailyReport.deletedAt IS NULL')
      .andWhere("dailyReport.companyId = :companyId", { companyId: user.companyId })
      .andWhere("dailyReport.wellId = :wellId", { wellId: query.wellId })
      .leftJoinAndSelect("dailyReport.well", "well")
      .leftJoinAndSelect("dailyReport.wellInformation", "wellInformation")
      .leftJoinAndSelect("dailyReport.createdBy", "createdBy")
      .leftJoinAndSelect("dailyReport.updatedBy", "updatedBy")
      .orderBy("dailyReport.createdAt", "DESC");
    if (query.fromDate || query.toDate) {
      const fromDate = setTimeOfDate(new Date((query.fromDate ?? query.toDate)!), 0, 0);
      const toDate = setTimeOfDate(new Date((query.toDate ?? query.fromDate)!), 23, 59, 59);
      queryBuilder = queryBuilder.andWhere("dailyReport.createdAt BETWEEN :fromDate AND :toDate", {
        fromDate: fromDate,
        toDate: toDate
      });
      // queryBuilder = queryBuilder
      //   .andWhere('dailyReport.reportDate >= :fromDate', { fromDate: query.fromDate })
      //   .andWhere('dailyReport.reportDate <= :toDate', { toDate: query.toDate });
    }
    return this.repository.paginate(queryBuilder, paginationQuery);
  }

  async create(user: User, data: CreateDailyReportDto): Promise<DailyReport> {
    const createData = {
      well: { id: data.wellId },
      companyId: user.companyId!,
      casedHoles: data.casedHoleIds?.map(id => ({ id }) as CasedHole),
      openHoles: data.openHoleIds?.map(id => ({ id }) as OpenHole),
      createdBy: user,
      updatedBy: user,
      // reportDate: moment().format("yyyy-MM-DD")
    };
    if (data.wellInformationId) {
      createData["wellInformation"] = { id: data.wellInformationId };
    }
    return this.repository.save(this.repository.create(createData));
  }

  async detail(id: string): Promise<DailyReport | null> {
    return this.repository.createQueryBuilder('report')
      .where('report.id = :id', {id})
      .leftJoinAndSelect('report.wellInformation','wellInformation')
      .leftJoinAndSelect('wellInformation.dailyReport','dailyReport')
      .leftJoinAndSelect('report.notes','notes')
      .leftJoinAndSelect('report.createdBy','createdBy')
      .leftJoinAndSelect('report.updatedBy','updatedBy')
      .leftJoinAndSelect('wellInformation.engineer','engineer')
      .getOne();
  }

  async update(id: string, data: UpdateDailyReportDto): Promise<boolean> {
    const createData = {
      casedHoles: data.casedHoleIds?.map(id => ({ id }) as CasedHole),
      openHoles: data.openHoleIds?.map(id => ({ id }) as OpenHole)
    };
    if (data.wellInformationId) {
      createData['wellInformation'] = { id: data.wellInformationId };
    }
    await this.repository.update({ id }, createData);
    return true;
  }

  async findTodayReport(user: User, wellId: string): Promise<DailyReport> {
    const report = await this.repository.findTodayReport(user, wellId);
    const volumeTracking = await this.volumeTrackingRepository.find({
      where: { dailyReportId: report.id }
    });
    if (!volumeTracking.length) {
      await this.volumeTrackingRepository.save(
        this.volumeTrackingRepository.create({
          dailyReportId: report.id,
          name: "Active Pit",
          status: VolumeTrackingStatusEnum.active
        })
      );
      await this.volumeTrackingRepository.save(
        this.volumeTrackingRepository.create({
          dailyReportId: report.id,
          name: "Site",
          storageType: "On-site",
          status: VolumeTrackingStatusEnum.active
        })
      );
    }
    return report;
  }

  async getPreviousReport(query: GetPreviousReportsQueryDto): Promise<DailyReport | null> {
    // Fetch the current report
    const currentReport = await this.repository.findOne({
      where: { id: query.currentReportId },
    });

    if (!currentReport) {
      throw new HttpBadRequestError(ErrorCode.CURRENT_REPORT_NOT_FOUND);
    }

    // Find the previous report using reportDate or createdAt
    return await this.repository
      .createQueryBuilder('dailyReport')
      .leftJoinAndSelect('dailyReport.wellInformation', 'wellInformation')
      .leftJoinAndSelect('dailyReport.casedHoles', 'casedHoles')
      .leftJoinAndSelect('dailyReport.openHoles', 'openHoles')
      .leftJoinAndSelect('dailyReport.notes', 'notes')
      .leftJoinAndSelect('dailyReport.samples', 'samples')
      .where('dailyReport.wellId = :wellId', { wellId: query.wellId })
      .andWhere('dailyReport.createdAt < :createdAt', { createdAt: currentReport.createdAt })
      .orderBy('dailyReport.createdAt', 'DESC') // Get the latest previous report
      .getOne();
  }

  async findLatestReport(user: User, wellId: string): Promise<DailyReport|null> {
    return this.repository
      .createQueryBuilder("dailyReport")
      // .leftJoinAndSelect('dailyReport.company', 'company')
      .leftJoinAndSelect('dailyReport.wellInformation', 'wellInformation')
      .leftJoinAndSelect('dailyReport.casedHoles', 'casedHoles')
      .leftJoinAndSelect('dailyReport.openHoles', 'openHoles')
      .leftJoinAndSelect('dailyReport.notes', 'notes')
      .leftJoinAndSelect('dailyReport.samples', 'samples')
      .where(`"dailyReport".id = (
          SELECT r.id FROM daily_report r
          WHERE r."wellId" = :wellId
          ORDER BY r."createdAt" DESC
          LIMIT 1
        )`, { wellId }).getOne();
  }

  async softDeleteById(id: string): Promise<boolean> {
    await this.repository.softDelete({ id });
    return true;
  }
}
