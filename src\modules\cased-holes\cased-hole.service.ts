import { Injectable } from '@nestjs/common';
import { SelectQueryBuilder } from 'typeorm';
import { PaginationDto } from '../../common/dtos/paginationDto';
import { PaginationResponseDto } from '../../common/dtos/pagination.response.dto';
import { CasedHoleRepository } from './cased-hole.repository';
import { GetCasedHoleDto } from './dtos/requests/get-cased-hole.dto';
import { CasedHole } from './entities/cased-hole.entity';
import { CreateCasedHoleDto } from './dtos/requests/create-cased-hole.dto';
import { UpdateCasedHoleDto } from './dtos/requests/update-cased-hole.dto';
import { DailyReport } from '../daily-reports/entities/daily-report.entity';
import { DailyReportRepository } from '../daily-reports/daily-report.repository';
import { User } from '../users/entities/user.entity';

@Injectable()
export class CasedHoleService {
  constructor(
    private casedHoleRepository: CasedHoleRepository,
    private reportRepository: DailyReportRepository,
  ) {}

  async findAll(
    query: GetCasedHoleDto,
    paginationQuery: PaginationDto,
  ): Promise<PaginationResponseDto<CasedHole>> {
    const queryBuilder: SelectQueryBuilder<CasedHole> = this.casedHoleRepository
      .createQueryBuilder('casedHole')
      .where('casedHole.dailyReportId = :dailyReportId', { dailyReportId: query.dailyReportId })
      .orderBy('casedHole.createdAt', 'DESC')
      .addOrderBy('casedHole.topDepth', 'DESC');
    return this.casedHoleRepository.paginate(queryBuilder, paginationQuery);
  }

  async create(user: User, data: CreateCasedHoleDto): Promise<CasedHole> {
    await this.reportRepository.updateReportUpdatedBy(user, data.dailyReportId!);
    const casedHole = await this.casedHoleRepository.create(data);
    casedHole.dailyReport = { id: data.dailyReportId } as DailyReport;
    return this.casedHoleRepository.save(casedHole);
  }

  async update(user: User, id: string, data: UpdateCasedHoleDto): Promise<boolean> {
    await this.reportRepository.updateReportUpdatedBy(user, data.dailyReportId!);
    const dailyReport = { id: data.dailyReportId } as DailyReport;
    const dataUpdate = { dailyReport, ...data };
    delete dataUpdate.dailyReportId;
    await this.casedHoleRepository.update({ id }, dataUpdate);
    return true;
  }

  async findOne(id: string): Promise<CasedHole | null> {
    return this.casedHoleRepository.findOne({ where: { id } });
  }

  async softDeleteById(user: User, id: string): Promise<boolean> {
    const item = await this.casedHoleRepository.findOne({
      where: { id },
      relations: ['dailyReport'],
    });
    if (item?.dailyReport?.id) {
      await this.reportRepository.updateReportUpdatedBy(user, item!.dailyReport!.id!);
    }
    await this.casedHoleRepository.softDelete({ id });
    return true;
  }
}
