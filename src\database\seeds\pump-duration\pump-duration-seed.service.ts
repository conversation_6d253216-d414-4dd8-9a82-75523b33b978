import { Injectable } from '@nestjs/common';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { Pump } from '../../../modules/pumps/entities/pump.entity';
import { PumpDuration } from '../../../modules/pump-durations/entities/pump-duration.entity';

@Injectable()
export class PumpDurationSeedService {
  constructor(
    @InjectRepository(Pump)
    private pumpRepository: Repository<Pump>,
    @InjectRepository(PumpDuration)
    private pumpDurationRepository: Repository<PumpDuration>,
  ) {}

  async run(): Promise<void> {
    await this.fakeDurations();
  }

  private async fakeDurations(): Promise<void> {
    const pumps = await this.pumpRepository.find({ where: {} });
    for (let i = 0; i < pumps.length; i++) {
      await this.pumpDurationRepository.save(
        this.pumpDurationRepository.create({
          pump: { id: pumps[i]?.id },
          durations: 50,
        }),
      );
    }
  }
}
