import { Exclude, Expose, Type } from 'class-transformer';
import { PumpDurationResponseDto } from '../../../pump-durations/dtos/responses/pump-duration.response.dto';

@Exclude()
export class PumpDetailResponseDto {
  @Expose()
  id: string;

  @Expose()
  description: string;

  @Expose()
  inUse: boolean;

  @Expose()
  model: string;

  @Expose()
  linearID: number;

  @Expose()
  rodOD: number;

  @Expose()
  strokeLength: number;

  @Expose()
  efficiency: number;

  @Expose()
  stroke: number;

  @Expose()
  displacement: number;

  @Expose()
  rate: number;

  @Expose()
  totalDurations: number;

  @Expose()
  @Type(() => PumpDurationResponseDto)
  durations: PumpDurationResponseDto[];
}
