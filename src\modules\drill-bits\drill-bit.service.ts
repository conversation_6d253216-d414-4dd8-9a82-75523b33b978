import { Injectable } from '@nestjs/common';
import { SelectQueryBuilder } from 'typeorm';
import { PaginationDto } from '../../common/dtos/paginationDto';
import { PaginationResponseDto } from '../../common/dtos/pagination.response.dto';

import { DrillBitRepository } from './drill-bit.repository';
import { GetDrillBitDto } from './dtos/requests/get-drill-bit.dto';
import { DrillBit } from './entities/drill-bit.entity';
import { CreateDrillBitDto } from './dtos/requests/create-drill-bit.dto';
import { UpdateDrillBitDto } from './dtos/requests/update-drill-bit.dto';
import { DailyReportRepository } from '../daily-reports/daily-report.repository';
import { User } from '../users/entities/user.entity';

@Injectable()
export class DrillBitService {
  constructor(
    private drillStringRepository: DrillBitRepository,
    private reportRepository: DailyReportRepository,
  ) {}

  async findAll(
    query: GetDrillBitDto,
    paginationQuery: PaginationDto,
  ): Promise<PaginationResponseDto<DrillBit>> {
    const queryBuilder: SelectQueryBuilder<DrillBit> = this.drillStringRepository
      .createQueryBuilder('drillBit')
      .where('drillBit.dailyReportId = :dailyReportId', { dailyReportId: query.dailyReportId })
      .orderBy('drillBit.createdAt', 'DESC')
      .addOrderBy('drillBit.depth', 'DESC');
    return this.drillStringRepository.paginate(queryBuilder, paginationQuery);
  }

  async create(user: User, data: CreateDrillBitDto): Promise<DrillBit> {
    await this.reportRepository.updateReportUpdatedBy(user, data.dailyReportId);
    return this.drillStringRepository.save(this.drillStringRepository.create(data));
  }

  async update(user: User, id: string, data: UpdateDrillBitDto): Promise<boolean> {
    await this.reportRepository.updateReportUpdatedBy(user, data.dailyReportId);
    await this.drillStringRepository.update({ id }, data);
    return true;
  }

  async findOne(id: string): Promise<DrillBit | null> {
    return this.drillStringRepository.findOne({ where: { id } });
  }

  async softDeleteById(user: User, id: string): Promise<boolean> {
    const item = await this.drillStringRepository
      .createQueryBuilder('drill')
      .where('drill.id = :id', { id })
      .getOne();
    if (item?.dailyReportId) {
      await this.reportRepository.updateReportUpdatedBy(user, item!.dailyReportId!);
    }
    await this.drillStringRepository.softDelete({ id });
    return true;
  }
}
