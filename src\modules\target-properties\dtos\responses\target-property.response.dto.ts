import { Exclude, Expose } from 'class-transformer';

@Exclude()
export class TargetPropertyResponseDto {
  @Expose()
  id: string;

  @Expose()
  fluidType: number;

  @Expose()
  mudWeight: number;

  @Expose()
  funnelViscosity: number;

  @Expose()
  plasticViscosity: number;

  @Expose()
  yieldPoint: number;

  @Expose()
  apiFiltrate: number;

  @Expose()
  apiCakeThickness: number;

  @Expose()
  pH: number;

  @Expose()
  mudAlkalinity: number;

  @Expose()
  filtrateAlkalinity: number;

  @Expose()
  chlorides: number;

  @Expose()
  totalHardness: number;

  @Expose()
  linearGelStrengthPercent: number;

  @Expose()
  rpm: number;
}
