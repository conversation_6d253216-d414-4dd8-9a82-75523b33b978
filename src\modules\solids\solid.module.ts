import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IsExist } from '../../utils/validators/is-exists.validator';
import { IsNotExist } from '../../utils/validators/is-not-exists.validator';
import { SolidService } from './solid.service';
import { JwtModule } from '@nestjs/jwt';
import { SolidRepository } from './solid.repository';
import { SolidController } from './solid.controller';
import { Solid } from './entities/solid.entity';
import { DailyReportRepository } from '../daily-reports/daily-report.repository';

@Module({
  imports: [TypeOrmModule.forFeature([Solid]), JwtModule.register({})],
  controllers: [SolidController],
  providers: [IsExist, IsNotExist, SolidService, SolidRepository, DailyReportRepository],
  exports: [SolidService],
})
export class SolidModule {}
