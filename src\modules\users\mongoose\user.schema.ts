import { <PERSON><PERSON>, <PERSON>hem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';
import { UserStatus } from './userStatus.enum';

@Schema()
export class User extends Document {
  @Prop({ required: true, unique: true })
  email: string;

  @Prop()
  firstName: string;

  @Prop()
  lastName: string;

  @Prop({ required: true })
  password: string;

  @Prop()
  officePhone: string;

  @Prop()
  mobilePhone: string;

  @Prop()
  address: string;

  @Prop()
  note: string;

  @Prop()
  status: UserStatus;

  @Prop()
  confirmCode: string;

  @Prop()
  expiredConfirmCode: string;

  @Prop({ type: Date })
  assignedDate: Date;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'Company' })
  company: string[];

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'User' })
  supervisor: string[];

  @Prop({ type: [{ type: MongooseSchema.Types.ObjectId, ref: 'Role' }] })
  roles: string[];
}

export const UserSchema = SchemaFactory.createForClass(User);
