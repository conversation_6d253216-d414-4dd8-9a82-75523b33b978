import { Injectable } from '@nestjs/common';
import { FluidTypeEnum } from '../../../modules/samples/enums/fluid-type.enum';
import { faker } from '@faker-js/faker';
import { Repository } from 'typeorm';
import { DailyReport } from '../../../modules/daily-reports/entities/daily-report.entity';
import { Sample } from '../../../modules/samples/entities/sample.entity';
import { InjectRepository } from '@nestjs/typeorm';
import moment from 'moment';

@Injectable()
export class SampleSeedService {
  constructor(
    @InjectRepository(DailyReport)
    private dailyReportRepository: Repository<DailyReport>,
    @InjectRepository(Sample)
    private sampleRepository: Repository<Sample>,
  ) {}

  async run(): Promise<void> {
    await this.fakeSamples();
  }

  private async fakeSamples(): Promise<void> {
    const reports = await this.dailyReportRepository.find({ where: {} });
    for (let i = 0; i < reports.length; i++) {
      for (let j = 0; j < 20; j++) {
        await this.sampleRepository.save(
          this.sampleRepository.create({
            dailyReport: { id: reports[i]?.id },
            fluidType: FluidTypeEnum.oil,
            weightedMud: true,
            sampleFrom: faker.location.state(),
            timeSampleTaken: moment(faker.date.anytime()).format('HH:mm:ss'),
            flowlineTemperature: faker.number.float({ max: 200, precision: 0.01 }),
            measuredDepth: faker.number.float({ max: 200, precision: 0.01 }),
            mudWeight: faker.number.float({ max: 200, precision: 0.01 }),
            funnelViscosity: faker.number.float({ max: 200, precision: 0.01 }),
            temperatureForPlasticViscosity: faker.number.float({ max: 200, precision: 0.01 }),
            plasticViscosity: faker.number.float({ max: 200, precision: 0.01 }),
            yieldPoint: faker.number.float({ max: 200, precision: 0.01 }),
            gelStrength10s: faker.number.float({ max: 200, precision: 0.01 }),
            gelStrength10m: faker.number.float({ max: 200, precision: 0.01 }),
            gelStrength30m: faker.number.float({ max: 200, precision: 0.01 }),
            apiFiltrate: faker.number.float({ max: 200, precision: 0.01 }),
            apiCakeThickness: faker.number.float({ max: 200, precision: 0.01 }),
            temperatureForHTHP: faker.number.float({ max: 200, precision: 0.01 }),
            hthpFiltrate: faker.number.float({ max: 200, precision: 0.01 }),
            hthpCakeThickness: faker.number.float({ max: 200, precision: 0.01 }),
            solids: faker.number.float({ max: 200, precision: 0.01 }),
            oil: faker.number.float({ max: 200, precision: 0.01 }),
            water: faker.number.float({ max: 200, precision: 0.01 }),
            sandContent: faker.number.float({ max: 200, precision: 0.01 }),
            mbtCapacity: faker.number.float({ max: 200, precision: 0.01 }),
            pH: faker.number.float({ max: 200, precision: 0.01 }),
            mudAlkalinity: faker.number.float({ max: 200, precision: 0.01 }),
            filtrateAlkalinity: faker.number.float({ max: 200, precision: 0.01 }),
            calcium: faker.number.float({ max: 200, precision: 0.01 }),
            chlorides: faker.number.float({ max: 200, precision: 0.01 }),
            totalHardness: faker.number.float({ max: 200, precision: 0.01 }),
            excessLime: faker.number.float({ max: 200, precision: 0.01 }),
            kPlus: faker.number.float({ max: 200, precision: 0.01 }),
            makeUpWater: faker.number.float({ max: 200, precision: 0.01 }),
            solidsAdjustedForSalt: faker.number.float({ max: 200, precision: 0.01 }),
            fineLCM: faker.number.float({ max: 200, precision: 0.01 }),
            coarseLCM: faker.number.float({ max: 200, precision: 0.01 }),
            linearGelStrengthPercent: faker.number.float({ max: 200, precision: 0.01 }),
            linearGelStrengthLbBbl: faker.number.float({ max: 200, precision: 0.01 }),
            highGelStrengthPercent: faker.number.float({ max: 200, precision: 0.01 }),
            highGelStrengthLbBbl: faker.number.float({ max: 200, precision: 0.01 }),
            bentoniteConcentrationPercent: faker.number.float({ max: 200, precision: 0.01 }),
            bentoniteConcentrationLbBbl: faker.number.float({ max: 200, precision: 0.01 }),
            drillSolidsConcentrationPercent: faker.number.float({ max: 200, precision: 0.01 }),
            drillSolidsConcentrationLbBbl: faker.number.float({ max: 200, precision: 0.01 }),
            drillSolidsToBentoniteRatio: faker.number.float({ max: 200, precision: 0.01 }),
            averageSpecificGravityOfSolids: faker.number.float({ max: 200, precision: 0.01 }),
            shearRate600: faker.number.float({ max: 200, precision: 0.01 }),
            shearRate300: faker.number.float({ max: 200, precision: 0.01 }),
            shearRate200: faker.number.float({ max: 200, precision: 0.01 }),
            shearRate100: faker.number.float({ max: 200, precision: 0.01 }),
            shearRate6: faker.number.float({ max: 200, precision: 0.01 }),
            shearRate3: faker.number.float({ max: 200, precision: 0.01 }),
            apparentViscosity: faker.number.float({ max: 200, precision: 0.01 }),
            shearRate: faker.number.float({ max: 200, precision: 0.01 }),
            shearStress: faker.number.float({ max: 200, precision: 0.01 }),
          }),
        );
      }
    }
  }
}
