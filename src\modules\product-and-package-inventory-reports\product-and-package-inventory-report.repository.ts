import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { BaseRepository } from '../../common/bases/base.repository';
import { ProductAndPackageInventoryReport } from './entities/product-and-package-inventory-report.entity';

@Injectable()
export class ProductAndPackageInventoryReportRepository extends BaseRepository<ProductAndPackageInventoryReport> {
  constructor(dataSource: DataSource) {
    super(ProductAndPackageInventoryReport, dataSource);
  }

  async getTotalCost(productAndPackageInventoryId: string): Promise<number> {
    const items = await this.createQueryBuilder('report')
      .where('report.productAndPackageInventoryId = :productAndPackageInventoryId', {
        productAndPackageInventoryId,
      })
      .getMany();
    if (!items.length) {
      return 0;
    }
    return items.map(e => e.totalCost).reduce((a, b) => a + b);
  }
}
