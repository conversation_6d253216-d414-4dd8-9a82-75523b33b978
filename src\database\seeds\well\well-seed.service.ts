import { Injectable } from '@nestjs/common';
import { faker } from '@faker-js/faker';
import { UserRole } from '../../../modules/roles/enums/roles.enum';
import { Repository } from 'typeorm';
import { Well } from '../../../modules/wells/entities/well.entity';
import { Company } from '../../../modules/companies/entities/company.entity';
import { User } from '../../../modules/users/entities/user.entity';
import { Customer } from '../../../modules/customers/entities/customer.entity';
import { State } from '../../../modules/states/entities/state.entity';
import { Country } from '../../../modules/countries/entities/country.entity';
import { InjectRepository } from '@nestjs/typeorm';

@Injectable()
export class WellSeedService {
  constructor(
    @InjectRepository(Well)
    private wellService: Repository<Well>,
    @InjectRepository(Company)
    private companyService: Repository<Company>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(Customer)
    private customerService: Repository<Customer>,
    @InjectRepository(State)
    private stateService: Repository<State>,
    @InjectRepository(Country)
    private countryService: Repository<Country>,
  ) {}

  async run(): Promise<void> {
    await this.fakeWells();
  }

  private async fakeWells(): Promise<void> {
    const company = await this.companyService.findOne({ where: {} });
    const engineers = await this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.roles', 'role')
      .where('role.value IN (:...roles)', { roles: [UserRole.ENGINEER] })
      .getMany();
    const supervisors = await this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.roles', 'role')
      .where('role.value IN (:...roles)', { roles: [UserRole.SUPERVISOR] })
      .getMany();
    const customers = (await this.customerService.find({ where: {} })) ?? [];
    const state = await this.stateService.findOne({ where: {} });
    const country = await this.countryService.findOne({ where: {} });
    for (let i = 0; i < 10; i++) {
      const well = this.wellService.create({
        stateOrProvinceId: state?.id,
        countryId: country?.id,
        nameOrNo: faker.commerce.product(),
        apiWellNo: faker.string.alphanumeric(6),
        latitude: faker.location.latitude(),
        longitude: faker.location.longitude(),
        fieldOrBlock: faker.string.alphanumeric(6),
        sectionOrTownshipOrRange: faker.string.alphanumeric(6),
        countyOrParishOrOffshoreArea: faker.location.county(),
        rigName: faker.string.alphanumeric(6),
        spudDate: faker.date.anytime(),
        stockPoint: faker.location.county(),
        stockPointContact: faker.location.streetAddress(),
        operator: faker.company.name(),
        contractor: faker.company.name(),
        kickOffPoint: faker.number.float({ max: 200, precision: 0.01 }),
        landingPoint: faker.number.float({ max: 200, precision: 0.01 }),
        seaLevel: faker.number.float({ max: 200, precision: 0.01 }),
        airGap: faker.number.float({ max: 200, precision: 0.01 }),
        waterDepth: faker.number.float({ max: 200, precision: 0.01 }),
        riserId: faker.number.float({ max: 200, precision: 0.01 }),
        riserOD: faker.number.float({ max: 200, precision: 0.01 }),
        chokeLineId: faker.number.float({ max: 200, precision: 0.01 }),
        killLineId: faker.number.float({ max: 200, precision: 0.01 }),
        boostLineId: faker.number.float({ max: 200, precision: 0.01 }),
        rateOfPenetration: true,
        revolutionsPerMinute: true,
        eccentricity: true,
        archived: false,
        users: [],
        customers: [],
      });
      well.users?.push(engineers[0]);
      well.users?.push(engineers[1]);
      well.users?.push(engineers[2]);
      well.users?.push(supervisors[0]);
      well.users?.push(supervisors[1]);
      well.users?.push(supervisors[2]);
      well.customers?.push(customers[0]);
      well.customers?.push(customers[1]);
      company && (well.company = company);
      await this.wellService.save(well);
    }
  }
}
