import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsEmail, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { Transform } from 'class-transformer';
import { lowerCaseTransformer } from '../../../../common/transformers/lower-case.transformer';

export class UpdateCustomerContactDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  name?: string;

  @ApiProperty()
  @Transform(lowerCaseTransformer)
  @IsNotEmpty()
  @IsEmail()
  @IsOptional()
  emailAddress?: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  mobilePhone?: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  officePhone?: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  address?: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  notes?: string;

  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  primaryContact?: boolean;

  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  notifyOnNewReport?: boolean;
}
