import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';

export class UpdateSampleDto {
  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  dailyReportId?: string;

  @ApiProperty({
    required: true,
    description: 'water: 1, oil:2, synthetic: 3',
    default: 1,
  })
  @IsInt()
  fluidType: number;

  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  weightedMud?: boolean;

  @ApiProperty()
  @IsString()
  @IsOptional()
  sampleFrom?: string;

  @ApiProperty({ example: '10:30:40' })
  @IsString()
  @IsOptional()
  timeSampleTaken?: string;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  flowlineTemperature?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  measuredDepth?: number;

  @ApiProperty({ required: true })
  @IsNumber()
  mudWeight: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  funnelViscosity?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  temperatureForPlasticViscosity?: number;

  @ApiProperty({ required: true })
  @IsNumber()
  plasticViscosity: number;

  @ApiProperty({ required: true })
  @IsNumber()
  yieldPoint: number;

  @ApiProperty({ required: true })
  @IsNumber()
  gelStrength10s: number;

  @ApiProperty({ required: true })
  @IsNumber()
  gelStrength10m: number;

  @ApiProperty({ required: true })
  @IsNumber()
  gelStrength30m: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  apiFiltrate?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  apiCakeThickness?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  temperatureForHTHP?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  hthpFiltrate?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  hthpCakeThickness?: number;

  @ApiProperty({ required: true })
  @IsNumber()
  solids: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  oil?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  water?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  sandContent?: number;

  @ApiProperty({ required: true })
  @IsNumber()
  mbtCapacity: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  pH?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  mudAlkalinity?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  filtrateAlkalinity?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  calcium?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  chlorides?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  totalHardness?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  excessLime?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  kPlus?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  makeUpWater?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  solidsAdjustedForSalt?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  fineLCM?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  coarseLCM?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  linearGelStrengthPercent?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  linearGelStrengthLbBbl?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  highGelStrengthPercent?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  highGelStrengthLbBbl?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  bentoniteConcentrationPercent?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  bentoniteConcentrationLbBbl?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  drillSolidsConcentrationPercent?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  drillSolidsConcentrationLbBbl?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  drillSolidsToBentoniteRatio?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  averageSpecificGravityOfSolids?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  shearRate600?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  shearRate300?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  shearRate200?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  shearRate100?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  shearRate6?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  shearRate3?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  apparentViscosity?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  shearRate?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  shearStress?: number;
}
