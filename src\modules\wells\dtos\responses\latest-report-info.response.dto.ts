import { Exclude, Expose, Type } from 'class-transformer';
import { NoteInfoResponseDto } from './note-info.response.dto';
import { ReportWellInfoResponseDto } from './report-well-info.response.dto';
import { SampleResponseDto } from "../../../samples/dtos/responses/sample.response.dto";

@Exclude()
export class LatestReportInfoResponseDto {
  @Expose()
  id: string;

  @Expose()
  @Type(() => ReportWellInfoResponseDto)
  wellInformation?: ReportWellInfoResponseDto;

  @Expose()
  @Type(() => SampleResponseDto)
  samples?: SampleResponseDto[];

  @Expose()
  @Type(() => NoteInfoResponseDto)
  notes?: NoteInfoResponseDto[];
}
