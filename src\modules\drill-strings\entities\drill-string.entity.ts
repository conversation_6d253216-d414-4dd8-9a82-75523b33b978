import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm';
import { EntityHelper } from '../../../utils/entity-helper';
import { DailyReport } from '../../daily-reports/entities/daily-report.entity';

@Entity()
export class DrillString extends EntityHelper {
  @ManyToOne(() => DailyReport, { nullable: false })
  @JoinColumn({ name: 'dailyReportId' })
  @Column({ type: String, nullable: false })
  dailyReportId: string;

  @Column({ type: String, nullable: false })
  description: string;

  @Column({ type: 'float', nullable: false })
  outsideDiameter: number;

  @Column({ type: 'float', nullable: false })
  weight: number;

  @Column({ type: 'float', nullable: false })
  insideDiameter: number;

  @Column({ type: 'float', nullable: false })
  length: number;
}
