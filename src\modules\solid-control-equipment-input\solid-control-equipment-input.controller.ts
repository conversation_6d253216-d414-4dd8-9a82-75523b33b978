import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { AuthGuard } from '../auth/guards/auth.guard';
import { toDto } from '../../common/transformers/dto.transformer';

import { RolesDecorator } from '../auth/decorators/roles.decorator';
import { UserRole } from '../roles/enums/roles.enum';
import { RolesGuard } from '../auth/guards/roles.guard';
import { SolidControlEquipmentInputService } from './solid-control-equipment-input.service';
import { SolidControlEquipmentInputResponseDto } from './dtos/responses/solid-control-equipment-input.response.dto';
import { Responder } from '../../common/decorators/responder.decorator';
import { CreateSolidControlEquipmentInputDto } from './dtos/requests/create-solid-control-equipment-input.dto';
import { UpdateSolidControlEquipmentInputDto } from './dtos/requests/update-solid-control-equipment-input.dto';

@ApiTags('SolidControlEquipmentInput')
@Controller('solidControlEquipmentInputs')
@ApiBearerAuth('access-token')
@RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN, UserRole.SUPERVISOR, UserRole.ENGINEER)
@UseGuards(AuthGuard, RolesGuard)
export class SolidControlEquipmentInputController {
  constructor(private readonly service: SolidControlEquipmentInputService) {}

  @Post()
  @ApiOperation({ description: 'Create solid control equipment inputs' })
  @Responder.handle('Create solid control equipment inputs')
  @HttpCode(HttpStatus.CREATED)
  async create(
    @Body() data: CreateSolidControlEquipmentInputDto,
  ): Promise<SolidControlEquipmentInputResponseDto> {
    const value = await this.service.create(data);
    return toDto(SolidControlEquipmentInputResponseDto, value);
  }

  @Put('/:id')
  @ApiOperation({ description: 'Update solid control equipment input' })
  @Responder.handle('solid control equipment input')
  @HttpCode(HttpStatus.CREATED)
  async update(
    @Param('id') id: string,
    @Body() data: UpdateSolidControlEquipmentInputDto,
  ): Promise<boolean> {
    return this.service.update(id, data);
  }

  @Get('/:id')
  @ApiOperation({ description: 'Get solid control equipment input detail' })
  @Responder.handle('Get solid control equipment input detail')
  @HttpCode(HttpStatus.OK)
  async detail(@Param('id') id: string): Promise<SolidControlEquipmentInputResponseDto> {
    const data = await this.service.findOne(id);
    return toDto(SolidControlEquipmentInputResponseDto, data);
  }

  @Delete('/:id')
  @ApiOperation({ description: 'Delete solid control equipment input' })
  @Responder.handle('Delete solid control equipment input')
  @HttpCode(HttpStatus.OK)
  delete(@Param('id') id: string): Promise<boolean> {
    return this.service.softDeleteById(id);
  }
}
