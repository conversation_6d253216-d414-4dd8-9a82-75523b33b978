import { Module } from '@nestjs/common';
import { SampleSeedService } from './sample-seed.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DailyReport } from '../../../modules/daily-reports/entities/daily-report.entity';
import { Sample } from '../../../modules/samples/entities/sample.entity';

@Module({
  imports: [TypeOrmModule.forFeature([DailyReport, Sample])],
  providers: [SampleSeedService],
  exports: [SampleSeedService],
})
export class SampleSeedModule {}
