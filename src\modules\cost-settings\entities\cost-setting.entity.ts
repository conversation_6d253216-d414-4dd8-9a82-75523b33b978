import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm';
import { EntityHelper } from '../../../utils/entity-helper';
import { CostSettingTypeEnum } from '../enums/cost-setting-type.enum';
import { Company } from '../../companies/entities/company.entity';

@Entity()
export class CostSetting extends EntityHelper {
  @ManyToOne(() => Company)
  @JoinColumn({ name: 'companyId' })
  @Column({ type: String, nullable: false })
  companyId: string | null;

  @ManyToOne(() => Company)
  company: Company;

  @Column({ type: String, nullable: false })
  name: string;

  @Column({ type: String, nullable: false })
  description: string;

  @Column({ type: 'float', nullable: false })
  cost: number;

  @Column({ nullable: false })
  type: CostSettingTypeEnum;
}
