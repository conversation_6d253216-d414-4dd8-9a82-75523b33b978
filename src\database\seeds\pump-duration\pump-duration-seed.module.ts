import { Module } from '@nestjs/common';
import { PumpDurationSeedService } from './pump-duration-seed.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Pump } from '../../../modules/pumps/entities/pump.entity';
import { PumpDuration } from '../../../modules/pump-durations/entities/pump-duration.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Pump, PumpDuration])],
  providers: [PumpDurationSeedService],
  exports: [PumpDurationSeedService],
})
export class PumpDurationSeedModule {}
