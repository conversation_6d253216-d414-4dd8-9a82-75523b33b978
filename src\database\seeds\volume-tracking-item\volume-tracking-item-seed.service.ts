import { Injectable } from '@nestjs/common';
import { faker } from '@faker-js/faker';
import { Repository } from 'typeorm';
import { VolumeTracking } from '../../../modules/volum-trackings/entities/volume-tracking.entity';
import { VolumeTrackingItem } from '../../../modules/volume-tracking-items/entities/volume-tracking-item.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { VolumeTrackingItemTypeEnum } from '../../../modules/volume-tracking-items/enums/volume-tracking-item-type.enum';

@Injectable()
export class VolumeTrackingItemSeedService {
  constructor(
    @InjectRepository(VolumeTracking)
    private volumeTrackingService: Repository<VolumeTracking>,
    @InjectRepository(VolumeTrackingItem)
    private volumeTrackingItemService: Repository<VolumeTrackingItem>,
  ) {}

  async run(): Promise<void> {
    await this.fakeVolumeTrackingItems();
  }

  private async fakeVolumeTrackingItems(): Promise<void> {
    const volumes = await this.volumeTrackingService.find({ where: {} });
    for (let i = 0; i < volumes.length; i++) {
      await this.volumeTrackingItemService.save(
        this.volumeTrackingItemService.create({
          volumeTracking: { id: volumes[i]?.id },
          volume: 100,
          description: faker.lorem.text(),
          type: VolumeTrackingItemTypeEnum.addition,
        }),
      );
      await this.volumeTrackingItemService.save(
        this.volumeTrackingItemService.create({
          volumeTracking: { id: volumes[i]?.id },
          volume: 20,
          description: faker.lorem.text(),
          type: VolumeTrackingItemTypeEnum.loss,
        }),
      );
      await this.volumeTrackingItemService.save(
        this.volumeTrackingItemService.create({
          volumeTracking: { id: volumes[i]?.id },
          volume: 30,
          description: faker.lorem.text(),
          type: VolumeTrackingItemTypeEnum.transfer,
        }),
      );
    }
  }
}
