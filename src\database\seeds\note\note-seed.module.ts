import { Module } from '@nestjs/common';
import { NoteSeedService } from './note-seed.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DailyReport } from '../../../modules/daily-reports/entities/daily-report.entity';
import { Note } from '../../../modules/notes/entities/note.entity';

@Module({
  imports: [TypeOrmModule.forFeature([DailyReport, Note])],
  providers: [NoteSeedService],
  exports: [NoteSeedService],
})
export class NoteSeedModule {}
