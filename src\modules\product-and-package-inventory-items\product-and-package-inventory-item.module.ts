import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IsExist } from '../../utils/validators/is-exists.validator';
import { IsNotExist } from '../../utils/validators/is-not-exists.validator';
import { ProductAndPackageInventoryItemService } from './product-and-package-inventory-item.service';
import { JwtModule } from '@nestjs/jwt';
import { ProductAndPackageInventoryItemRepository } from './product-and-package-inventory-item.repository';
import { ProductAndPackageInventoryItemController } from './product-and-package-inventory-item.controller';
import { ProductAndPackageInventoryItem } from './entities/product-and-package-inventory-item.entity';
import { ProductAndPackageInventoryReportRepository } from '../product-and-package-inventory-reports/product-and-package-inventory-report.repository';
import { ProductAndPackageInventoryRepository } from '../product-and-package-inventorys/product-and-package-inventory.repository';
import { DailyReportRepository } from '../daily-reports/daily-report.repository';

@Module({
  imports: [TypeOrmModule.forFeature([ProductAndPackageInventoryItem]), JwtModule.register({})],
  controllers: [ProductAndPackageInventoryItemController],
  providers: [
    IsExist,
    IsNotExist,
    DailyReportRepository,
    ProductAndPackageInventoryItemService,
    ProductAndPackageInventoryItemRepository,
    ProductAndPackageInventoryReportRepository,
    ProductAndPackageInventoryRepository,
  ],
  exports: [ProductAndPackageInventoryItemService],
})
export class ProductAndPackageInventoryItemModule {}
