import { Column, Entity, <PERSON>inColumn, ManyToMany, ManyToOne } from 'typeorm';
import { EntityHelper } from '../../../utils/entity-helper';
import { Company } from '../../companies/entities/company.entity';
import { Well } from '../../wells/entities/well.entity';

@Entity()
export class Customer extends EntityHelper {
  @ManyToOne(() => Company, { nullable: true })
  @JoinColumn({ name: 'companyId' })
  @Column({ type: 'uuid', nullable: true })
  companyId: string;

  @ManyToOne(() => Company, company => company.customers)
  company: Company;

  @Column({ type: String, nullable: false })
  customerName: string;

  @Column({ type: String, nullable: true })
  notes: string;

  @ManyToMany(() => Well, well => well.customers)
  wells?: Well[];
}
