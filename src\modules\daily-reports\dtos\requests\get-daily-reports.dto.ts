import { ApiProperty } from '@nestjs/swagger';
import { IsDateString, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class GetDailyReportsQueryDto {
  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty()
  wellId: string;

  @ApiProperty({ required: false, example: '2024-01-14' })
  @IsDateString()
  @IsNotEmpty()
  @IsOptional()
  fromDate: string;

  @ApiProperty({ required: false, example: '2024-01-15' })
  @IsDateString()
  @IsNotEmpty()
  @IsOptional()
  toDate: string;
}
