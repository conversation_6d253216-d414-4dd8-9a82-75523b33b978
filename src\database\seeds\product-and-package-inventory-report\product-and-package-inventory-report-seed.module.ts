import { Module } from '@nestjs/common';
import { ProductAndPackageInventoryReportSeedService } from './product-and-package-inventory-report-seed.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ProductAndPackageInventoryReport } from '../../../modules/product-and-package-inventory-reports/entities/product-and-package-inventory-report.entity';
import { Product } from '../../../modules/products/entities/product.entity';
import { ProductAndPackageInventory } from '../../../modules/product-and-package-inventorys/entities/product-and-package-inventory.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Product,
      ProductAndPackageInventoryReport,
      ProductAndPackageInventory,
    ]),
  ],
  providers: [ProductAndPackageInventoryReportSeedService],
  exports: [ProductAndPackageInventoryReportSeedService],
})
export class ProductAndPackageInventoryReportSeedModule {}
