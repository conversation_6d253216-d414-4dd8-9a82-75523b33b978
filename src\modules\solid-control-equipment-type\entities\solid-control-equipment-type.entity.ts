import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm';
import { EntityHelper } from '../../../utils/entity-helper';
import { Company } from '../../companies/entities/company.entity';

@Entity()
export class SolidControlEquipmentType extends EntityHelper {
  @ManyToOne(() => Company, { nullable: false })
  @JoinColumn({ name: 'companyId' })
  @Column({ nullable: false })
  companyId: string;

  @Column({ nullable: false })
  name: string;
}
