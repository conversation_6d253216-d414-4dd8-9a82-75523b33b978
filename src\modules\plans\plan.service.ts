import { Injectable } from '@nestjs/common';

import { SelectQueryBuilder } from 'typeorm';

import { PaginationResponseDto } from '../../common/dtos/pagination.response.dto';

import { PaginationDto } from '../../common/dtos/paginationDto';
import { PlanRepository } from './plan.repository';
import { UpdatePlanDto } from './dtos/requests/update-plan.dto';
import { GetPlansQueryDto } from './dtos/requests/get-plans.dto';
import { Plan } from './entities/plan.entity';
import { CreatePlanDto } from './dtos/requests/create-plan.dto';

@Injectable()
export class PlanService {
  constructor(private repository: PlanRepository) {}

  async findAll(
    query: GetPlansQueryDto,
    paginationQuery: PaginationDto,
  ): Promise<PaginationResponseDto<Plan>> {
    const queryBuilder: SelectQueryBuilder<Plan> = this.repository
      .createQueryBuilder('interval')
      .where('interval.wellId = :wellId', { wellId: query.wellId })
      .orderBy('interval.createdAt', 'DESC');
    return this.repository.paginate(queryBuilder, paginationQuery);
  }

  async create(data: CreatePlanDto): Promise<Plan> {
    return this.repository.save(this.repository.create(data));
  }

  async update(id: string, data: UpdatePlanDto): Promise<boolean> {
    await this.repository.update({ id }, { ...data });
    return true;
  }

  async findOne(id: string): Promise<Plan | null> {
    return this.repository.findOne({ where: { id } });
  }

  async softDeleteById(id: string): Promise<boolean> {
    await this.repository.softDelete({ id });
    return true;
  }
}
