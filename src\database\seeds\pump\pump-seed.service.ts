import { Injectable } from '@nestjs/common';
import { faker } from '@faker-js/faker';
import { Repository } from 'typeorm';
import { DailyReport } from '../../../modules/daily-reports/entities/daily-report.entity';
import { Pump } from '../../../modules/pumps/entities/pump.entity';
import { InjectRepository } from '@nestjs/typeorm';

@Injectable()
export class PumpSeedService {
  constructor(
    @InjectRepository(DailyReport)
    private dailyReportRepository: Repository<DailyReport>,
    @InjectRepository(Pump)
    private pumpRepository: Repository<Pump>,
  ) {}

  async run(): Promise<void> {
    await this.fakePumps();
  }

  private async fakePumps(): Promise<void> {
    const reports = await this.dailyReportRepository.find({ where: {} });
    for (let i = 0; i < reports.length; i++) {
      await this.pumpRepository.save(
        this.pumpRepository.create({
          dailyReportId: reports[i]?.id,
          description: faker.lorem.text(),
          inUse: true,
          model: faker.string.alphanumeric(6),
          linearID: faker.number.float({ max: 200, precision: 0.01 }),
          rodOD: faker.number.float({ max: 200, precision: 0.01 }),
          strokeLength: faker.number.float({ max: 200, precision: 0.01 }),
          efficiency: faker.number.float({ max: 200, precision: 0.01 }),
          stroke: faker.number.float({ max: 200, precision: 0.01 }),
          displacement: faker.number.float({ max: 200, precision: 0.01 }),
          rate: faker.number.float({ max: 200, precision: 0.01 }),
          totalDurations: 50,
        }),
      );
    }
  }
}
