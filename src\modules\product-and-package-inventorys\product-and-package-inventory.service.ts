import { Injectable } from '@nestjs/common';
import { ProductAndPackageInventory } from './entities/product-and-package-inventory.entity';
import { ProductAndPackageInventoryRepository } from './product-and-package-inventory.repository';
import { DailyReport } from '../daily-reports/entities/daily-report.entity';
import { DailyReportRepository } from '../daily-reports/daily-report.repository';
import { User } from '../users/entities/user.entity';

@Injectable()
export class ProductAndPackageInventoryService {
  constructor(
    private dailyReportRepository: DailyReportRepository,
    private repository: ProductAndPackageInventoryRepository,
  ) {}

  async findOne(user: User, dailyReportId: string): Promise<ProductAndPackageInventory | null> {
    const productAndPackage = await this.repository
      .createQueryBuilder('productAndPackageInventory')
      .where('productAndPackageInventory.dailyReportId = :dailyReportId', { dailyReportId })
      .leftJoinAndSelect('productAndPackageInventory.reportItems', 'item')
      .leftJoinAndSelect('item.product', 'product', 'item.productId = product.id')
      .orderBy('item.createdAt', 'DESC')
      .getOne();
    if (productAndPackage) {
      return productAndPackage;
    }
    await this.dailyReportRepository.updateReportUpdatedBy(user, dailyReportId);
    return this.repository.save(
      this.repository.create({
        dailyReport: { id: dailyReportId } as DailyReport,
        totalCost: 0,
        totalProductVolume: 0,
        weightMaterials: 0,
        baseFluid: 0,
        addWater: 0,
        totalVolume: 0,
      }),
    );
  }

  async softDeleteById(user: User, id: string): Promise<boolean> {
    const item = await this.repository.findOne({
      where: { id },
      relations: ['dailyReport'],
    });
    if (item?.dailyReport?.id) {
      await this.dailyReportRepository.updateReportUpdatedBy(user, item!.dailyReport.id!);
    }
    await this.repository.softDelete({ id });
    return true;
  }
}
