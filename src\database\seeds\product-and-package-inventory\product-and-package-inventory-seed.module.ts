import { Module } from '@nestjs/common';
import { ProductAndPackageInventorySeedService } from './product-and-package-inventory-seed.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DailyReport } from '../../../modules/daily-reports/entities/daily-report.entity';
import { ProductAndPackageInventory } from '../../../modules/product-and-package-inventorys/entities/product-and-package-inventory.entity';

@Module({
  imports: [TypeOrmModule.forFeature([DailyReport, ProductAndPackageInventory])],
  providers: [ProductAndPackageInventorySeedService],
  exports: [ProductAndPackageInventorySeedService],
})
export class ProductAndPackageInventorySeedModule {}
