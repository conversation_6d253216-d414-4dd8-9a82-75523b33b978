import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm';
import { EntityHelper } from '../../../utils/entity-helper';
import { VolumeTracking } from '../../volum-trackings/entities/volume-tracking.entity';
import { VolumeTrackingItemTypeEnum } from '../enums/volume-tracking-item-type.enum';

@Entity()
export class VolumeTrackingItem extends EntityHelper {
  @ManyToOne(() => VolumeTracking, volumeTracking => volumeTracking.volumeItems, {
    nullable: false,
  })
  @JoinColumn({ name: 'volumeTrackingId' })
  volumeTracking: VolumeTracking;

  @Column({ type: 'float', nullable: false })
  volume: number;

  @Column({ type: String, nullable: false })
  description: string;

  @Column({ nullable: false })
  type: VolumeTrackingItemTypeEnum;
}
