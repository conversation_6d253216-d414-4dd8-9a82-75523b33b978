import { Module } from '@nestjs/common';
import { VolumeTrackingItemSeedService } from './volume-tracking-item-seed.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { VolumeTrackingItem } from '../../../modules/volume-tracking-items/entities/volume-tracking-item.entity';
import { VolumeTracking } from '../../../modules/volum-trackings/entities/volume-tracking.entity';

@Module({
  imports: [TypeOrmModule.forFeature([VolumeTrackingItem, VolumeTracking])],
  providers: [VolumeTrackingItemSeedService],
  exports: [VolumeTrackingItemSeedService],
})
export class VolumeTrackingItemSeedModule {}
