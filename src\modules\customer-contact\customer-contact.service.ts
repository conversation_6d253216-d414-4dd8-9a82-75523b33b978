import { Injectable } from '@nestjs/common';

import { SelectQueryBuilder } from 'typeorm';
import { CustomerContact } from './entities/customer-contact.entity';
import { CreateCustomerContactDto } from './dtos/requests/create-customer-contact.dto';
import { PaginationDto } from '../../common/dtos/paginationDto';
import { PaginationResponseDto } from '../../common/dtos/pagination.response.dto';
import { User } from '../users/entities/user.entity';
import { CustomerContactRepository } from './customer-contact.repository';
import { GetCustomerContactQueryDto } from './dtos/requests/get-customer-contact.dto';
import { UpdateCustomerContactDto } from './dtos/requests/update-customer-contact.dto';
import { DeleteCustomerContactsQueryDto } from "./dtos/requests/delete-customer-contacts.dto";
import { DeleteCustomerQueryDto } from "../customers/dtos/requests/delete-customer.dto";

@Injectable()
export class CustomerContactService {
  constructor(private repository: CustomerContactRepository) {}

  async findAll(
    query: GetCustomerContactQueryDto,
    paginationQuery: PaginationDto,
  ): Promise<PaginationResponseDto<CustomerContact>> {
    let queryBuilder: SelectQueryBuilder<CustomerContact> = this.repository
      .createQueryBuilder('contact')
      .where('contact.customerId = :customerId', { customerId: query.customerId });
    if (query.name) {
      queryBuilder = queryBuilder.andWhere('contact.name ILIKE :name', { name: `%${query.name}%` });
    }
    if (query.sortBy) {
      const sortDirection = query.sortDirection ?? 'ASC';
      queryBuilder = queryBuilder.orderBy(
        `contact.${query.sortBy}`,
        sortDirection === 'DESC' ? 'DESC' : 'ASC',
      );
    } else {
      queryBuilder = queryBuilder.orderBy('contact.createdAt', 'DESC');
    }
    return this.repository.paginate(queryBuilder, paginationQuery);
  }

  async create(user: User, data: CreateCustomerContactDto): Promise<CustomerContact> {
    const customer = this.repository.create(data);
    return this.repository.save(customer);
  }

  async update(id: string, user: User, data: UpdateCustomerContactDto): Promise<CustomerContact> {
    return await this.repository.save({ id, ...data });
  }

  async findOne(id: string): Promise<CustomerContact | null> {
    return this.repository.findOne({ where: { id } });
  }

  async softDeleteById(body: DeleteCustomerContactsQueryDto): Promise<boolean> {
    for (const id of body.ids) {
      await this.repository.softDelete({ id });
    }
    return true;
  }
}
