import { Module } from '@nestjs/common';
import { PumpSeedService } from './pump-seed.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DailyReport } from '../../../modules/daily-reports/entities/daily-report.entity';
import { Pump } from '../../../modules/pumps/entities/pump.entity';

@Module({
  imports: [TypeOrmModule.forFeature([DailyReport, Pump])],
  providers: [PumpSeedService],
  exports: [PumpSeedService],
})
export class PumpSeedModule {}
