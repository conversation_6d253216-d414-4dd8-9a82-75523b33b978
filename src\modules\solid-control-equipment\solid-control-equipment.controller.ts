import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { PaginationResponseDto } from '../../common/dtos/pagination.response.dto';
import { AuthGuard } from '../auth/guards/auth.guard';

import { PaginationDto } from '../../common/dtos/paginationDto';
import { toDto, toPaginateDtos } from '../../common/transformers/dto.transformer';

import { RolesDecorator } from '../auth/decorators/roles.decorator';
import { UserRole } from '../roles/enums/roles.enum';
import { RolesGuard } from '../auth/guards/roles.guard';
import { GetSolidControlEquipmentDto } from './dtos/requests/get-solid-control-equipments.dto';
import { SolidControlEquipmentService } from './solid-control-equipment.service';
import { Responder } from '../../common/decorators/responder.decorator';
import { SolidControlEquipmentResponseDto } from './dtos/responses/solid-control-equipment.response.dto';
import { CreateSolidControlEquipmentDto } from './dtos/requests/create-solid-control-equipment.dto';
import { UpdateSolidControlEquipmentDto } from './dtos/requests/update-solid-control-equipment.dto';
import { CurrentUser } from '../../common/decorators/current-user.decorator';
import { User } from '../users/entities/user.entity';

@ApiTags('SolidControlEquipment')
@Controller('solidControlEquipments')
@ApiBearerAuth('access-token')
@RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN, UserRole.SUPERVISOR, UserRole.ENGINEER)
@UseGuards(AuthGuard, RolesGuard)
export class SolidControlEquipmentController {
  constructor(private readonly service: SolidControlEquipmentService) {}

  @Get()
  @ApiOperation({ description: 'Get solid control equipments' })
  @Responder.handle('Get solid control equipments')
  @HttpCode(HttpStatus.OK)
  async findAll(
    @Query() query: GetSolidControlEquipmentDto,
    @Query() paginationQuery: PaginationDto,
  ): Promise<PaginationResponseDto<SolidControlEquipmentResponseDto>> {
    const data = await this.service.findAll(query, paginationQuery);
    return toPaginateDtos(SolidControlEquipmentResponseDto, data);
  }

  @Post()
  @ApiOperation({ description: 'Create solid control equipment' })
  @Responder.handle('Create solid control equipment')
  @HttpCode(HttpStatus.CREATED)
  async create(
    @CurrentUser() user: User,
    @Body() data: CreateSolidControlEquipmentDto,
  ): Promise<SolidControlEquipmentResponseDto> {
    const value = await this.service.create(user, data);
    return toDto(SolidControlEquipmentResponseDto, value);
  }

  @Put('/:id')
  @ApiOperation({ description: 'Update solid control equipment' })
  @Responder.handle('Update solid control equipment')
  @HttpCode(HttpStatus.CREATED)
  async update(
    @CurrentUser() user: User,
    @Param('id') id: string,
    @Body() data: UpdateSolidControlEquipmentDto,
  ): Promise<boolean> {
    return this.service.update(user, id, data);
  }

  @Get('/:id')
  @ApiOperation({ description: 'Get solid control equipment detail' })
  @Responder.handle('Get solid control equipment detail')
  @HttpCode(HttpStatus.OK)
  async detail(@Param('id') id: string): Promise<SolidControlEquipmentResponseDto> {
    const data = await this.service.findOne(id);
    return toDto(SolidControlEquipmentResponseDto, data);
  }

  @Delete('/:id')
  @ApiOperation({ description: 'Delete solid control equipment' })
  @Responder.handle('Delete solid control equipment')
  @HttpCode(HttpStatus.OK)
  delete(@CurrentUser() user: User, @Param('id') id: string): Promise<boolean> {
    return this.service.softDeleteById(user, id);
  }
}
