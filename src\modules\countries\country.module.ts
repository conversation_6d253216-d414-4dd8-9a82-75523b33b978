import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IsExist } from '../../utils/validators/is-exists.validator';
import { IsNotExist } from '../../utils/validators/is-not-exists.validator';
import { CountryService } from './country.service';
import { Country } from './entities/country.entity';
import { CountryRepository } from './country.repository';
import { CountryController } from './country.controller';
import { JwtModule } from '@nestjs/jwt';

@Module({
  imports: [TypeOrmModule.forFeature([Country]), JwtModule.register({})],
  controllers: [CountryController],
  providers: [IsExist, IsNotExist, CountryService, CountryRepository],
  exports: [CountryService],
})
export class CountryModule {}
