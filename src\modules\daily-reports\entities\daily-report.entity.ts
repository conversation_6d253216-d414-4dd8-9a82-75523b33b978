import { Column, <PERSON><PERSON>ty, <PERSON>inColumn, ManyToOne, OneToMany, OneToOne } from 'typeorm';
import { EntityHelper } from '../../../utils/entity-helper';
import { Well } from '../../wells/entities/well.entity';
import { Company } from '../../companies/entities/company.entity';
import { WellInformation } from '../../well-informations/entities/well-information.entity';
import { Note } from '../../notes/entities/note.entity';
import { CasedHole } from '../../cased-holes/entities/cased-hole.entity';
import { OpenHole } from '../../open-holes/entities/open-hole.entity';
import { User } from '../../users/entities/user.entity';
import { Sample } from "../../samples/entities/sample.entity";

@Entity()
export class DailyReport extends EntityHelper {
  @ManyToOne(() => Well, well => well.dailyReport)
  @JoinColumn({ name: 'wellId' })
  well: Well;

  @ManyToOne(() => Company)
  @JoinColumn({ name: 'companyId' })
  @Column({ nullable: false })
  companyId?: string | null;

  // @Column({ nullable: false })
  // reportDate: string;

  @OneToOne(() => WellInformation, wellInformation => wellInformation.dailyReport, {
    nullable: true,
  })
  wellInformation?: WellInformation;

  @OneToMany(() => CasedHole, casedHole => casedHole.dailyReport, {
    nullable: true,
  })
  casedHoles?: CasedHole[];

  @OneToMany(() => OpenHole, openHole => openHole.dailyReport, {
    nullable: true,
  })
  openHoles?: OpenHole[];

  @OneToMany(() => Note, note => note.dailyReport, {
    nullable: true,
  })
  notes?: Note[];

  @OneToMany(() => Sample, sample => sample.dailyReport, {
    nullable: true,
  })
  samples?: Sample[];

  @ManyToOne(() => User)
  @JoinColumn({ name: 'createdById' })
  createdBy: User;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'updatedById' })
  updatedBy: User;
}
