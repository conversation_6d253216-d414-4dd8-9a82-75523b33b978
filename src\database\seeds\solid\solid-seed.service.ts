import { Injectable } from '@nestjs/common';
import { faker } from '@faker-js/faker';
import { Repository } from 'typeorm';
import { DailyReport } from '../../../modules/daily-reports/entities/daily-report.entity';
import { Solid } from '../../../modules/solids/entities/solid.entity';
import { InjectRepository } from '@nestjs/typeorm';

@Injectable()
export class SolidSeedService {
  constructor(
    @InjectRepository(DailyReport)
    private dailyReportRepository: Repository<DailyReport>,
    @InjectRepository(Solid)
    private solidRepository: Repository<Solid>,
  ) {}

  async run(): Promise<void> {
    await this.fakeSolids();
  }

  private async fakeSolids(): Promise<void> {
    const reports = await this.dailyReportRepository.find({ where: {} });
    for (let i = 0; i < reports.length; i++) {
      await this.solidRepository.save(
        this.solidRepository.create({
          dailyReportId: reports[i]?.id,
          shaleCEC: faker.number.float({ max: 200, precision: 0.01 }),
          bentCEC: faker.number.float({ max: 200, precision: 0.01 }),
          highGelStrength: faker.number.float({ max: 200, precision: 0.01 }),
          linearGelStrength: faker.number.float({ max: 200, precision: 0.01 }),
        }),
      );
    }
  }
}
