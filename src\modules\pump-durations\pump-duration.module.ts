import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IsExist } from '../../utils/validators/is-exists.validator';
import { IsNotExist } from '../../utils/validators/is-not-exists.validator';
import { PumpDurationService } from './pump-duration.service';
import { JwtModule } from '@nestjs/jwt';
import { PumpDuration } from './entities/pump-duration.entity';
import { PumpDurationDurationController } from './pump-duration.controller';
import { PumpDurationRepository } from './pump-duration.repository';
import { PumpRepository } from '../pumps/pump.repository';

@Module({
  imports: [TypeOrmModule.forFeature([PumpDuration]), JwtModule.register({})],
  controllers: [PumpDurationDurationController],
  providers: [IsExist, IsNotExist, PumpDurationService, PumpDurationRepository, PumpRepository],
  exports: [PumpDurationService],
})
export class PumpDurationModule {}
