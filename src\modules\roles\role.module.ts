import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IsExist } from '../../utils/validators/is-exists.validator';
import { IsNotExist } from '../../utils/validators/is-not-exists.validator';
import { JwtModule } from '@nestjs/jwt';
import { RoleRepository } from './role.repository';
import { Role } from './entities/role.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Role]), JwtModule.register({})],
  controllers: [],
  providers: [IsExist, IsNotExist, RoleRepository],
  exports: [],
})
export class RoleModule {}
