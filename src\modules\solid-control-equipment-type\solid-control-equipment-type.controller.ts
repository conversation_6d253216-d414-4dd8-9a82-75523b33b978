import { Controller, Get, HttpCode, HttpStatus, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { AuthGuard } from '../auth/guards/auth.guard';
import { toDtos } from '../../common/transformers/dto.transformer';
import { RolesDecorator } from '../auth/decorators/roles.decorator';
import { UserRole } from '../roles/enums/roles.enum';
import { RolesGuard } from '../auth/guards/roles.guard';
import { SolidControlEquipmentTypeService } from './solid-control-equipment-type.service';
import { Responder } from '../../common/decorators/responder.decorator';
import { CurrentUser } from '../../common/decorators/current-user.decorator';
import { User } from '../users/entities/user.entity';
import { SolidControlEquipmentTypeResponseDto } from './dtos/responses/solid-control-equipment-type.response.dto';

@ApiTags('SolidControlEquipmentType')
@Controller('solidControlEquipmentTypes')
@ApiBearerAuth('access-token')
@RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN, UserRole.SUPERVISOR, UserRole.ENGINEER)
@UseGuards(AuthGuard, RolesGuard)
export class SolidControlEquipmentTypeController {
  constructor(private readonly service: SolidControlEquipmentTypeService) {}

  @Get()
  @ApiOperation({ description: 'Get screen types' })
  @Responder.handle('Get screen types')
  @HttpCode(HttpStatus.OK)
  async findAll(@CurrentUser() user: User): Promise<SolidControlEquipmentTypeResponseDto[]> {
    const data = await this.service.findAll(user);
    return toDtos(SolidControlEquipmentTypeResponseDto, data);
  }
}
