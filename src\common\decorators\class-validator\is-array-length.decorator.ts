import { registerDecorator, ValidationArguments, ValidationOptions } from 'class-validator';
import { isArray } from 'util';

export function IsArrayWithLength(property: string, validationOptions?: ValidationOptions) {
  return (object: any, propertyName: string) => {
    registerDecorator({
      name: 'IsArrayWithLength',
      target: object.constructor,
      propertyName,
      constraints: [property],
      options: Object.assign(
        {
          message: `${propertyName} must be have length equal ${property}`,
        },
        validationOptions,
      ),
      validator: {
        validate(value: any, args: ValidationArguments) {
          const [relatedPropertyName] = args.constraints;
          const relatedValue = (args.object as any)[relatedPropertyName];
          if (!isArray(value)) {
            return false;
          }
          const arrayValue = value as [];
          return typeof relatedValue === 'number' && arrayValue.length === relatedValue;
        },
      },
    });
  };
}
