// Target server hostname or IP address
const TARGET_SERVER_HOST = process.env.TARGET_SERVER_HOST ? process.env.TARGET_SERVER_HOST.trim() : '';
// Target server username
const TARGET_SERVER_USER = process.env.TARGET_SERVER_USER ? process.env.TARGET_SERVER_USER.trim() : '';
// Target server application path
const TARGET_SERVER_APP_PATH = `/var/www/html/**********************`;
// Your repository
const REPO = '**************:techsupport14/**********************.git';

const PRE_DEPLOY = 'git add . && git reset --hard && git checkout yarn.lock';

const POST_DEPLOY = {
  PROD:
    'ln -nfs ../shared/.env .env && \
        yarn install && \
        pm2 reload ecosystem.config.js --env production',
  STG:
    'ln -nfs ../shared/.env .env && \
        git pull && \
        npm install && \
        npm run build && \
        pm2 startOrGracefulReload ecosystem.config.js\
        && pm2 save',
};


module.exports = {
  apps: [
    {
      name: '**********************',
      script: 'dist/main.js',
      env: {
        NODE_ENV: 'staging',
      },
    },
  ],
  deploy: {
    stage: {
      user: TARGET_SERVER_USER,
      host: TARGET_SERVER_HOST,
      ref: 'staging',
      repo: REPO,
      ssh_options: 'StrictHostKeyChecking=no',
      path: TARGET_SERVER_APP_PATH,
      'pre-deploy': PRE_DEPLOY,
      'post-deploy': POST_DEPLOY.STG,
    },
  },
};
