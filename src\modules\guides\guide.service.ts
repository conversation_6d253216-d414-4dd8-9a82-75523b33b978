import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Guide } from './entities/guide.entity';
import { Repository } from 'typeorm';
import { EntityCondition } from '../../common/types/entity-condition.type';
import { NullableType } from '../../common/types/nullable.type';
import { CreateGuideDto } from './dtos/requests/create-guide.dto';

@Injectable()
export class GuideService {
  constructor(
    @InjectRepository(Guide)
    private customerRepository: Repository<Guide>,
  ) {}

  async create(data: CreateGuideDto): Promise<Guide> {
    return this.customerRepository.save(this.customerRepository.create(data));
  }

  findOne(
    fields: EntityCondition<Guide>,
    relations?: Record<string, boolean>,
  ): Promise<NullableType<Guide>> {
    const query: any = { where: fields };
    if (relations) {
      query.relations = relations;
    }
    return this.customerRepository.findOne({ ...query });
  }
}
