import { Module } from '@nestjs/common';
import { AuthModule } from './modules/auth/auth.module';
import databaseConfig from './database/config/database.config';
import appConfig from './config/app.config';
import authConfig from './modules/auth/config/auth.config';
import mailConfig from './config/mail.config';
import path from 'path';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { I18nModule } from 'nestjs-i18n/dist/i18n.module';
import { HeaderResolver } from 'nestjs-i18n';
import { TypeOrmConfigService } from './database/typeorm-config.service';
import { DataSource, DataSourceOptions } from 'typeorm';
import { AllConfigType } from './config/config.type';
import { WellModule } from './modules/wells/well.module';
import { CountryModule } from './modules/countries/country.module';
import { StateModule } from './modules/states/state.module';
import { IntervalModule } from './modules/intervals/interval.module';
import { PlanModule } from './modules/plans/plan.module';
import { ProductAndPackageInventoryModule } from './modules/product-and-package-inventorys/product-and-package-inventory.module';
import { VolumeTrackingModule } from './modules/volum-trackings/volume-tracking.module';
import { NoteModule } from './modules/notes/note.module';
import { CostSettingModule } from './modules/cost-settings/cost-setting.module';
import { CostModule } from './modules/costs/cost.module';
import { VolumeTrackingItemModule } from './modules/volume-tracking-items/volume-tracking-item.module';
import { WellInformationModule } from './modules/well-informations/well-information.module';
import { CasedHoleModule } from './modules/cased-holes/cased-hole.module';
import { OpenHoleModule } from './modules/open-holes/open-hole.module';
import { DrillStringModule } from './modules/drill-strings/drill-string.module';
import { DrillBitModule } from './modules/drill-bits/drill-bit.module';
import { NozzleModule } from './modules/nozzles/nozzle.module';
import { SampleModule } from './modules/samples/sample.module';
import { CustomerModule } from './modules/customers/customer.module';
import { UserModule } from './modules/users/user.module';
import { DailyReportModule } from './modules/daily-reports/daily-report.module';
import { ProductAndPackageInventoryItemModule } from './modules/product-and-package-inventory-items/product-and-package-inventory-item.module';
import { ProductModule } from './modules/products/product.module';
import { SolidModule } from './modules/solids/solid.module';
import { PumpModule } from './modules/pumps/pump.module';
import { PumpDurationModule } from './modules/pump-durations/pump-duration.module';
import { TaskModule } from './modules/tasks/task.module';
import { SolidControlEquipmentTypeModule } from './modules/solid-control-equipment-type/solid-control-equipment-type.module';
import { SolidControlEquipmentModule } from './modules/solid-control-equipment/solid-control-equipment.module';
import { SolidControlEquipmentTimeModule } from './modules/solid-control-equipment-time/solid-control-equipment-time.module';
import { SolidControlEquipmentInputModule } from './modules/solid-control-equipment-input/solid-control-equipment-input.module';
import { TargetPropertyModule } from './modules/target-properties/target-property.module';
import { ProductAndPackageInventoryReportModule } from './modules/product-and-package-inventory-reports/product-and-package-inventory-report.module';
import { MailModule } from './mail/mail.module';
import { ServeStaticModule } from '@nestjs/serve-static';
import { MulterModule } from '@nestjs/platform-express';
import { join } from 'path';
import { CompanyModule } from './modules/companies/company.module';
import { CustomerContactModule } from './modules/customer-contact/customer-contact.module';
import { APP_GUARD } from "@nestjs/core";
import { UserGuard } from "./modules/auth/guards/user.guard";
import { JwtModule } from "@nestjs/jwt";
import { MongodbConfigService } from './database/mongodb-config.service';
import { MongooseModule } from '@nestjs/mongoose';

@Module({
  imports: [
    JwtModule.register({}),
    ConfigModule.forRoot({
      isGlobal: true,
      load: [databaseConfig, appConfig, authConfig, mailConfig],
      envFilePath: ['.env'],
    }),
    MongooseModule.forRootAsync({
      useClass: MongodbConfigService,
    }),
    TypeOrmModule.forRootAsync({
      useClass: TypeOrmConfigService,
      dataSourceFactory: async (options: DataSourceOptions) => {
        return new DataSource(options).initialize();
      },
    }),
    I18nModule.forRootAsync({
      useFactory: (configService: ConfigService<AllConfigType>) => ({
        fallbackLanguage: configService.getOrThrow('app.fallbackLanguage', {
          infer: true,
        }),
        loaderOptions: { path: path.join(__dirname, '/i18n/'), watch: true },
      }),
      resolvers: [
        {
          use: HeaderResolver,
          useFactory: (configService: ConfigService<AllConfigType>) => {
            return [
              configService.get('app.headerLanguage', {
                infer: true,
              }),
            ];
          },
          inject: [ConfigService],
        },
      ],
      imports: [ConfigModule],
      inject: [ConfigService],
    }),
    ServeStaticModule.forRoot({
      rootPath: join(__dirname, '..', 'src', 'assets'),
      serveRoot: '/assets/',
    }),
    MulterModule.register({
      dest: './public/avatars',
    }),
    ServeStaticModule.forRoot({
      rootPath: join(__dirname, '..', 'public'),
    }),
    AuthModule,
    UserModule,
    CompanyModule,
    CustomerModule,
    CustomerContactModule,
    ProductModule,
    CostSettingModule,
    CountryModule,
    StateModule,
    WellModule,
    DailyReportModule,
    WellInformationModule,
    IntervalModule,
    PlanModule,
    ProductAndPackageInventoryModule,
    ProductAndPackageInventoryReportModule,
    ProductAndPackageInventoryItemModule,
    VolumeTrackingModule,
    VolumeTrackingItemModule,
    NoteModule,
    CostModule,
    CasedHoleModule,
    OpenHoleModule,
    DrillStringModule,
    DrillBitModule,
    NozzleModule,
    SampleModule,
    TargetPropertyModule,
    SolidModule,
    PumpModule,
    PumpDurationModule,
    TaskModule,
    SolidControlEquipmentModule,
    SolidControlEquipmentTypeModule,
    SolidControlEquipmentTimeModule,
    SolidControlEquipmentInputModule,
    MailModule,
  ],
  providers: [
    {
      provide: APP_GUARD,
      useClass: UserGuard,
    },
  ],
})
export class AppModule {}

