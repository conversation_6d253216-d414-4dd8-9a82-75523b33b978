import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  UploadedFile,
  UseGuards,
  UseInterceptors
} from "@nestjs/common";
import { ApiBearerAuth, ApiBody, ApiConsumes, ApiOperation, ApiTags } from "@nestjs/swagger";
import { PaginationResponseDto } from "../../common/dtos/pagination.response.dto";
import { AuthGuard } from "../auth/guards/auth.guard";

import { PaginationDto } from "../../common/dtos/paginationDto";
import { toDto, toPaginateDtos } from "../../common/transformers/dto.transformer";

import { RolesDecorator } from "../auth/decorators/roles.decorator";
import { RolesGuard } from "../auth/guards/roles.guard";
import { UserService } from "./user.service";
import { GetUserDto } from "./dtos/requests/get-user.dto";
import { UserResponseDto } from "./dtos/responses/user.response.dto";
import { UserRole } from "../roles/enums/roles.enum";
import { CurrentUser } from "../../common/decorators/current-user.decorator";
import { User } from "./entities/user.entity";
import { Responder } from "../../common/decorators/responder.decorator";
import { UserProfileResponseDto } from "./dtos/responses/user-profile.response.dto";
import { ChangePasswordRequestDto } from "./dtos/requests/change-password.request.dto";
import { ChangePasswordResponseDto } from "./dtos/responses/change-password.response.dto";
import { UpdateUserProfileDto } from "./dtos/requests/update-user-profile.dto";
import { ResetUserPasswordRequestDto } from "./dtos/requests/reset-password.request.dto";
import { ResetPasswordResponseDto } from "./dtos/responses/reset-password.response.dto";
import { UserChangePasswordRequestDto } from "./dtos/requests/user-change-password.request.dto";
import { CreateUserDto } from "./dtos/requests/create-user.dto";
import { FileInterceptor } from "@nestjs/platform-express";
import { diskStorage } from "multer";
import { extname } from "path";
import { UploadAvatarRequestDto } from "./dtos/requests/add-avatar.request.dto";
import { FileResponseDto } from "./dtos/responses/file.response.dto";
import { AssignEngineersDto } from "./dtos/requests/assign-engineers.request.dto";
import { GetAssignedEngineerDto } from "./dtos/requests/get-assigned-engineer.dto";
import { RemoveAssignedEngineersDto } from "./dtos/requests/remove-assigned-engineers.request.dto";
import { UpdateMyProfileDto } from "./dtos/requests/update-my-profile.dto";
import { UserProfileFullResponseDto } from "./dtos/responses/user-profile-full.response.dto";
import { DeleteUsersDto } from "./dtos/requests/delete-users.dto";
import { InviteUsersDto } from "./dtos/requests/invite-users.dto";
import { AuthService } from "../auth/auth.service";
import { AuthLoginResponseDto } from "../auth/dto/responses/auth-login.response.dto";
import { HttpBadRequestError } from "../../errors/bad-request.error";
import { ErrorCode } from "../../errors/error-code";
import { UserProfileAdditionInfoResponseDto } from "./dtos/responses/user-profile-addition-info.response.dto";

@ApiTags('User')
@Controller('users')
@ApiBearerAuth('access-token')
export class UserController {
  constructor(private readonly service: UserService, private readonly authService: AuthService) {}

  @Get()
  @ApiOperation({ description: 'Get users' })
  @Responder.handle('Get users')
  @HttpCode(HttpStatus.OK)
  @RolesDecorator(UserRole.ADMIN, UserRole.SUPERVISOR, UserRole.ENGINEER, UserRole.COMPANY_ADMIN)
  @UseGuards(AuthGuard, RolesGuard)
  async findAll(
    @CurrentUser() user: User,
    @Query() query: GetUserDto,
    @Query() paginationQuery: PaginationDto,
  ): Promise<PaginationResponseDto<UserResponseDto>> {
    const data = await this.service.findAll(user, query, paginationQuery);
    return toPaginateDtos(UserResponseDto, data);
  }

  @Get('/profile')
  @ApiOperation({ description: 'Get my profile' })
  @Responder.handle('Get my profile')
  @HttpCode(HttpStatus.OK)
  @RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN, UserRole.SUPERVISOR, UserRole.ENGINEER)
  @UseGuards(AuthGuard, RolesGuard)
  async findProfile(@CurrentUser() user: User): Promise<UserProfileAdditionInfoResponseDto> {
    const data = await this.service.findProfileFullInfo(user.id);
    return toDto(UserProfileAdditionInfoResponseDto, data);
  }

  @Put('/change-password')
  @ApiOperation({ description: 'User change password' })
  @Responder.handle('User change password')
  @HttpCode(HttpStatus.OK)
  @RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN, UserRole.SUPERVISOR, UserRole.ENGINEER)
  @UseGuards(AuthGuard, RolesGuard)
  async changePassword(
    @CurrentUser() user: User,
    @Body() changePasswordDto: ChangePasswordRequestDto,
  ): Promise<ChangePasswordResponseDto> {
    const message = await this.service.changePassword(user, changePasswordDto);
    return toDto(ChangePasswordResponseDto, message);
  }

  @Put('/profile/:id')
  @ApiOperation({ description: 'Admin Update profile for user' })
  @Responder.handle('Update profile')
  @HttpCode(HttpStatus.OK)
  @RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN, UserRole.SUPERVISOR)
  @UseGuards(AuthGuard, RolesGuard)
  async updateProfile(
    @Body() validateUpdateUserDto: UpdateUserProfileDto,
    @CurrentUser() user: User,
    @Param('id') id: string,
  ): Promise<UserProfileResponseDto> {
    const updatedUser = await this.service.updateProfile(user, id, validateUpdateUserDto);
    return toDto(UserProfileResponseDto, updatedUser);
  }

  @Get('/profile/:id')
  @ApiOperation({ description: 'Get user profile' })
  @Responder.handle('Get user profile')
  @HttpCode(HttpStatus.OK)
  @RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN, UserRole.SUPERVISOR, UserRole.ENGINEER)
  @UseGuards(AuthGuard, RolesGuard)
  async getProfile(@Param('id') id: string): Promise<UserProfileFullResponseDto> {
    const user = await this.service.findProfileFullInfo(id);
    return toDto(UserProfileFullResponseDto, user);
  }

  @Put('/profile')
  @ApiOperation({ description: 'Update my profile; Only admin and companyAdmin can update roles; admin: 1, supervisor: 2, engineer: 3, companyAdmin: 4' })
  @Responder.handle('Update my profile')
  @HttpCode(HttpStatus.OK)
  @RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN, UserRole.SUPERVISOR, UserRole.ENGINEER)
  @UseGuards(AuthGuard, RolesGuard)
  async updateMyProfile(
    @Body() validateUpdateUserDto: UpdateMyProfileDto,
    @CurrentUser() user: User,
  ): Promise<UserProfileResponseDto> {
    const response = await this.service.updateMyProfile(user, validateUpdateUserDto);
    return toDto(UserProfileResponseDto, response);
  }

  @Get('/engineers/assigned')
  @ApiOperation({ description: 'Get assigned engineers' })
  @Responder.handle('Get assigned engineers')
  @HttpCode(HttpStatus.OK)
  @RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN, UserRole.SUPERVISOR, UserRole.ENGINEER)
  @UseGuards(AuthGuard, RolesGuard)
  async getAssignedEngineers(
    @CurrentUser() user: User,
    @Query() query: GetAssignedEngineerDto,
    @Query() paginationQuery: PaginationDto,
  ): Promise<PaginationResponseDto<UserResponseDto>> {
    const data = await this.service.getAssignedEngineers(user, query, paginationQuery);
    return toPaginateDtos(UserResponseDto, data);
  }

  @Post('/engineers/assigned/add')
  @ApiOperation({ description: 'Assign engineers' })
  @Responder.handle('Assign engineers')
  @HttpCode(HttpStatus.OK)
  @RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN, UserRole.SUPERVISOR)
  @UseGuards(AuthGuard, RolesGuard)
  async assignEngineers(
    @Body() body: AssignEngineersDto,
    @CurrentUser() user: User,
  ): Promise<boolean> {
    return this.service.assignEngineers(body, user);
  }

  @Post('/engineers/assigned/remove')
  @ApiOperation({ description: 'Remove assigned engineers' })
  @Responder.handle('Remove assigned engineers')
  @HttpCode(HttpStatus.OK)
  @RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN, UserRole.SUPERVISOR)
  @UseGuards(AuthGuard, RolesGuard)
  async removeAssignedEngineers(
    @Body() body: RemoveAssignedEngineersDto,
    @CurrentUser() user: User,
  ): Promise<boolean> {
    return this.service.removeAssignedEngineers(body, user);
  }

  @Post('/reset-password')
  @ApiOperation({ description: 'Reset password' })
  @Responder.handle('Reset password')
  @HttpCode(HttpStatus.OK)
  @RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN, UserRole.SUPERVISOR, UserRole.ENGINEER)
  @UseGuards(AuthGuard, RolesGuard)
  async resetPassword(
    @Body() resetPassword: ResetUserPasswordRequestDto,
  ): Promise<ResetPasswordResponseDto> {
    const message = await this.service.resetPassword(resetPassword);
    return toDto(ResetPasswordResponseDto, message);
  }

  @Put('/user-reset-password/:token')
  @ApiOperation({ description: 'User change password' })
  @Responder.handle('User change password')
  @HttpCode(HttpStatus.OK)
  async userChangePassword(
    @Body() changePasswordDto: UserChangePasswordRequestDto,
    @Param('token') token: string,
  ): Promise<ChangePasswordResponseDto> {
    const response = await this.service.userResetPassword(token, changePasswordDto);
    let message = '';
    if (response) {
      message = 'Password changed successfully !';
    }
    return toDto(ChangePasswordResponseDto, message);
  }

  @Post('/add')
  @ApiOperation({ description: 'Add new user' })
  @Responder.handle('Add new user')
  @HttpCode(HttpStatus.OK)
  @RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN)
  @UseGuards(AuthGuard, RolesGuard)
  async addNewUser(@Body() body: CreateUserDto): Promise<UserProfileResponseDto> {
    const data = await this.service.createUser(body);
    return toDto(UserProfileResponseDto, data);
  }

  @Post('/upload-avatar')
  @ApiOperation({ description: 'Upload avatar user' })
  @Responder.handle('Upload avatar user')
  @HttpCode(HttpStatus.OK)
  @RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN, UserRole.SUPERVISOR, UserRole.ENGINEER)
  @UseGuards(AuthGuard, RolesGuard)
  @UseInterceptors(
    FileInterceptor('file', {
      storage: diskStorage({
        destination: './public/avatars', // Directory to save the uploaded files
        filename: (req, file, cb) => {
          const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9);
          const ext = extname(file.originalname);
          cb(null, `${file.fieldname}-${uniqueSuffix}${ext}`);
        },
      }),
      limits: { fileSize: 2 * 1024 * 1024 },
    }),
  )
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Upload avatar',
    type: UploadAvatarRequestDto,
  })
  async uploadAvatar(
    @UploadedFile() file: Express.Multer.File,
    @Body() body: UploadAvatarRequestDto,
  ): Promise<FileResponseDto> {
    const data = await this.service.uploadAvatar(body.userId, file.filename);
    return toDto(FileResponseDto, {
      filename: file.fieldname,
      originalName: file.originalname,
      mimeType: file.mimetype,
      size: file.size,
      uploadedBy: data.id,
      uploadedAt: data.uploadedAt,
      url: data.url,
    });
  }

  @Get('invited')
  @ApiOperation({ description: 'Get invited users' })
  @Responder.handle('Get invited users')
  @HttpCode(HttpStatus.OK)
  @RolesDecorator(UserRole.ADMIN, UserRole.SUPERVISOR, UserRole.ENGINEER, UserRole.COMPANY_ADMIN)
  @UseGuards(AuthGuard, RolesGuard)
  async findInvitedUsers(
    @CurrentUser() user: User,
  ): Promise<UserResponseDto[]> {
    const data = await this.service.getInvitedUsers();
    return data.map(e => toDto(UserResponseDto, e));
  }

  @Post('/invite')
  @ApiOperation({ description: 'Invite users' })
  @Responder.handle('Invite users')
  @HttpCode(HttpStatus.OK)
  @RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN)
  @UseGuards(AuthGuard, RolesGuard)
  async inviteUser(@CurrentUser() user, @Body() body: InviteUsersDto): Promise<Boolean> {
    return this.service.inviteUsers(user, body);
  }

  @Post('/invite/accept/:token')
  @ApiOperation({ description: 'Accept invitation' })
  @Responder.handle('Accept invitation')
  @HttpCode(HttpStatus.OK)
  async acceptInvite(@Param('token') token: string): Promise<AuthLoginResponseDto> {
    const email = await this.service.acceptInvitation(token);
    if (email) {
      return this.authService.login({email, password: '12345678'});
    }
    throw new HttpBadRequestError(ErrorCode.ACCEPT_INVITATION_FAILED);
  }

  @Post('/delete')
  @ApiOperation({ description: 'Delete user' })
  @Responder.handle('Delete user')
  @HttpCode(HttpStatus.OK)
  @RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN)
  @UseGuards(AuthGuard, RolesGuard)
  delete(@Body() body: DeleteUsersDto): Promise<boolean> {
    return this.service.deleteUsers(body);
  }
}
