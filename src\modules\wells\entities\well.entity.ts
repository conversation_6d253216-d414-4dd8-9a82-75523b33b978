import { Column, Entity, JoinColumn, ManyToMany, ManyToOne, JoinTable, OneToMany } from 'typeorm';
import { EntityHelper } from '../../../utils/entity-helper';
import { Company } from '../../companies/entities/company.entity';
import { Customer } from '../../customers/entities/customer.entity';
import { User } from '../../users/entities/user.entity';
import { State } from '../../states/entities/state.entity';
import { Country } from '../../countries/entities/country.entity';
import { DailyReport } from '../../daily-reports/entities/daily-report.entity';

@Entity()
export class Well extends EntityHelper {
  @ManyToOne(() => Company, { nullable: true })
  @JoinColumn()
  company: Company | null;

  @OneToMany(() => DailyReport, dailyReport => dailyReport.well, { nullable: true })
  @JoinColumn()
  dailyReport: DailyReport | null;

  @OneToMany(() => DailyReport, dailyReport => dailyReport.well, { nullable: true })
  @JoinColumn()
  reportWithLatestSample: DailyReport | null;

  @ManyToOne(() => State, { nullable: true })
  @JoinColumn({ name: 'stateOrProvinceId' })
  @Column({ type: String })
  stateOrProvinceId?: string;

  @ManyToOne(() => Country, { nullable: true })
  @JoinColumn({ name: 'countryId' })
  @Column({ type: String })
  countryId?: string;

  @ManyToMany(() => User, user => user.wells)
  @JoinTable({
    name: 'user_well',
    joinColumn: {
      name: 'wellId',
      referencedColumnName: 'id',
    },
    inverseJoinColumn: {
      name: 'userId',
      referencedColumnName: 'id',
    },
  })
  users: User[];

  @ManyToMany(() => Customer, customer => customer.wells)
  @JoinTable({
    name: 'customer_well',
    joinColumn: {
      name: 'wellId',
      referencedColumnName: 'id',
    },
    inverseJoinColumn: {
      name: 'customerId',
      referencedColumnName: 'id',
    },
  })
  customers: Customer[];

  @Column({ type: String, nullable: true })
  nameOrNo?: string;

  @Column({ type: String, nullable: true })
  apiWellNo?: string;

  @Column({ type: 'float', nullable: true })
  latitude?: number;

  @Column({ type: 'float', nullable: true })
  longitude?: number;

  @Column({ type: String, nullable: true })
  fieldOrBlock?: string;

  @Column({ type: String, nullable: true })
  sectionOrTownshipOrRange?: string;

  @Column({ type: String, nullable: true })
  countyOrParishOrOffshoreArea?: string;

  @Column({ type: String, nullable: true })
  rigName?: string;

  @Column({ type: 'timestamp', nullable: true })
  spudDate?: Date;

  @Column({ type: String, nullable: true })
  stockPoint?: string;

  @Column({ type: String, nullable: true })
  stockPointContact?: string;

  @Column({ type: String, nullable: true })
  operator?: string;

  @Column({ type: String, nullable: true })
  contractor?: string;

  @Column({ type: 'float', nullable: true })
  kickOffPoint?: number;

  @Column({ type: 'float', nullable: true })
  landingPoint?: number;

  @Column({ type: 'float', nullable: true })
  seaLevel?: number;

  @Column({ type: 'float', nullable: true })
  airGap?: number;

  @Column({ type: 'float', nullable: true })
  waterDepth?: number;

  @Column({ type: 'float', nullable: true })
  riserId?: number;

  @Column({ type: 'float', nullable: true })
  riserOD?: number;

  @Column({ type: 'float', nullable: true })
  chokeLineId?: number;

  @Column({ type: 'float', nullable: true })
  killLineId?: number;

  @Column({ type: 'float', nullable: true })
  boostLineId?: number;

  @Column({ nullable: true, default: false })
  rateOfPenetration?: boolean;

  @Column({ nullable: true, default: false })
  revolutionsPerMinute?: boolean;

  @Column({ nullable: true, default: false })
  eccentricity?: boolean;

  @Column({ nullable: false, default: false })
  archived: boolean;
}
