import { MigrationInterface, QueryRunner, Table } from 'typeorm';
import {
  columnCreatedAt,
  columnDeletedAt,
  columnId,
  columnUpdatedAt,
} from '../constants/columns.constant';

export class CreateSampleTable1702441948868 implements MigrationInterface {
  private tableName = 'sample';
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: this.tableName,
        columns: [
          columnId,
          {
            name: 'dailyReportId',
            type: 'uuid',
            isNullable: false,
          },
          {
            name: 'fluidType',
            type: 'int',
            isNullable: false,
          },
          {
            name: 'weightedMud',
            type: 'boolean',
            isNullable: false,
          },
          {
            name: 'sampleFrom',
            type: 'varchar',
            isNullable: false,
          },
          {
            name: 'timeSampleTaken',
            type: 'time',
            isNullable: false,
          },
          {
            name: 'flowlineTemperature',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'measuredDepth',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'mudWeight',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'funnelViscosity',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'temperatureForPlasticViscosity',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'plasticViscosity',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'yieldPoint',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'gelStrength10s',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'gelStrength10m',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'gelStrength30m',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'apiFiltrate',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'apiCakeThickness',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'temperatureForHTHP',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'hthpFiltrate',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'hthpCakeThickness',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'solids',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'oil',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'water',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'sandContent',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'mbtCapacity',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'pH',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'mudAlkalinity',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'filtrateAlkalinity',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'calcium',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'chlorides',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'totalHardness',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'excessLime',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'kPlus',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'makeUpWater',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'solidsAdjustedForSalt',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'fineLCM',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'coarseLCM',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'linearGelStrengthPercent',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'linearGelStrengthLbBbl',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'highGelStrengthPercent',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'highGelStrengthLbBbl',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'bentoniteConcentrationPercent',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'bentoniteConcentrationLbBbl',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'drillSolidsConcentrationPercent',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'drillSolidsConcentrationLbBbl',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'drillSolidsToBentoniteRatio',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'averageSpecificGravityOfSolids',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'shearRate600',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'shearRate300',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'shearRate200',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'shearRate100',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'shearRate6',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'shearRate3',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'apparentViscosity',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'shearRate',
            type: 'float8',
            isNullable: false,
          },
          {
            name: 'shearStress',
            type: 'float8',
            isNullable: false,
          },
          columnCreatedAt,
          columnUpdatedAt,
          columnDeletedAt,
        ],
      }),
      false,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable(this.tableName);
  }
}
