import { Injectable } from '@nestjs/common';
import { Interval } from './entities/interval.entity';
import { SelectQueryBuilder } from 'typeorm';
import { CreateIntervalDto } from './dtos/requests/create-interval.dto';
import { PaginationResponseDto } from '../../common/dtos/pagination.response.dto';
import { GetIntervalsQueryDto } from './dtos/requests/get-intervals.dto';
import { PaginationDto } from '../../common/dtos/paginationDto';
import { IntervalRepository } from './interval.repository';
import { UpdateIntervalDto } from './dtos/requests/update-interval.dto';

@Injectable()
export class IntervalService {
  constructor(private repository: IntervalRepository) {}

  async findAll(
    query: GetIntervalsQueryDto,
    paginationQuery: PaginationDto,
  ): Promise<PaginationResponseDto<Interval>> {
    const queryBuilder: SelectQueryBuilder<Interval> = this.repository
      .createQueryBuilder('interval')
      .where('interval.wellId = :wellId', { wellId: query.wellId })
      .orderBy('interval.createdAt', 'DESC')
      .addOrderBy('interval.interval', 'DESC');
    return this.repository.paginate(queryBuilder, paginationQuery);
  }

  async create(data: CreateIntervalDto): Promise<Interval> {
    return this.repository.save(this.repository.create(data));
  }

  async update(id: string, data: UpdateIntervalDto): Promise<boolean> {
    await this.repository.update({ id }, data);
    return true;
  }

  async findOne(id: string): Promise<Interval | null> {
    return this.repository.findOne({ where: { id } });
  }

  async softDeleteById(id: string): Promise<boolean> {
    await this.repository.softDelete({ id });
    return true;
  }
}
