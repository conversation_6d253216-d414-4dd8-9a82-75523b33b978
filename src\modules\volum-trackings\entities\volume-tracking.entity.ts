import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany } from 'typeorm';
import { <PERSON>tityHelper } from '../../../utils/entity-helper';
import { DailyReport } from '../../daily-reports/entities/daily-report.entity';
import { VolumeTrackingStatusEnum } from '../enums/volume-tracking-status.enum';
import { VolumeTrackingItem } from '../../volume-tracking-items/entities/volume-tracking-item.entity';

@Entity()
export class VolumeTracking extends EntityHelper {
  @ManyToOne(() => DailyReport, { nullable: false })
  @JoinColumn({ name: 'dailyReportId' })
  @Column({ type: String, nullable: false })
  dailyReportId: string;

  @Column({ type: String, nullable: false })
  name: string;

  @Column({ type: String, nullable: false })
  storageType: string;

  @Column({ nullable: false })
  status: VolumeTrackingStatusEnum;

  @Column({ type: 'float', nullable: false })
  measuredVolume: number;

  @Column({ type: 'float', nullable: false })
  mudWeight: number;

  @Column({ type: 'float', nullable: false })
  mudType: number;

  @OneToMany(() => VolumeTrackingItem, volumeItem => volumeItem.volumeTracking)
  volumeItems: VolumeTrackingItem[];

  @Column({ type: 'float', nullable: false })
  totalAdditions: number;

  @Column({ type: 'float', nullable: false })
  totalLosses: number;

  @Column({ type: 'float', nullable: false })
  totalTransfers: number;

  @Column({ type: 'float', nullable: false })
  calculatedVolume: number;
}
