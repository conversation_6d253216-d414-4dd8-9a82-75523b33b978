import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IsExist } from '../../utils/validators/is-exists.validator';
import { IsNotExist } from '../../utils/validators/is-not-exists.validator';
import { CustomerService } from './customer.service';
import { JwtModule } from '@nestjs/jwt';
import { CustomerController } from './customer.controller';
import { CustomerRepository } from './customer.repository';
import { Customer } from './entities/customer.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Customer]), JwtModule.register({})],
  controllers: [CustomerController],
  providers: [IsExist, IsNotExist, CustomerService, CustomerRepository],
  exports: [CustomerService],
})
export class CustomerModule {}
