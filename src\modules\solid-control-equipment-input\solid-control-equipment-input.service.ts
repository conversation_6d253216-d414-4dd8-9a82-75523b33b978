import { Injectable } from '@nestjs/common';
import { SolidControlEquipmentInputRepository } from './solid-control-equipment-input.repository';
import { CreateSolidControlEquipmentInputDto } from './dtos/requests/create-solid-control-equipment-input.dto';
import { SolidControlEquipmentInput } from './entities/solid-control-equipment-input.entity';
import { UpdateSolidControlEquipmentInputDto } from './dtos/requests/update-solid-control-equipment-input.dto';
import { SolidControlEquipment } from '../solid-control-equipment/entities/solid-control-equipment.entity';

@Injectable()
export class SolidControlEquipmentInputService {
  constructor(private repository: SolidControlEquipmentInputRepository) {}

  async create(data: CreateSolidControlEquipmentInputDto): Promise<SolidControlEquipmentInput> {
    const volumeItem = this.repository.create(data);
    volumeItem.solidControlEquipment = {
      id: data.solidControlEquipmentId,
    } as SolidControlEquipment;
    return this.repository.save(volumeItem);
  }

  async update(id: string, data: UpdateSolidControlEquipmentInputDto): Promise<boolean> {
    const solidControlEquipment = { id: data.solidControlEquipmentId } as SolidControlEquipment;
    const dataUpdate = { solidControlEquipment, ...data };
    delete dataUpdate.solidControlEquipmentId;
    await this.repository.update({ id }, dataUpdate);
    return true;
  }

  async findOne(id: string): Promise<SolidControlEquipmentInput | null> {
    return this.repository.findOne({ where: { id } });
  }

  async softDeleteById(id: string): Promise<boolean> {
    await this.repository.softDelete({ id });
    return true;
  }
}
