import { Injectable } from '@nestjs/common';
import { SolidControlEquipmentTimeRepository } from './solid-control-equipment-time.repository';
import { CreateSolidControlEquipmentTimeDto } from './dtos/requests/create-solid-control-equipment-time.dto';
import { SolidControlEquipmentTime } from './entities/solid-control-equipment-time.entity';
import { UpdateSolidControlEquipmentTimeDto } from './dtos/requests/update-solid-control-equipment-time.dto';
import { SolidControlEquipment } from '../solid-control-equipment/entities/solid-control-equipment.entity';
import { SolidControlEquipmentRepository } from '../solid-control-equipment/solid-control-equipment.repository';

@Injectable()
export class SolidControlEquipmentTimeService {
  constructor(
    private repository: SolidControlEquipmentTimeRepository,
    private solidControlEquipmentRepository: SolidControlEquipmentRepository,
  ) {}

  async create(data: CreateSolidControlEquipmentTimeDto): Promise<SolidControlEquipmentTime> {
    const volumeItem = this.repository.create(data);
    volumeItem.solidControlEquipment = {
      id: data.solidControlEquipmentId,
    } as SolidControlEquipment;
    const result = await this.repository.save(volumeItem);
    const totalDurations = await this.repository.getTotalDurations(data.solidControlEquipmentId!);
    await this.solidControlEquipmentRepository.update(
      { id: data.solidControlEquipmentId },
      { totalDurations },
    );
    return result;
  }

  async update(id: string, data: UpdateSolidControlEquipmentTimeDto): Promise<boolean> {
    const solidControlEquipment = { id: data.solidControlEquipmentId } as SolidControlEquipment;
    const dataUpdate = { solidControlEquipment, ...data };
    delete dataUpdate.solidControlEquipmentId;
    await this.repository.update({ id }, dataUpdate);
    const totalDurations = await this.repository.getTotalDurations(data.solidControlEquipmentId!);
    await this.solidControlEquipmentRepository.update(
      { id: data.solidControlEquipmentId },
      { totalDurations },
    );
    return true;
  }

  async findOne(id: string): Promise<SolidControlEquipmentTime | null> {
    return this.repository.findOne({ where: { id } });
  }

  async softDeleteById(id: string): Promise<boolean> {
    const duration = await this.repository.findOne({
      where: { id },
      relations: ['solidControlEquipment'],
    });
    await this.repository.softDelete({ id });
    if (duration?.solidControlEquipment?.id) {
      const totalDurations = await this.repository.getTotalDurations(
        duration!.solidControlEquipment!.id,
      );
      await this.solidControlEquipmentRepository.update(
        { id: duration!.solidControlEquipment!.id },
        { totalDurations },
      );
    }
    return true;
  }
}
