import { Module } from '@nestjs/common';
import { TaskSeedService } from './task-seed.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DailyReport } from '../../../modules/daily-reports/entities/daily-report.entity';
import { Task } from '../../../modules/tasks/entities/task.entity';

@Module({
  imports: [TypeOrmModule.forFeature([DailyReport, Task])],
  providers: [TaskSeedService],
  exports: [TaskSeedService],
})
export class TaskSeedModule {}
