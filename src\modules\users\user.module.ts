import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IsExist } from '../../utils/validators/is-exists.validator';
import { IsNotExist } from '../../utils/validators/is-not-exists.validator';
import { UserService } from './user.service';
import { JwtModule } from '@nestjs/jwt';
import { UserController } from './user.controller';
import { UserRepository } from './user.repository';
import { User } from './entities/user.entity';
import { WellInformationRepository } from '../well-informations/well-information.repository';
import { IsEmailUnique } from 'src/common/decorators';
import { MailModule } from 'src/mail/mail.module';
import { RoleRepository } from '../roles/role.repository';
import { MailService } from '../../mail/mail.service';
import { IsEmailUniqueWithoutSelf } from 'src/common/decorators';
import { CompanyRepository } from '../companies/company.repository';
import { AuthService } from '../auth/auth.service';
import { MongooseModule } from '@nestjs/mongoose';
import { UserSchema } from '../mongoose/users/user.schema';
import { UserService as MongoUserService } from '../mongoose/users/user.service';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: 'User', schema: UserSchema }]),
    JwtModule.register({}),
    MailModule,
  ],
  controllers: [UserController],
  providers: [
    IsExist,
    IsNotExist,
    UserService,
    AuthService,
    UserRepository,
    WellInformationRepository,
    IsEmailUnique,
    RoleRepository,
    MailService,
    IsEmailUniqueWithoutSelf,
    CompanyRepository,
  ],
  exports: [MongoUserService],
})
export class UserModule {}
