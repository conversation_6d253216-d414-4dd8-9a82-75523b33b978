import { Injectable } from '@nestjs/common';
import { Repository } from 'typeorm';
import { DailyReport } from '../../../modules/daily-reports/entities/daily-report.entity';
import { SolidControlEquipment } from '../../../modules/solid-control-equipment/entities/solid-control-equipment.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { SolidControlEquipmentType } from '../../../modules/solid-control-equipment-type/entities/solid-control-equipment-type.entity';
import { faker } from '@faker-js/faker';

@Injectable()
export class SolidControlEquipmentSeedService {
  constructor(
    @InjectRepository(DailyReport)
    private dailyReportRepository: Repository<DailyReport>,
    @InjectRepository(SolidControlEquipmentType)
    private solidControlEquipmentTypeRepository: Repository<SolidControlEquipmentType>,
    @InjectRepository(SolidControlEquipment)
    private solidControlEquipmentRepository: Repository<SolidControlEquipment>,
  ) {}

  async run(): Promise<void> {
    await this.fakeSolidControlEquipments();
  }

  private async fakeSolidControlEquipments(): Promise<void> {
    const type = await this.solidControlEquipmentTypeRepository.findOne({ where: {} });
    const reports = await this.dailyReportRepository.find({ where: {} });
    for (let i = 0; i < reports.length; i++) {
      await this.solidControlEquipmentRepository.save(
        this.solidControlEquipmentRepository.create({
          dailyReportId: reports[i].id,
          type: type!,
          screen: faker.number.float({ max: 200, precision: 0.01 }).toString(),
          totalDurations: 50,
        }),
      );
    }
  }
}
