import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString, IsUUID } from 'class-validator';

export class UpdateSolidDto {
  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  dailyReportId: string;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  shaleCEC?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  bentCEC?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  highGelStrength?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  linearGelStrength?: number;
}
