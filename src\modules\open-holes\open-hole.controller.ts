import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { PaginationResponseDto } from '../../common/dtos/pagination.response.dto';
import { AuthGuard } from '../auth/guards/auth.guard';

import { PaginationDto } from '../../common/dtos/paginationDto';
import { toDto, toPaginateDtos } from '../../common/transformers/dto.transformer';

import { RolesDecorator } from '../auth/decorators/roles.decorator';
import { UserRole } from '../roles/enums/roles.enum';
import { RolesGuard } from '../auth/guards/roles.guard';
import { OpenHoleService } from './open-hole.service';
import { GetOpenHoleDto } from './dtos/requests/get-open-hole.dto';
import { OpenHoleResponseDto } from './dtos/responses/open-hole.response.dto';
import { CreateOpenHoleDto } from './dtos/requests/create-open-hole.dto';
import { UpdateOpenHoleDto } from './dtos/requests/update-open-hole.dto';
import { Responder } from '../../common/decorators/responder.decorator';
import { User } from '../users/entities/user.entity';
import { CurrentUser } from '../../common/decorators/current-user.decorator';

@ApiTags('OpenHole')
@Controller('openHoles')
@ApiBearerAuth('access-token')
@RolesDecorator(UserRole.ADMIN, UserRole.SUPERVISOR, UserRole.ENGINEER, UserRole.COMPANY_ADMIN)
@UseGuards(AuthGuard, RolesGuard)
export class OpenHoleController {
  constructor(private readonly service: OpenHoleService) {}

  @Get()
  @ApiOperation({ description: 'Get open holes' })
  @Responder.handle('Get open holes')
  @HttpCode(HttpStatus.OK)
  async findAll(
    @Query() query: GetOpenHoleDto,
    @Query() paginationQuery: PaginationDto,
  ): Promise<PaginationResponseDto<OpenHoleResponseDto>> {
    const data = await this.service.findAll(query, paginationQuery);
    return toPaginateDtos(OpenHoleResponseDto, data);
  }

  @Post()
  @ApiOperation({ description: 'Create open holes' })
  @Responder.handle('Create open holes')
  @HttpCode(HttpStatus.CREATED)
  async create(
    @CurrentUser() user: User,
    @Body() data: CreateOpenHoleDto,
  ): Promise<OpenHoleResponseDto> {
    const value = await this.service.create(user, data);
    return toDto(OpenHoleResponseDto, value);
  }

  @Put('/:id')
  @ApiOperation({ description: 'Update open holes' })
  @Responder.handle('Update open holes')
  @HttpCode(HttpStatus.CREATED)
  async update(
    @CurrentUser() user: User,
    @Param('id') id: string,
    @Body() data: UpdateOpenHoleDto,
  ): Promise<boolean> {
    return this.service.update(user, id, data);
  }

  @Get('/:id')
  @ApiOperation({ description: 'Get open hole detail' })
  @Responder.handle('Get open hole detail')
  @HttpCode(HttpStatus.OK)
  async detail(@Param('id') id: string): Promise<OpenHoleResponseDto> {
    const data = await this.service.findOne(id);
    return toDto(OpenHoleResponseDto, data);
  }

  @Delete('/:id')
  @ApiOperation({ description: 'Delete cased hole' })
  @Responder.handle('Delete cased hole')
  @HttpCode(HttpStatus.OK)
  delete(@CurrentUser() user: User, @Param('id') id: string): Promise<boolean> {
    return this.service.softDeleteById(user, id);
  }
}
