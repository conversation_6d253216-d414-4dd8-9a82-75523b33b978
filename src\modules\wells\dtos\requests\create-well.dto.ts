import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsDateString,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';
import { IsArray } from '../../../../common/decorators';
import { IsUuidArray } from '../../../../common/decorators/class-validator/is-array-uuid.decorator';

export class CreateWellDto {
  @ApiProperty({ example: ['xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx'], required: true })
  @IsNotEmpty()
  @IsUuidArray()
  @IsArray()
  @IsOptional()
  userIds?: string[];

  @ApiProperty({ example: ['xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx'], required: true })
  @IsNotEmpty()
  @IsUuidArray()
  @IsArray()
  @IsOptional()
  customerIds?: string[];

  @ApiProperty({ required: false, example: 'xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx' })
  @IsUUID()
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  companyId?: string;

  @ApiProperty({ required: false, example: 'xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx' })
  @IsUUID()
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  stateOrProvinceId?: string;

  @ApiProperty({ required: false, example: 'xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx' })
  @IsUUID()
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  countryId?: string;

  @ApiProperty({required: true})
  @IsString()
  nameOrNo: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  apiWellNo?: string;

  @ApiProperty({ example: -74.194149 })
  @IsOptional()
  @IsNumber()
  latitude?: number;

  @ApiProperty({ example: -74.194149 })
  @IsOptional()
  @IsNumber()
  longitude?: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  fieldOrBlock?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  sectionOrTownshipOrRange?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  countyOrParishOrOffshoreArea?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  rigName?: string;

  @ApiProperty({ required: false })
  @IsNotEmpty()
  @IsDateString()
  @IsOptional()
  spudDate: Date;

  @ApiProperty()
  @IsString()
  @IsOptional()
  stockPoint?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  stockPointContact?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  operator?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  contractor?: string;

  @ApiProperty({ required: false })
  @IsNumber()
  @IsNotEmpty()
  @IsOptional()
  kickOffPoint: number;

  @ApiProperty({ required: false })
  @IsNumber()
  @IsNotEmpty()
  @IsOptional()
  landingPoint: number;

  @ApiProperty({ required: false })
  @IsNumber()
  @IsNotEmpty()
  @IsOptional()
  seaLevel: number;

  @ApiProperty({ required: false })
  @IsNumber()
  @IsNotEmpty()
  @IsOptional()
  airGap: number;

  @ApiProperty({ required: false })
  @IsNumber()
  @IsNotEmpty()
  @IsOptional()
  waterDepth: number;

  @ApiProperty({ required: false })
  @IsNumber()
  @IsNotEmpty()
  @IsOptional()
  riserId: number;

  @ApiProperty({ required: false })
  @IsNumber()
  @IsNotEmpty()
  @IsOptional()
  riserOD: number;

  @ApiProperty({ required: false })
  @IsNumber()
  @IsNotEmpty()
  @IsOptional()
  chokeLineId: number;

  @ApiProperty({ required: false })
  @IsNumber()
  @IsNotEmpty()
  @IsOptional()
  killLineId: number;

  @ApiProperty({ required: false })
  @IsNumber()
  @IsNotEmpty()
  @IsOptional()
  boostLineId: number;

  @ApiProperty({ default: false })
  @IsBoolean()
  @IsOptional()
  rateOfPenetration?: boolean;

  @ApiProperty({ default: false })
  @IsBoolean()
  @IsOptional()
  revolutionsPerMinute?: boolean;

  @ApiProperty({ default: false })
  @IsBoolean()
  @IsOptional()
  eccentricity?: boolean;
}
