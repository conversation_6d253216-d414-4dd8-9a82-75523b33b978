import { Expose, Type } from 'class-transformer';
import { LocationInfoResponseDto } from '../../../product-and-package-inventory-reports/dtos/responses/location-info.response.dto';
import { ProductAndPackageInventoryReportInfoResponseDto } from './product-and-package-inventory-report-info.response.dto';

export class ProductAndPackageInventoryItemResponseDto {
  @Expose()
  id: string;

  @Expose()
  @Type(() => ProductAndPackageInventoryReportInfoResponseDto)
  productAndPackageInventoryReport: ProductAndPackageInventoryReportInfoResponseDto;

  @Expose()
  @Type(() => LocationInfoResponseDto)
  location: LocationInfoResponseDto;

  @Expose()
  type: number;

  @Expose()
  quantity: number;

  @Expose()
  cost: number;

  @Expose()
  bolNo: string;

  @Expose()
  notes: string;

  @Expose()
  createdAt: string;

  @Expose()
  updatedAt: string;
}
