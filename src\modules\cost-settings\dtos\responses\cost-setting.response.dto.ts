import { Exclude, Expose, Type } from "class-transformer";
import { CompanyResponseDto } from "../../../companies/dtos/responses/company.response.dto";

@Exclude()
export class CostSettingResponseDto {
  @Expose()
  id: string;

  @Expose()
  name: string;

  @Expose()
  type: number;

  @Expose()
  cost: number;

  @Expose()
  @Type(() => CompanyResponseDto)
  company: CompanyResponseDto;

  @Expose()
  createdAt: string;
}
