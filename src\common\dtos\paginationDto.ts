import { Transform } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsOptional, <PERSON>, Min } from 'class-validator';

export class PaginationDto {
  @ApiProperty({ required: false, example: 1 })
  @IsOptional()
  @IsInt()
  @Transform(({ value }) => (value ? Number(value) : 0))
  page: number = 1;

  @ApiProperty({ required: false, example: 10 })
  @IsOptional()
  @IsInt()
  @Transform(({ value }) => (value ? Number(value) : 10))
  @Max(1000)
  @Min(1)
  limit: number = 10;
}
