import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsNotEmpty, IsNumber, IsOptional, IsString, IsUUID } from 'class-validator';

export class CreateProductAndPackageInventoryReportDto {
  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  productAndPackageInventoryId: string;

  @ApiProperty({ required: true })
  @IsUUID()
  @IsNotEmpty()
  productId: string;

  @ApiProperty({ required: true, example: 'volume tracking id' })
  @IsUUID()
  @IsNotEmpty()
  locationId: string;

  @ApiProperty({ required: true })
  @IsInt()
  quantity: number;

  @ApiProperty({ required: true })
  @IsNumber()
  cost: number;

  @ApiProperty({ required: false })
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  bolNo: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  notes: string;
}
