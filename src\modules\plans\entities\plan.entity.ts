import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm';
import { EntityHelper } from '../../../utils/entity-helper';
import { Well } from '../../wells/entities/well.entity';

@Entity()
export class Plan extends EntityHelper {
  @ManyToOne(() => Well)
  @JoinColumn({ name: 'wellId' })
  wellId: string;

  @Column({ type: 'float8', nullable: false })
  mudDepth: number;

  @Column({ type: 'int', nullable: false })
  day: number;

  @Column({ type: 'float8', nullable: false })
  cost: number;
}
