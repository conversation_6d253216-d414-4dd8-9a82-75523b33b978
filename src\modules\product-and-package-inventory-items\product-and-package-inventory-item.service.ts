import { Injectable } from '@nestjs/common';
import { ProductAndPackageInventoryItemRepository } from './product-and-package-inventory-item.repository';
import { ProductAndPackageInventoryItem } from './entities/product-and-package-inventory-item.entity';
import { CreateProductAndPackageInventoryItemDto } from './dtos/requests/create-product-and-package-inventory-item.dto';
import { UpdateProductAndPackageInventoryItemDto } from './dtos/requests/update-product-and-package-inventory-item.dto';
import { VolumeTracking } from '../volum-trackings/entities/volume-tracking.entity';
import { ProductAndPackageInventoryReport } from '../product-and-package-inventory-reports/entities/product-and-package-inventory-report.entity';
import { PaginationDto } from '../../common/dtos/paginationDto';
import { PaginationResponseDto } from '../../common/dtos/pagination.response.dto';
import { SelectQueryBuilder } from 'typeorm';
import { GetProductAndPackageInventoryItemsDto } from './dtos/requests/get-product-and-package-inventory-items.dto';
import { ProductAndPackageInventoryReportRepository } from '../product-and-package-inventory-reports/product-and-package-inventory-report.repository';
import { ProductAndPackageInventoryRepository } from '../product-and-package-inventorys/product-and-package-inventory.repository';
import { HttpBadRequestError } from '../../errors/bad-request.error';
import { ErrorCode } from '../../errors/error-code';
import { DailyReportRepository } from '../daily-reports/daily-report.repository';
import { User } from '../users/entities/user.entity';

@Injectable()
export class ProductAndPackageInventoryItemService {
  constructor(
    private dailyReportRepository: DailyReportRepository,
    private itemRepository: ProductAndPackageInventoryItemRepository,
    private reportRepository: ProductAndPackageInventoryReportRepository,
    private productAndPackageInventoryRepository: ProductAndPackageInventoryRepository,
  ) {}

  async findAll(
    query: GetProductAndPackageInventoryItemsDto,
    paginationQuery: PaginationDto,
  ): Promise<PaginationResponseDto<ProductAndPackageInventoryItem>> {
    const queryBuilder: SelectQueryBuilder<ProductAndPackageInventoryItem> = this.itemRepository
      .createQueryBuilder('productAndPackageInventoryItem')
      .where(
        'productAndPackageInventoryItem.productAndPackageInventoryReportId = :productAndPackageInventoryReportId',
        {
          productAndPackageInventoryReportId: query.productAndPackageInventoryReportId,
        },
      )
      .leftJoinAndSelect('productAndPackageInventoryItem.location', 'location')
      .orderBy('productAndPackageInventoryItem.createdAt', 'DESC');
    return this.itemRepository.paginate(queryBuilder, paginationQuery);
  }

  async create(
    user: User,
    data: CreateProductAndPackageInventoryItemDto,
  ): Promise<ProductAndPackageInventoryItem> {
    if (data.type === 1) {
      throw new HttpBadRequestError(ErrorCode.REPORT_ITEM_INITIAL_EXIST);
    }
    const item = this.itemRepository.create(data);
    item.location = { id: data.locationId } as VolumeTracking;
    item.productAndPackageInventoryReport = {
      id: data.productAndPackageInventoryReportId,
    } as ProductAndPackageInventoryReport;
    const result = await this.itemRepository.save(item);
    await this.updateProductAndPackageInventoryReport(data.productAndPackageInventoryReportId);
    const report = await this.reportRepository.findOne({
      where: { id: data.productAndPackageInventoryReportId },
      relations: ['productAndPackageInventory'],
    });
    await this.updateProductAndPackageInventory(user, report!.productAndPackageInventory!.id!);
    return result;
  }

  async update(
    user: User,
    id: string,
    data: UpdateProductAndPackageInventoryItemDto,
  ): Promise<boolean> {
    const location = { id: data.locationId } as VolumeTracking;
    const productAndPackageInventoryReport = {
      id: data.productAndPackageInventoryReportId,
    } as ProductAndPackageInventoryReport;
    const dataUpdate = { productAndPackageInventoryReport, location, ...data };
    delete dataUpdate.locationId;
    delete dataUpdate.productAndPackageInventoryReportId;
    await this.itemRepository.update({ id }, dataUpdate);
    await this.updateProductAndPackageInventoryReport(data.productAndPackageInventoryReportId!);
    const report = await this.reportRepository.findOne({
      where: { id: data.productAndPackageInventoryReportId },
      relations: ['productAndPackageInventory'],
    });
    await this.updateProductAndPackageInventory(user, report!.productAndPackageInventory!.id!);
    return true;
  }

  async findOne(id: string): Promise<ProductAndPackageInventoryItem | null> {
    return this.itemRepository
      .createQueryBuilder('item')
      .where('item.id = :id', { id })
      .leftJoinAndSelect('item.location', 'location')
      .leftJoinAndSelect('item.productAndPackageInventoryReport', 'report')
      .leftJoinAndSelect('report.product', 'product')
      .getOne();
  }

  async softDeleteById(user: User, id: string): Promise<boolean> {
    const item = await this.itemRepository.findOne({
      where: { id },
      relations: [
        'productAndPackageInventoryReport',
        'productAndPackageInventoryReport.productAndPackageInventory',
      ],
    });
    await this.itemRepository.softDelete({ id });
    if (item?.productAndPackageInventoryReport.id) {
      const reportId = item!.productAndPackageInventoryReport!.id!;
      const productAndPackageInventoryId =
        item.productAndPackageInventoryReport.productAndPackageInventory.id;
      await this.updateProductAndPackageInventoryReport(reportId);
      await this.updateProductAndPackageInventory(user, productAndPackageInventoryId);
    }
    return true;
  }

  async updateProductAndPackageInventoryReport(id: string): Promise<void> {
    const totalCost = await this.itemRepository.getTotalCost(id);
    const totalQty = await this.itemRepository.getTotalQuantity(id);
    await this.reportRepository.update({ id }, { totalCost, quantity: totalQty });
  }

  async updateProductAndPackageInventory(user: User, id: string) {
    const totalProductCost = await this.reportRepository.getTotalCost(id);
    await this.productAndPackageInventoryRepository.update({ id }, { totalCost: totalProductCost });
    const productAndPackageInventory = await this.productAndPackageInventoryRepository.findOne({
      where: { id: id },
      relations: ['dailyReport'],
    });
    if (productAndPackageInventory?.dailyReport.id) {
      await this.dailyReportRepository.updateReportUpdatedBy(
        user,
        productAndPackageInventory!.dailyReport!.id!,
      );
    }
  }
}
