import { Exclude, Expose, Type } from 'class-transformer';
import { CustomerInfoResponseDto } from './customer-info.response.dto';
import { EngineerInfoResponseDto } from './engineer-info.response.dto';
import { LatestReportInfoResponseDto } from './latest-report-info.response.dto';
import { CompanyResponseDto } from "../../../companies/dtos/responses/company.response.dto";
import { SampleResponseDto } from "../../../samples/dtos/responses/sample.response.dto";

@Exclude()
export class WellResponseDto {
  @Expose()
  id: string;

  @Expose()
  nameOrNo: string;

  @Expose()
  rigName: string;

  @Expose()
  landingPoint: number;

  @Expose()
  @Type(() => CompanyResponseDto)
  company: CompanyResponseDto;

  @Expose()
  @Type(() => EngineerInfoResponseDto)
  users: EngineerInfoResponseDto[];

  @Expose()
  @Type(() => CustomerInfoResponseDto)
  customers: CustomerInfoResponseDto[];

  @Expose()
  @Type(() => LatestReportInfoResponseDto)
  dailyReport: LatestReportInfoResponseDto;

  @Expose()
  @Type(() => LatestReportInfoResponseDto)
  reportWithLatestSample: LatestReportInfoResponseDto;

  @Expose()
  archived: boolean;

  @Expose()
  createdAt: Date;
}
