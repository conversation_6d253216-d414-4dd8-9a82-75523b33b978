import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsNumber, IsOptional, IsString, IsUUID } from 'class-validator';

export class CreatePumpDto {
  @ApiProperty({ required: true })
  @IsUUID()
  @IsNotEmpty()
  dailyReportId: string;

  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({ required: true, default: false })
  @IsBoolean()
  @IsOptional()
  inUse?: boolean;

  @ApiProperty()
  @IsString()
  @IsOptional()
  model?: string;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  linearID?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  rodOD?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  strokeLength?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  efficiency?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  stroke?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  displacement?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  rate?: number;
}
