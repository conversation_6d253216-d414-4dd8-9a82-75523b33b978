import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as Mailjet from 'node-mailjet';

@Injectable()
export class MailjetService {
  constructor(
    private readonly configService: ConfigService,
  ) {
    const apiKey = this.configService.get('mail.mailjetApiKey', { infer: true });
    const secretKey = this.configService.get('mail.mailjetSecretKey', { infer: true });
    if (apiKey && secretKey) {
      this.mailjet = Mailjet.Client.apiConnect(apiKey, secretKey);
    }
  }
  private mailjet;

  async sendOTPConfirmation(email: string, name: string, otp: string) {
    await this.mailjet.post('send', { version: 'v3.1' }).request({
      "Messages": [
        {
          "From": {
            "Email": '<EMAIL>',
          },
          "To": [
            {
              "Email": email,
              "Name": name
            }
          ],
          "TemplateID": 6307474,
          "TemplateLanguage": true,
          "Subject": `The Muddy Software - Active confirm`,
          "Variables": {
            "name": name,
            "otp": otp
          }
        }
      ]
    });
  }

  async sendActiveConfirmation(email: string, name: string, password: string) {
    await this.mailjet.post('send', { version: 'v3.1' }).request({
      "Messages": [
        {
          "From": {
            "Email": '<EMAIL>',
          },
          "To": [
            {
              "Email": email,
              "Name": name
            }
          ],
          "TemplateID": 6307444,
          "TemplateLanguage": true,
          "Subject": `The Muddy Software - Active confirm`,
          "Variables": {
            "name": name,
            "password": password,
          }
        }
      ]
    });
  }

  async sendResetPasswordMail(email: string, name: string, url: string) {
    await this.mailjet.post('send', { version: 'v3.1' }).request({
      "Messages": [
        {
          "From": {
            "Email": '<EMAIL>',
          },
          "To": [
            {
              "Email": email,
              "Name": name
            }
          ],
          "TemplateID": 6307487,
          "TemplateLanguage": true,
          "Subject": `The Muddy Software - Password reset`,
          "Variables": {
            "email": email,
            "name": name,
            "url": url,
          }
        }
      ]
    });
  }

  async sendInviteMail(email: string, company: string, name: string, url: string) {
    await this.mailjet.post('send', { version: 'v3.1' }).request({
      "Messages": [
        {
          "From": {
            "Email": '<EMAIL>',
          },
          "To": [
            {
              "Email": email,
              "Name": name
            }
          ],
          "TemplateID": 6282712,
          "TemplateLanguage": true,
          "Subject": `The Muddy Software - You are invited to ${company}`,
          "Variables": {
            "email": email,
            "url": url,
          }
        }
      ]
    });
  }
}
