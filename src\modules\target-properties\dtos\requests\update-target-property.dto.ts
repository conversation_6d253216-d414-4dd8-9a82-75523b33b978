import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional } from 'class-validator';

export class UpdateTargetPropertyDto {
  @ApiProperty()
  @IsNumber()
  @IsOptional()
  fluidType?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  mudWeight?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  funnelViscosity?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  plasticViscosity?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  yieldPoint?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  apiFiltrate?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  apiCakeThickness?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  pH?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  mudAlkalinity?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  filtrateAlkalinity?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  chlorides?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  totalHardness?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  linearGelStrengthPercent?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  rpm?: number;
}
