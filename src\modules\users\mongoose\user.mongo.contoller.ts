import { Api<PERSON>earerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { MongoUserService } from './user.mongo.service';
import { AuthService } from 'src/modules/auth/auth.service';
import { Controller, Get, HttpCode, HttpStatus, Param, UseGuards } from '@nestjs/common';
import { User } from './user.schema';
import { Responder } from 'src/common/decorators/responder.decorator';
import { RolesDecorator } from 'src/modules/auth/decorators/roles.decorator';
import { UserRole } from 'src/modules/roles/enums/roles.enum';
import { AuthGuard } from 'src/modules/auth/guards/auth.guard';
import { RolesGuard } from 'src/modules/auth/guards/roles.guard';
import { CurrentUser } from 'src/common/decorators/current-user.decorator';
import { CurrentMongoUser } from './current-user.decorator';

@ApiTags('User')
@Controller('mongo-users')
@ApiBearerAuth('access-token')
export class MongoUserController {
  constructor(
    private readonly service: MongoUserService,
    private readonly authService: AuthService,
  ) {}

  @Get()
  @ApiOperation({ description: 'Get users from MongoDB' })
  @Responder.handle('Get MongoDB users')
  @HttpCode(HttpStatus.OK)
  @RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN, UserRole.SUPERVISOR, UserRole.ENGINEER)
  @UseGuards(AuthGuard, RolesGuard)
  async findAllMongo(): Promise<User[] | null> {
    return this.service.findAll();
  }

  @Get('/:id')
  @ApiOperation({ description: 'Get single user by ID from MongoDB' })
  @Responder.handle('Get MongoDB user')
  @HttpCode(HttpStatus.OK)
  @RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN, UserRole.SUPERVISOR)
  @UseGuards(AuthGuard, RolesGuard)
  async getUserById(@Param('id') id: string): Promise<User | null> {
    return this.service.findOne(id);
  }

  @Get('/profile')
  @ApiOperation({ description: 'Get my profile' })
  @Responder.handle('Get my user profile')
  @HttpCode(HttpStatus.OK)
  @RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN, UserRole.SUPERVISOR, UserRole.ENGINEER)
  @UseGuards(AuthGuard, RolesGuard)
  async findProfile(@CurrentMongoUser() user: User): Promise<User | null> {
    return this.service.findOne(user.id);
  }
}
