import { Injectable } from '@nestjs/common';
import { faker } from '@faker-js/faker';
import { Repository } from 'typeorm';
import { CustomerContact } from '../../../modules/customer-contact/entities/customer-contact.entity';
import { Customer } from '../../../modules/customers/entities/customer.entity';
import { InjectRepository } from '@nestjs/typeorm';

@Injectable()
export class CustomerContactSeedService {
  constructor(
    @InjectRepository(CustomerContact)
    private customerContactRepository: Repository<CustomerContact>,
    @InjectRepository(Customer)
    private customerRepository: Repository<Customer>,
  ) {}

  async run(): Promise<void> {
    await this.fakeCustomerContacts();
  }

  private async fakeCustomerContacts(): Promise<void> {
    const customers = await this.customerRepository.find({});
    for (let i = 0; i < customers.length; i++) {
      console.log(customers[i].id);
      await this.customerContactRepository.save(
        this.customerContactRepository.create({
          customerId: customers[i].id,
          address: faker.location.streetAddress(),
          notes: faker.lorem.text(),
          emailAddress: faker.internet.email(),
          mobilePhone: faker.phone.number(),
          officePhone: faker.phone.number(),
          name: faker.company.name(),
          notifyOnNewReport: true,
          primaryContact: true,
        }),
      );
      if (i % 2 == 0) {
        await this.customerContactRepository.save(
          this.customerContactRepository.create({
            customerId: customers[i].id,
            address: faker.location.streetAddress(),
            notes: faker.lorem.text(),
            emailAddress: faker.internet.email(),
            mobilePhone: faker.phone.number(),
            officePhone: faker.phone.number(),
            name: faker.company.name(),
            notifyOnNewReport: true,
            primaryContact: false,
          }),
        );
      }
    }
  }
}
