import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { BaseRepository } from '../../common/bases/base.repository';
import { CustomerContact } from './entities/customer-contact.entity';

@Injectable()
export class CustomerContactRepository extends BaseRepository<CustomerContact> {
  constructor(dataSource: DataSource) {
    super(CustomerContact, dataSource);
  }
}
