import { Module } from '@nestjs/common';
import { ProductSeedService } from './product-seed.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Company } from '../../../modules/companies/entities/company.entity';
import { Product } from '../../../modules/products/entities/product.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Company, Product])],
  providers: [ProductSeedService],
  exports: [ProductSeedService],
})
export class ProductSeedModule {}
