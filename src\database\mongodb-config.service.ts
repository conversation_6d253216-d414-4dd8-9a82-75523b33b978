import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { MongooseModuleOptions, MongooseOptionsFactory } from '@nestjs/mongoose';

@Injectable()
export class MongodbConfigService implements MongooseOptionsFactory {
  constructor(private configService: ConfigService) {}

  createMongooseOptions(): MongooseModuleOptions {
    const uri =
      this.configService.get('database.url') ||
      `mongodb://${this.configService.get('database.username')}:${this.configService.get(
        'database.password',
      )}@${this.configService.get('database.host')}:${this.configService.get(
        'database.port',
      )}/${this.configService.get('database.name')}`;

    return {
      uri,
    };
  }
}
