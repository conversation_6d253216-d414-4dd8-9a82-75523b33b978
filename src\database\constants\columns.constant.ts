import { TableColumnOptions } from 'typeorm';

export const columnId: TableColumnOptions = {
  name: 'id',
  type: 'uuid',
  isPrimary: true,
  generationStrategy: 'uuid',
  default: `uuid_generate_v4()`,
};

export const columnCreatedAt: TableColumnOptions = {
  name: 'createdAt',
  type: 'timestamp',
  default: 'now()',
};

export const columnUpdatedAt: TableColumnOptions = {
  name: 'updatedAt',
  type: 'timestamp',
  default: 'now()',
};

export const columnDeletedAt: TableColumnOptions = {
  name: 'deletedAt',
  type: 'timestamp',
  isNullable: true,
};
