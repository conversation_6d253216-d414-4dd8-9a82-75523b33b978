import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IsExist } from '../../utils/validators/is-exists.validator';
import { IsNotExist } from '../../utils/validators/is-not-exists.validator';
import { JwtModule } from '@nestjs/jwt';
import { OpenHole } from './entities/open-hole.entity';
import { OpenHoleController } from './open-hole.controller';
import { OpenHoleService } from './open-hole.service';
import { OpenHoleRepository } from './open-hole.repository';
import { DailyReportRepository } from '../daily-reports/daily-report.repository';

@Module({
  imports: [TypeOrmModule.forFeature([OpenHole]), JwtModule.register({})],
  controllers: [OpenHoleController],
  providers: [IsExist, IsNotExist, OpenHoleService, OpenHoleRepository, DailyReportRepository],
  exports: [OpenHoleService],
})
export class OpenHoleModule {}
