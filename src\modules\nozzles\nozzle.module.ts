import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IsExist } from '../../utils/validators/is-exists.validator';
import { IsNotExist } from '../../utils/validators/is-not-exists.validator';
import { NozzleService } from './nozzle.service';
import { JwtModule } from '@nestjs/jwt';
import { NozzleController } from './nozzle.controller';
import { NozzleRepository } from './nozzle.repository';
import { Nozzle } from './entities/nozzle.entity';
import { DailyReportRepository } from '../daily-reports/daily-report.repository';

@Module({
  imports: [TypeOrmModule.forFeature([Nozzle]), JwtModule.register({})],
  controllers: [NozzleController],
  providers: [IsExist, IsNotExist, NozzleService, NozzleRepository, DailyReportRepository],
  exports: [NozzleService],
})
export class NozzleModule {}
