import { Exclude, Expose, Type } from "class-transformer";
import { UserResponseDto } from "../../../users/dtos/responses/user.response.dto";

@Exclude()
export class CompanyResponseDto {
  @Expose()
  id: string;

  @Expose()
  name: string;

  @Expose()
  registerNumber: string;

  @Expose()
  description: string;

  @Expose()
  @Type(() => UserResponseDto)
  users: UserResponseDto[];

  @Expose()
  createdAt: string;
}
