import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IsExist } from '../../utils/validators/is-exists.validator';
import { IsNotExist } from '../../utils/validators/is-not-exists.validator';
import { CostService } from './cost.service';
import { Cost } from './entities/cost.entity';
import { JwtModule } from '@nestjs/jwt';
import { CostController } from './cost.controller';
import { CostRepository } from './cost.repository';
import { DailyReportRepository } from '../daily-reports/daily-report.repository';

@Module({
  imports: [TypeOrmModule.forFeature([Cost]), JwtModule.register({})],
  controllers: [CostController],
  providers: [IsExist, IsNotExist, CostService, CostRepository, DailyReportRepository],
  exports: [CostService],
})
export class CostModule {}
