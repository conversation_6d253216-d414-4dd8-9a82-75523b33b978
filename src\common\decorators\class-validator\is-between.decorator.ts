import { registerDecorator, ValidationArguments, ValidationOptions } from 'class-validator';

export function IsBetweenField(
  property1: string,
  property2: string,
  validationOptions?: ValidationOptions,
) {
  return (object: any, propertyName: string) => {
    registerDecorator({
      name: 'IsBetweenField',
      target: object.constructor,
      propertyName,
      constraints: [property1, property2],
      options: Object.assign(
        {
          message: `${propertyName} must be between ${property1} and ${property2}`,
        },
        validationOptions,
      ),
      validator: {
        validate(value: any, args: ValidationArguments) {
          const [relatedPropertyName1, relatedPropertyName2] = args.constraints;
          const relatedValue1 = (args.object as any)[relatedPropertyName1];
          const relatedValue2 = (args.object as any)[relatedPropertyName2];
          const valueNumber = Number(value);
          const relatedValue1Number = Number(relatedValue1);
          const relatedValue2Number = Number(relatedValue2);
          return (
            !isNaN(valueNumber) &&
            !isNaN(relatedValue1Number) &&
            !isNaN(relatedValue2Number) &&
            valueNumber >= relatedValue1Number &&
            valueNumber <= relatedValue2Number
          );
        },
      },
    });
  };
}
