import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { PaginationResponseDto } from '../../common/dtos/pagination.response.dto';
import { AuthGuard } from '../auth/guards/auth.guard';

import { PaginationDto } from '../../common/dtos/paginationDto';
import { toDto, toPaginateDtos } from '../../common/transformers/dto.transformer';

import { RolesDecorator } from '../auth/decorators/roles.decorator';
import { UserRole } from '../roles/enums/roles.enum';
import { RolesGuard } from '../auth/guards/roles.guard';

import { DailyReportService } from './daily-report.service';
import { CurrentUser } from '../../common/decorators/current-user.decorator';
import { GetDailyReportsQueryDto } from './dtos/requests/get-daily-reports.dto';
import { User } from '../users/entities/user.entity';
import { DailyReportResponseDto } from './dtos/responses/daily-report.response.dto';
import { CreateDailyReportDto } from './dtos/requests/create-daily-report.dto';
import { UpdateDailyReportDto } from './dtos/requests/update-daily-report.dto';
import { Responder } from '../../common/decorators/responder.decorator';
import { DailyReportDetailResponseDto } from './dtos/responses/daily-report-detail.response.dto';
import { NozzleService } from "../nozzles/nozzle.service";
import { GetPreviousReportsQueryDto } from "./dtos/requests/get-previous-reports.dto";

@ApiTags('DailyReport')
@Controller('dailyReports')
@ApiBearerAuth('access-token')
@UseGuards(AuthGuard, RolesGuard)
export class DailyReportController {
  constructor(private readonly dailyReportService: DailyReportService, private readonly nozzleService: NozzleService) {}

  @Get()
  @ApiOperation({ description: 'Get daily reports' })
  @Responder.handle('Get daily reports')
  @HttpCode(HttpStatus.OK)
  @RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN, UserRole.SUPERVISOR, UserRole.ENGINEER)
  async findAll(
    @CurrentUser() user: User,
    @Query() query: GetDailyReportsQueryDto,
    @Query() paginationQuery: PaginationDto,
  ): Promise<PaginationResponseDto<DailyReportResponseDto>> {
    const data = await this.dailyReportService.findAll(user, query, paginationQuery);
    return toPaginateDtos(DailyReportResponseDto, data);
  }

  @Post()
  @ApiOperation({ description: 'Create daily report' })
  @Responder.handle('Create daily report')
  @HttpCode(HttpStatus.CREATED)
  @RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN, UserRole.SUPERVISOR, UserRole.ENGINEER)
  async create(
    @CurrentUser() user: User,
    @Body() data: CreateDailyReportDto,
  ): Promise<DailyReportResponseDto> {
    const value = await this.dailyReportService.create(user, data);
    return toDto(DailyReportResponseDto, value);
  }

  @Get('/:id')
  @ApiOperation({ description: 'Get daily report detail' })
  @Responder.handle('Get daily report detail')
  @HttpCode(HttpStatus.CREATED)
  @RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN, UserRole.SUPERVISOR, UserRole.ENGINEER)
  async getDetail(@Param('id') id: string): Promise<DailyReportDetailResponseDto> {
    const data = await this.dailyReportService.detail(id);
    const newData = {...data};
    if (data?.wellInformation?.dailyReport?.id) {
      const wellData = {...data?.wellInformation};
      wellData['tfa'] = await this.nozzleService.getTfa(data!.wellInformation!.dailyReport!.id);
      newData['wellInformation'] = wellData as any;
    }
    return toDto(DailyReportDetailResponseDto, newData);
  }

  @Put('/:id')
  @ApiOperation({ description: 'Update daily report' })
  @Responder.handle('Update daily report')
  @HttpCode(HttpStatus.CREATED)
  @RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN, UserRole.SUPERVISOR, UserRole.ENGINEER)
  async update(@Param('id') id: string, @Body() data: UpdateDailyReportDto): Promise<boolean> {
    return this.dailyReportService.update(id, data);
  }

  @Get('/todayReport/:wellId')
  @ApiOperation({ description: 'Get daily report detail' })
  @Responder.handle('Get daily report detail')
  @HttpCode(HttpStatus.OK)
  @RolesDecorator(UserRole.ADMIN, UserRole.SUPERVISOR, UserRole.ENGINEER, UserRole.COMPANY_ADMIN)
  async todayReport(
    @CurrentUser() user: User,
    @Param('wellId') wellId: string,
  ): Promise<DailyReportDetailResponseDto> {
    const data = await this.dailyReportService.findTodayReport(user, wellId);
    return toDto(DailyReportDetailResponseDto, data);
  }

  @Get('/latest/:wellId')
  @ApiOperation({ description: 'Get the latest daily report' })
  @Responder.handle('Get the latest daily report')
  @HttpCode(HttpStatus.OK)
  @RolesDecorator(UserRole.ADMIN, UserRole.SUPERVISOR, UserRole.ENGINEER, UserRole.COMPANY_ADMIN)
  async latestReport(
    @CurrentUser() user: User,
    @Param('wellId') wellId: string,
  ): Promise<DailyReportDetailResponseDto> {
    const data = await this.dailyReportService.findLatestReport(user, wellId);
    return toDto(DailyReportDetailResponseDto, data);
  }

  @Get('/previous/get')
  @ApiOperation({ description: 'Get the previous daily report' })
  @Responder.handle('Get the previous daily report')
  @HttpCode(HttpStatus.OK)
  @RolesDecorator(UserRole.ADMIN, UserRole.SUPERVISOR, UserRole.ENGINEER, UserRole.COMPANY_ADMIN)
  async getPreviousReport(@Query() query: GetPreviousReportsQueryDto): Promise<DailyReportDetailResponseDto | null> {
    const data = await this.dailyReportService.getPreviousReport(query);
    return toDto(DailyReportDetailResponseDto, data);
  }

  @Delete('/:id')
  @ApiOperation({ description: 'Delete daily report' })
  @Responder.handle('Delete daily report')
  @HttpCode(HttpStatus.OK)
  @RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN, UserRole.SUPERVISOR, UserRole.ENGINEER)
  delete(@Param('id') id: string): Promise<boolean> {
    return this.dailyReportService.softDeleteById(id);
  }
}
