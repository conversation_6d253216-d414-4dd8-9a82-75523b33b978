import { CanActivate, ExecutionContext, Injectable, UnauthorizedException } from "@nestjs/common";
import { JwtService } from "@nestjs/jwt";
import { Request } from "express";
import { ConfigService } from "@nestjs/config";
import { AllConfigType } from "../../../config/config.type";
import { UserService } from "../../users/user.service";
import { UserStatus } from "../../users/enums/statuses.enum";
import { ErrorCode } from "../../../errors/error-code";

@Injectable()
export class UserGuard implements CanActivate {
  constructor(
    private jwtService: JwtService,
    private userService: UserService,
    private configService: ConfigService<AllConfigType>,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const token = this.extractTokenFromHeader(request);
    if (!token) {
      return true;
    }
    try {
      request['user'] = await this.jwtService.verifyAsync(token, {
        secret: this.configService.getOrThrow('auth.secret', { infer: true }),
      });
    } catch {
      return true;
    }
    const user = await this.userService.findOne({id: request['user'].id});
    if (!user) {
      throw new UnauthorizedException();
    }
    if (user.status != UserStatus.ACTIVE) {
      throw new UnauthorizedException(ErrorCode.USER_IN_ACTIVE);
    }
    return true;
  }

  private extractTokenFromHeader(request: Request): string | undefined {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }
}
