import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm';
import { EntityHelper } from '../../../utils/entity-helper';
import { Country } from '../../countries/entities/country.entity';

@Entity()
export class State extends EntityHelper {
  @ManyToOne(() => Country, { nullable: false })
  @JoinColumn({ name: 'countryId' })
  @Column({ type: String, nullable: false })
  countryId: string;

  @Column({ type: String, nullable: false })
  name: string;
}
