import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { PaginationResponseDto } from '../../common/dtos/pagination.response.dto';
import { AuthGuard } from '../auth/guards/auth.guard';

import { PaginationDto } from '../../common/dtos/paginationDto';
import { toDto, toPaginateDtos } from '../../common/transformers/dto.transformer';

import { RolesDecorator } from '../auth/decorators/roles.decorator';
import { UserRole } from '../roles/enums/roles.enum';
import { RolesGuard } from '../auth/guards/roles.guard';
import { DrillBitService } from './drill-bit.service';
import { DrillBitResponseDto } from './dtos/responses/drill-bit.response.dto';
import { CreateDrillBitDto } from './dtos/requests/create-drill-bit.dto';
import { GetDrillBitDto } from './dtos/requests/get-drill-bit.dto';
import { UpdateDrillBitDto } from './dtos/requests/update-drill-bit.dto';
import { Responder } from '../../common/decorators/responder.decorator';
import { CurrentUser } from '../../common/decorators/current-user.decorator';
import { User } from '../users/entities/user.entity';

@ApiTags('DrillBit')
@Controller('drillBits')
@ApiBearerAuth('access-token')
@RolesDecorator(UserRole.ADMIN, UserRole.SUPERVISOR, UserRole.ENGINEER, UserRole.COMPANY_ADMIN)
@UseGuards(AuthGuard, RolesGuard)
export class DrillBitController {
  constructor(private readonly service: DrillBitService) {}

  @Get()
  @ApiOperation({ description: 'Get drill bits' })
  @Responder.handle('Get drill bits')
  @HttpCode(HttpStatus.OK)
  async findAll(
    @Query() query: GetDrillBitDto,
    @Query() paginationQuery: PaginationDto,
  ): Promise<PaginationResponseDto<DrillBitResponseDto>> {
    const data = await this.service.findAll(query, paginationQuery);
    return toPaginateDtos(DrillBitResponseDto, data);
  }

  @Post()
  @ApiOperation({ description: 'Create drill bits' })
  @Responder.handle('Create drill bits')
  @HttpCode(HttpStatus.CREATED)
  async create(
    @CurrentUser() user: User,
    @Body() data: CreateDrillBitDto,
  ): Promise<DrillBitResponseDto> {
    const value = await this.service.create(user, data);
    return toDto(DrillBitResponseDto, value);
  }

  @Put('/:id')
  @ApiOperation({ description: 'Update drill bit' })
  @Responder.handle('Update drill bit')
  @HttpCode(HttpStatus.CREATED)
  async update(
    @CurrentUser() user: User,
    @Param('id') id: string,
    @Body() data: UpdateDrillBitDto,
  ): Promise<boolean> {
    return this.service.update(user, id, data);
  }

  @Get('/:id')
  @ApiOperation({ description: 'Get drill bit detail' })
  @Responder.handle('Get drill bit detail')
  @HttpCode(HttpStatus.OK)
  async detail(@Param('id') id: string): Promise<DrillBitResponseDto> {
    const data = await this.service.findOne(id);
    return toDto(DrillBitResponseDto, data);
  }

  @Delete('/:id')
  @ApiOperation({ description: 'Delete drill bit' })
  @Responder.handle('Delete drill bit')
  @HttpCode(HttpStatus.OK)
  delete(@CurrentUser() user: User, @Param('id') id: string): Promise<boolean> {
    return this.service.softDeleteById(user, id);
  }
}
