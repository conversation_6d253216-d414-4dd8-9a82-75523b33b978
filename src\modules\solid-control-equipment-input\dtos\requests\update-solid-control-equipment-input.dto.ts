import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsString, IsUUID } from 'class-validator';

export class UpdateSolidControlEquipmentInputDto {
  @ApiProperty({ required: true })
  @IsUUID()
  @IsNotEmpty()
  solidControlEquipmentId?: string;

  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({ required: true })
  @IsNumber()
  value: number;

  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty()
  units: string;
}
