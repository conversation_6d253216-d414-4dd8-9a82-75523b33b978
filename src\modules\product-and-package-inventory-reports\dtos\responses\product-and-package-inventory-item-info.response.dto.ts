import { Expose, Type } from 'class-transformer';
import { LocationInfoResponseDto } from './location-info.response.dto';

export class ProductAndPackageInventoryItemInfoResponseDto {
  @Expose()
  id: string;

  @Expose()
  @Type(() => LocationInfoResponseDto)
  location: LocationInfoResponseDto;

  @Expose()
  type: number;

  @Expose()
  quantity: number;

  @Expose()
  cost: number;

  @Expose()
  createdAt: string;

  @Expose()
  updatedAt: string;
}
