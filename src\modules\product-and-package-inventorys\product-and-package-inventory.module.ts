import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IsExist } from '../../utils/validators/is-exists.validator';
import { IsNotExist } from '../../utils/validators/is-not-exists.validator';
import { ProductAndPackageInventoryService } from './product-and-package-inventory.service';
import { ProductAndPackageInventory } from './entities/product-and-package-inventory.entity';
import { JwtModule } from '@nestjs/jwt';
import { ProductAndPackageInventoryRepository } from './product-and-package-inventory.repository';
import { ProductAndPackageInventoryController } from './product-and-package-inventory.controller';
import { DailyReportRepository } from '../daily-reports/daily-report.repository';

@Module({
  imports: [TypeOrmModule.forFeature([ProductAndPackageInventory]), JwtModule.register({})],
  controllers: [ProductAndPackageInventoryController],
  providers: [
    IsExist,
    IsNotExist,
    ProductAndPackageInventoryService,
    ProductAndPackageInventoryRepository,
    DailyReportRepository,
  ],
  exports: [ProductAndPackageInventoryService],
})
export class ProductAndPackageInventoryModule {}
