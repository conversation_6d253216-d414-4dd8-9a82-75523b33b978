import { Module } from '@nestjs/common';
import { CostSeedService } from './cost-seed.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Cost } from '../../../modules/costs/entities/cost.entity';
import { DailyReport } from '../../../modules/daily-reports/entities/daily-report.entity';
import { CostSetting } from '../../../modules/cost-settings/entities/cost-setting.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Cost, CostSetting, DailyReport])],
  providers: [CostSeedService],
  exports: [CostSeedService],
})
export class CostSeedModule {}
