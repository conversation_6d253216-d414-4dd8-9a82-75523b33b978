import { Exclude, Expose, Type } from 'class-transformer';
import { CompanyResponseDto } from "../../../companies/dtos/responses/company.response.dto";

@Exclude()
export class ProductDetailResponseDto {
  @Expose()
  id: string;

  @Expose()
  name: string;

  @Expose()
  description: string;

  @Expose()
  price: number;

  @Expose()
  @Type(() => CompanyResponseDto)
  company: CompanyResponseDto;
  
  @Expose()
  createdAt: string;
}
