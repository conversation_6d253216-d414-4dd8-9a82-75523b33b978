import { Controller, Get, HttpCode, HttpStatus, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { PaginationResponseDto } from '../../common/dtos/pagination.response.dto';
import { AuthGuard } from '../auth/guards/auth.guard';
import { CountryService } from './country.service';
import { PaginationDto } from '../../common/dtos/paginationDto';
import { toPaginateDtos } from '../../common/transformers/dto.transformer';
import { CountryResponseDto } from './dtos/responses/country.response.dto';
import { GetCountriesQueryDto } from './dtos/requests/get-countries.dto';
import { Responder } from '../../common/decorators/responder.decorator';

@ApiTags('Country')
@Controller('countries')
@ApiBearerAuth('access-token')
@UseGuards(AuthGuard)
export class CountryController {
  constructor(private readonly countryService: CountryService) {}

  @Get()
  @ApiOperation({ description: 'Get countries' })
  @Responder.handle('Get countries')
  @HttpCode(HttpStatus.OK)
  async findAll(
    @Query() query: GetCountriesQueryDto,
    @Query() paginationQuery: PaginationDto,
  ): Promise<PaginationResponseDto<CountryResponseDto>> {
    const data = await this.countryService.findAll(query, paginationQuery);
    return toPaginateDtos(CountryResponseDto, data);
  }
}
