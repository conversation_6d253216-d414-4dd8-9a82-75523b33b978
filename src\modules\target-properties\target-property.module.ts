import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IsExist } from '../../utils/validators/is-exists.validator';
import { IsNotExist } from '../../utils/validators/is-not-exists.validator';
import { TargetPropertyService } from './target-property.service';
import { TargetProperty } from './entities/target-property.entity';
import { JwtModule } from '@nestjs/jwt';
import { TargetPropertyRepository } from './target-property.repository';
import { TargetPropertyController } from './target-property.controller';

@Module({
  imports: [TypeOrmModule.forFeature([TargetProperty]), JwtModule.register({})],
  controllers: [TargetPropertyController],
  providers: [IsExist, IsNotExist, TargetPropertyService, TargetPropertyRepository],
  exports: [TargetPropertyService],
})
export class TargetPropertyModule {}
