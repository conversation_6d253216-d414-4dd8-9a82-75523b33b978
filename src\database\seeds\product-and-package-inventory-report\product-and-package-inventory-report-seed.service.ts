import { Injectable } from '@nestjs/common';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { ProductAndPackageInventoryReport } from '../../../modules/product-and-package-inventory-reports/entities/product-and-package-inventory-report.entity';
import { Product } from '../../../modules/products/entities/product.entity';
import { ProductAndPackageInventory } from '../../../modules/product-and-package-inventorys/entities/product-and-package-inventory.entity';

@Injectable()
export class ProductAndPackageInventoryReportSeedService {
  constructor(
    @InjectRepository(ProductAndPackageInventory)
    private productAndPackageInventoryRepository: Repository<ProductAndPackageInventory>,
    @InjectRepository(Product)
    private productRepository: Repository<Product>,
    @InjectRepository(ProductAndPackageInventoryReport)
    private productAndPackageInventoryReportRepository: Repository<ProductAndPackageInventoryReport>,
  ) {}

  async run(): Promise<void> {
    await this.fakeProductAndPackageInventoryItems();
  }

  private async fakeProductAndPackageInventoryItems(): Promise<void> {
    const productAndPackageInventories = await this.productAndPackageInventoryRepository.find({
      where: {},
    });
    const product = await this.productRepository.findOne({ where: {} });
    for (let i = 0; i < productAndPackageInventories.length; i++) {
      await this.productAndPackageInventoryReportRepository.save(
        this.productAndPackageInventoryReportRepository.create({
          productAndPackageInventory: { id: productAndPackageInventories[i]?.id },
          product: { id: product?.id },
          totalCost: 100,
          quantity: 10,
        }),
      );
    }
  }
}
