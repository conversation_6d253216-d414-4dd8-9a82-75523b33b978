import { Exclude, Expose, Type } from 'class-transformer';
import { RoleResponseDto } from '../../../roles/dtos/responses/role.response.dto.js';
import { CompanyResponseDto } from "../../../companies/dtos/responses/company.response.dto";

@Exclude()
export class UserProfileAdditionInfoResponseDto {
  @Expose()
  id: string;

  @Expose()
  email: string;

  @Expose()
  firstName: string;

  @Expose()
  lastName: string;

  @Expose()
  @Type(() => CompanyResponseDto)
  company: CompanyResponseDto;

  @Expose()
  officePhone: string;

  @Expose()
  mobilePhone: string;

  @Expose()
  address: string;

  @Expose()
  note: string;

  @Expose()
  avatar?: string;

  @Expose()
  @Type(() => RoleResponseDto)
  roles: RoleResponseDto[];

  @Expose()
  status: number;
}
