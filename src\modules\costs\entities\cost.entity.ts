import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm';
import { EntityHelper } from '../../../utils/entity-helper';
import { DailyReport } from '../../daily-reports/entities/daily-report.entity';
import { CostSetting } from '../../cost-settings/entities/cost-setting.entity';

@Entity()
export class Cost extends EntityHelper {
  @ManyToOne(() => DailyReport, { nullable: false })
  @JoinColumn({ name: 'dailyReportId' })
  dailyReport: DailyReport;

  @ManyToOne(() => CostSetting, { nullable: false })
  @JoinColumn({ name: 'costSettingId' })
  costSetting: CostSetting;

  @Column({ type: 'float', nullable: false })
  unit: number;

  @Column({ type: 'int', nullable: false })
  quantity: number;
}
