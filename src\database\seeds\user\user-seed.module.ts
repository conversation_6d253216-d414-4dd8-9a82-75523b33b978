import { Module } from '@nestjs/common';
import { UserSeedService } from './user-seed.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from '../../../modules/users/entities/user.entity';
import { Company } from '../../../modules/companies/entities/company.entity';
import { Role } from '../../../modules/roles/entities/role.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Role, User, Company])],
  providers: [UserSeedService],
  exports: [UserSeedService],
})
export class UserSeedModule {}
