import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { PaginationResponseDto } from '../../common/dtos/pagination.response.dto';
import { AuthGuard } from '../auth/guards/auth.guard';

import { PaginationDto } from '../../common/dtos/paginationDto';
import { toDto, toPaginateDtos } from '../../common/transformers/dto.transformer';

import { RolesDecorator } from '../auth/decorators/roles.decorator';
import { UserRole } from '../roles/enums/roles.enum';
import { RolesGuard } from '../auth/guards/roles.guard';
import { CasedHoleService } from './cased-hole.service';
import { GetCasedHoleDto } from './dtos/requests/get-cased-hole.dto';
import { CasedHoleResponseDto } from './dtos/responses/cased-hole.response.dto';
import { CreateCasedHoleDto } from './dtos/requests/create-cased-hole.dto';
import { UpdateCasedHoleDto } from './dtos/requests/update-cased-hole.dto';
import { Responder } from '../../common/decorators/responder.decorator';
import { CurrentUser } from '../../common/decorators/current-user.decorator';
import { User } from '../users/entities/user.entity';

@ApiTags('CasedHole')
@Controller('casedHoles')
@ApiBearerAuth('access-token')
@RolesDecorator(UserRole.ADMIN, UserRole.SUPERVISOR, UserRole.ENGINEER, UserRole.COMPANY_ADMIN)
@UseGuards(AuthGuard, RolesGuard)
export class CasedHoleController {
  constructor(private readonly service: CasedHoleService) {}

  @Get()
  @ApiOperation({ description: 'Get cased holes' })
  @Responder.handle('Get cased holes')
  @HttpCode(HttpStatus.OK)
  async findAll(
    @Query() query: GetCasedHoleDto,
    @Query() paginationQuery: PaginationDto,
  ): Promise<PaginationResponseDto<CasedHoleResponseDto>> {
    const data = await this.service.findAll(query, paginationQuery);
    return toPaginateDtos(CasedHoleResponseDto, data);
  }

  @Post()
  @ApiOperation({ description: 'Create cased holes' })
  @Responder.handle('Create cased holes')
  @HttpCode(HttpStatus.CREATED)
  async create(
    @CurrentUser() user: User,
    @Body() data: CreateCasedHoleDto,
  ): Promise<CasedHoleResponseDto> {
    const value = await this.service.create(user, data);
    return toDto(CasedHoleResponseDto, value);
  }

  @Put('/:id')
  @ApiOperation({ description: 'Update cased holes' })
  @Responder.handle('Update cased holes')
  @HttpCode(HttpStatus.CREATED)
  async update(
    @CurrentUser() user: User,
    @Param('id') id: string,
    @Body() data: UpdateCasedHoleDto,
  ): Promise<boolean> {
    return this.service.update(user, id, data);
  }

  @Get('/:id')
  @ApiOperation({ description: 'Get cased hole detail' })
  @Responder.handle('Get cased hole detail')
  @HttpCode(HttpStatus.OK)
  async detail(@Param('id') id: string): Promise<CasedHoleResponseDto> {
    const data = await this.service.findOne(id);
    return toDto(CasedHoleResponseDto, data);
  }

  @Delete('/:id')
  @ApiOperation({ description: 'Delete cased hole' })
  @Responder.handle('Delete cased hole')
  @HttpCode(HttpStatus.OK)
  delete(@CurrentUser() user: User, @Param('id') id: string): Promise<boolean> {
    return this.service.softDeleteById(user, id);
  }
}
