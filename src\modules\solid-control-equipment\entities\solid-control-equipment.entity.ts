import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyTo<PERSON>ne, OneToMany } from 'typeorm';
import { <PERSON>tityHelper } from '../../../utils/entity-helper';
import { DailyReport } from '../../daily-reports/entities/daily-report.entity';
import { SolidControlEquipmentType } from '../../solid-control-equipment-type/entities/solid-control-equipment-type.entity';
import { SolidControlEquipmentTime } from '../../solid-control-equipment-time/entities/solid-control-equipment-time.entity';
import { SolidControlEquipmentInput } from '../../solid-control-equipment-input/entities/solid-control-equipment-input.entity';

@Entity()
export class SolidControlEquipment extends EntityHelper {
  @ManyToOne(() => DailyReport, { nullable: false })
  @JoinColumn({ name: 'dailyReportId' })
  @Column({ nullable: false })
  dailyReportId: string;

  @ManyToOne(() => SolidControlEquipmentType, { nullable: false })
  @JoinColumn({ name: 'solidControlEquipmentTypeId' })
  type: SolidControlEquipmentType;

  @OneToMany(() => SolidControlEquipmentTime, duration => duration.solidControlEquipment, {
    nullable: false,
  })
  durations: SolidControlEquipmentTime[];

  @OneToMany(() => SolidControlEquipmentInput, duration => duration.solidControlEquipment, {
    nullable: false,
  })
  inputs: SolidControlEquipmentInput[];

  @Column({ nullable: false })
  screen: string;

  @Column({ type: 'int', nullable: false })
  totalDurations: number;
}
