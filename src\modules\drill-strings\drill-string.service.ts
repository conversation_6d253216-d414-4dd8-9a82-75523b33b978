import { Injectable } from '@nestjs/common';
import { SelectQueryBuilder } from 'typeorm';
import { PaginationDto } from '../../common/dtos/paginationDto';
import { PaginationResponseDto } from '../../common/dtos/pagination.response.dto';

import { DrillStringRepository } from './drill-string.repository';
import { GetDrillStringDto } from './dtos/requests/get-drill-string.dto';
import { DrillString } from './entities/drill-string.entity';
import { CreateDrillStringDto } from './dtos/requests/create-drill-string.dto';
import { UpdateDrillStringDto } from './dtos/requests/update-drill-string.dto';
import { DailyReportRepository } from '../daily-reports/daily-report.repository';
import { User } from '../users/entities/user.entity';

@Injectable()
export class DrillStringService {
  constructor(
    private drillStringRepository: DrillStringRepository,
    private reportRepository: DailyReportRepository,
  ) {}

  async findAll(
    query: GetDrillStringDto,
    paginationQuery: PaginationDto,
  ): Promise<PaginationResponseDto<DrillString>> {
    const queryBuilder: SelectQueryBuilder<DrillString> = this.drillStringRepository
      .createQueryBuilder('drillString')
      .where('drillString.dailyReportId = :dailyReportId', { dailyReportId: query.dailyReportId })
      .orderBy('drillString.createdAt', 'DESC')
      .addOrderBy('drillString.description', 'ASC');
    return this.drillStringRepository.paginate(queryBuilder, paginationQuery);
  }

  async create(user: User, data: CreateDrillStringDto): Promise<DrillString> {
    await this.reportRepository.updateReportUpdatedBy(user, data.dailyReportId);
    return this.drillStringRepository.save(this.drillStringRepository.create(data));
  }

  async update(user: User, id: string, data: UpdateDrillStringDto): Promise<boolean> {
    await this.reportRepository.updateReportUpdatedBy(user, data.dailyReportId);
    await this.drillStringRepository.update({ id }, data);
    return true;
  }

  async findOne(id: string): Promise<DrillString | null> {
    return this.drillStringRepository.findOne({ where: { id } });
  }

  async softDeleteById(user: User, id: string): Promise<boolean> {
    const item = await this.drillStringRepository
      .createQueryBuilder('drill')
      .where('drill.id = :id', { id })
      .getOne();
    if (item?.dailyReportId) {
      await this.reportRepository.updateReportUpdatedBy(user, item!.dailyReportId!);
    }
    await this.drillStringRepository.softDelete({ id });
    return true;
  }
}
