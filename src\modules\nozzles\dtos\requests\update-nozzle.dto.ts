import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsN<PERSON>ber, IsString, IsUUID } from 'class-validator';

export class UpdateNozzleDto {
  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  dailyReportId: string;

  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty()
  identificationNumber: string;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  orificeSize: number;
}
