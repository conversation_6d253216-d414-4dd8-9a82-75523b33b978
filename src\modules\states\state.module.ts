import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IsExist } from '../../utils/validators/is-exists.validator';
import { IsNotExist } from '../../utils/validators/is-not-exists.validator';
import { StateService } from './state.service';
import { State } from './entities/state.entity';
import { StateController } from './state.controller';
import { StateRepository } from './state.repository';
import { JwtModule } from '@nestjs/jwt';

@Module({
  imports: [TypeOrmModule.forFeature([State]), JwtModule.register({})],
  controllers: [StateController],
  providers: [IsExist, IsNotExist, StateService, StateRepository],
  exports: [StateService],
})
export class StateModule {}
