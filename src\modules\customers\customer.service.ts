import { Injectable } from '@nestjs/common';
import { SelectQueryBuilder } from 'typeorm';
import { PaginationDto } from '../../common/dtos/paginationDto';
import { PaginationResponseDto } from '../../common/dtos/pagination.response.dto';

import { CustomerRepository } from './customer.repository';
import { User } from '../users/entities/user.entity';
import { Customer } from './entities/customer.entity';
import { GetCustomerQueryDto } from './dtos/requests/get-customer.dto';
import { CreateCustomerDto } from './dtos/requests/create-customer.dto';
import { UpdateCustomerDto } from './dtos/requests/update-customer.dto';
import { DeleteCustomerQueryDto } from "./dtos/requests/delete-customer.dto";

@Injectable()
export class CustomerService {
  constructor(private repository: CustomerRepository) {}

  async findAll(
    query: GetCustomerQueryDto,
    paginationQuery: PaginationDto,
  ): Promise<PaginationResponseDto<Customer>> {
    let queryBuilder: SelectQueryBuilder<Customer> = this.repository
      .createQueryBuilder('customer');
    if (query.companyId) {
      queryBuilder = queryBuilder.where('customer.companyId = :companyId', { companyId: query.companyId });
    }
    if (query.name) {
      queryBuilder = queryBuilder.andWhere('customer.customerName ILIKE :name', {
        name: `%${query.name}%`,
      });
    }
    if (query.sortBy) {
      const sortDirection = query.sortDirection ?? 'ASC';
      queryBuilder = queryBuilder.orderBy(
        `customer.${query.sortBy}`,
        sortDirection === 'DESC' ? 'DESC' : 'ASC',
      );
    } else {
      queryBuilder = queryBuilder.orderBy('customer.createdAt', 'DESC');
    }
    return this.repository.paginate(queryBuilder, paginationQuery);
  }

  async create(user: User, data: CreateCustomerDto): Promise<Customer> {
    const customer = this.repository.create(data);
    return this.repository.save(customer);
  }

  async update(id: string, user: User, data: UpdateCustomerDto): Promise<Customer> {
    return await this.repository.save({ id, ...data });
  }

  async findOne(id: string): Promise<Customer | null> {
    return this.repository.findOne({ where: { id }, relations: ['company'] });
  }

  async softDeleteById(body: DeleteCustomerQueryDto): Promise<boolean> {
    for (const id of body.ids) {
      await this.repository.softDelete({ id });
    }
    return true;
  }
}
