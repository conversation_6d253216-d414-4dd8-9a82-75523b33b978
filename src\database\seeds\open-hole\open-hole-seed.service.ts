import { Injectable } from '@nestjs/common';
import { faker } from '@faker-js/faker';
import { Repository } from 'typeorm';
import { DailyReport } from '../../../modules/daily-reports/entities/daily-report.entity';
import { OpenHole } from '../../../modules/open-holes/entities/open-hole.entity';
import { InjectRepository } from '@nestjs/typeorm';

@Injectable()
export class OpenHoleSeedService {
  constructor(
    @InjectRepository(DailyReport)
    private dailyReportService: Repository<DailyReport>,
    @InjectRepository(OpenHole)
    private openHoleService: Repository<OpenHole>,
  ) {}

  async run(): Promise<void> {
    await this.fakeOpenHoles();
  }

  private async fakeOpenHoles(): Promise<void> {
    const reports = await this.dailyReportService.find({ where: {} });
    for (let i = 0; i < reports.length; i++) {
      for (let j = 0; j < 20; j++) {
        await this.openHoleService.save(
          this.openHoleService.create({
            dailyReport: { id: reports[i]?.id },
            description: faker.lorem.text(),
            insideDiameter: faker.number.float({ max: 200, precision: 0.01 }),
            measuredDepth: faker.number.float({ max: 200, precision: 0.01 }),
            washout: faker.number.float({ max: 200, precision: 0.01 }),
          }),
        );
      }
    }
  }
}
