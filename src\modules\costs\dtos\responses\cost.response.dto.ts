import { Exclude, Expose, Type } from 'class-transformer';
import { CostSettingResponseDto } from '../../../cost-settings/dtos/responses/cost-setting.response.dto';

@Exclude()
export class CostResponseDto {
  @Expose()
  id: string;

  @Expose()
  @Type(() => CostSettingResponseDto)
  costSetting: CostSettingResponseDto;

  @Expose()
  unit: number;

  @Expose()
  quantity: number;

  @Expose()
  createdAt: string;
}
