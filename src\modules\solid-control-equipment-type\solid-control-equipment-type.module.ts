import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IsExist } from '../../utils/validators/is-exists.validator';
import { IsNotExist } from '../../utils/validators/is-not-exists.validator';
import { SolidControlEquipmentTypeService } from './solid-control-equipment-type.service';
import { JwtModule } from '@nestjs/jwt';
import { SolidControlEquipmentTypeController } from './solid-control-equipment-type.controller';
import { SolidControlEquipmentTypeRepository } from './solid-control-equipment-type.repository';
import { SolidControlEquipmentType } from './entities/solid-control-equipment-type.entity';

@Module({
  imports: [TypeOrmModule.forFeature([SolidControlEquipmentType]), JwtModule.register({})],
  controllers: [SolidControlEquipmentTypeController],
  providers: [
    <PERSON><PERSON>xist,
    IsNotExist,
    SolidControlEquipmentTypeService,
    SolidControlEquipmentTypeRepository,
  ],
  exports: [SolidControlEquipmentTypeService],
})
export class SolidControlEquipmentTypeModule {}
