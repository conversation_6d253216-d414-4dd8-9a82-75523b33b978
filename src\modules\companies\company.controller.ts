import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { PaginationResponseDto } from '../../common/dtos/pagination.response.dto';
import { AuthGuard } from '../auth/guards/auth.guard';

import { PaginationDto } from '../../common/dtos/paginationDto';
import { toDto, toPaginateDtos } from '../../common/transformers/dto.transformer';

import { RolesDecorator } from '../auth/decorators/roles.decorator';
import { UserRole } from '../roles/enums/roles.enum';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Responder } from '../../common/decorators/responder.decorator';
import { CurrentUser } from '../../common/decorators/current-user.decorator';
import { User } from '../users/entities/user.entity';
import { CompanyService } from './company.service';
import { GetCompanyQueryDto } from './dtos/requests/get-company.dto';
import { CompanyResponseDto } from './dtos/responses/company.response.dto';
import { CreateCompanyDto } from './dtos/requests/create-company.dto';
import { UpdateCompanyDto } from './dtos/requests/update-company.dto';
import { UserResponseDto } from '../users/dtos/responses/user.response.dto';
import { AssignAdminsDto } from './dtos/requests/assign-users.request.dto';
import { RemoveAdminsDto } from './dtos/requests/remove-users.request.dto';
import { GetCompanyUsersDto } from './dtos/requests/get-company-users.dto';

@ApiTags('Company')
@Controller('companies')
@ApiBearerAuth('access-token')
@UseGuards(AuthGuard, RolesGuard)
export class CompanyController {
  constructor(private readonly companyService: CompanyService) {}

  @Get()
  @ApiOperation({ description: 'Get companies' })
  @Responder.handle('Get companies')
  @HttpCode(HttpStatus.OK)
  @RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN)
  async findAll(
    @CurrentUser() user,
    @Query() query: GetCompanyQueryDto,
    @Query() paginationQuery: PaginationDto,
  ): Promise<PaginationResponseDto<CompanyResponseDto>> {
    const data = await this.companyService.findAll(user, query, paginationQuery);
    ///Get only one admin user
    const items = data.items.map((company) => ({
      ...company,
      users: company.users.filter((e) => (e.roles ?? []).map((e) => e.value).includes(UserRole.COMPANY_ADMIN)).slice(0, 1),  // Keep only the first user (admin)
    }));
    const newData = {
      total: data.total,
      page: data.page,
      limit: data.limit,
      items,
      totalPage: data.totalPage,
    }
    return toPaginateDtos(CompanyResponseDto, newData);
  }

  @Post()
  @ApiOperation({ description: 'Create company' })
  @Responder.handle('Create company')
  @HttpCode(HttpStatus.CREATED)
  @RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN)
  async create(
    @Body() data: CreateCompanyDto,
  ): Promise<CompanyResponseDto> {
    const value = await this.companyService.create(data);
    return toDto(CompanyResponseDto, value);
  }

  @Put('/:id')
  @ApiOperation({ description: 'Update company' })
  @Responder.handle('Update company')
  @HttpCode(HttpStatus.CREATED)
  @RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN)
  async update(
    @Param('id') id: string,
    @Body() data: UpdateCompanyDto,
  ): Promise<CompanyResponseDto> {
    const value = await this.companyService.update(id, data);
    return toDto(CompanyResponseDto, value);
  }

  @Get('/:id')
  @ApiOperation({ description: 'Get company detail' })
  @Responder.handle('Get company detail')
  @HttpCode(HttpStatus.OK)
  @RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN, UserRole.ENGINEER, UserRole.SUPERVISOR)
  async detail(@Param('id') id: string): Promise<CompanyResponseDto> {
    const data = await this.companyService.findOne(id);
    return toDto(CompanyResponseDto, data);
  }

  @Delete('/:id')
  @ApiOperation({ description: 'Delete company' })
  @Responder.handle('Delete company')
  @HttpCode(HttpStatus.OK)
  @RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN)
  delete(@Param('id') id: string): Promise<boolean> {
    return this.companyService.softDeleteById(id);
  }

  @Get('/users/list')
  @ApiOperation({ description: 'Get company users' })
  @Responder.handle('Get company users')
  @HttpCode(HttpStatus.OK)
  @RolesDecorator(UserRole.ADMIN, UserRole.SUPERVISOR, UserRole.COMPANY_ADMIN, UserRole.ENGINEER)
  async getUsers(
    @CurrentUser() user: User,
    @Query() query: GetCompanyUsersDto,
    @Query() paginationQuery: PaginationDto,
  ): Promise<PaginationResponseDto<UserResponseDto>> {
    const data = await this.companyService.getUsers(user, query, paginationQuery);
    return toPaginateDtos(UserResponseDto, data);
  }

  @Post('/users/add')
  @ApiOperation({ description: 'Add company users' })
  @Responder.handle('Add company users')
  @HttpCode(HttpStatus.OK)
  @RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN)
  async addUsers(@Body() body: AssignAdminsDto): Promise<Boolean> {
    return this.companyService.addUsers(body);
  }

  @Post('/users/remove')
  @ApiOperation({ description: 'Remove company users' })
  @Responder.handle('Remove company users')
  @HttpCode(HttpStatus.OK)
  @RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN)
  async removeUsers(@Body() body: RemoveAdminsDto): Promise<Boolean> {
    return this.companyService.removeUsers(body);
  }
}
