import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { PaginationResponseDto } from '../../common/dtos/pagination.response.dto';
import { AuthGuard } from '../auth/guards/auth.guard';

import { PaginationDto } from '../../common/dtos/paginationDto';
import { toDto, toPaginateDtos } from '../../common/transformers/dto.transformer';

import { RolesDecorator } from '../auth/decorators/roles.decorator';
import { UserRole } from '../roles/enums/roles.enum';
import { RolesGuard } from '../auth/guards/roles.guard';
import { GetVolumeTrackingDto } from './dtos/requests/get-volume-tracking.dto';
import { VolumeTrackingService } from './volume-tracking.service';

import { UpdateVolumeTrackingDto } from './dtos/requests/update-volume-tracking.dto';
import { VolumeTrackingResponseDto } from './dtos/responses/volume-tracking.response.dto';
import { CreateVolumeTrackingDto } from './dtos/requests/create-volume-tracking.dto';
import { Responder } from '../../common/decorators/responder.decorator';
import { CurrentUser } from '../../common/decorators/current-user.decorator';
import { User } from '../users/entities/user.entity';
import { VolumeTrackingDetailResponseDto } from './dtos/responses/volume-tracking-detail.response.dto';

@ApiTags('VolumeTracking')
@Controller('volumeTrackings')
@ApiBearerAuth('access-token')
@RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN, UserRole.SUPERVISOR, UserRole.ENGINEER)
@UseGuards(AuthGuard, RolesGuard)
export class VolumeTrackingController {
  constructor(private readonly service: VolumeTrackingService) {}

  @Get()
  @ApiOperation({ description: 'Get volume tracking' })
  @Responder.handle('Get volume tracking')
  @HttpCode(HttpStatus.OK)
  async findAll(
    @Query() query: GetVolumeTrackingDto,
    @Query() paginationQuery: PaginationDto,
  ): Promise<PaginationResponseDto<VolumeTrackingResponseDto>> {
    const data = await this.service.findAll(query, paginationQuery);
    return toPaginateDtos(VolumeTrackingResponseDto, data);
  }

  @Post()
  @ApiOperation({ description: 'Create volume Tracking' })
  @Responder.handle('Create volume Tracking')
  @HttpCode(HttpStatus.CREATED)
  async create(
    @CurrentUser() user: User,
    @Body() data: CreateVolumeTrackingDto,
  ): Promise<VolumeTrackingResponseDto> {
    const value = await this.service.create(user, data);
    return toDto(VolumeTrackingResponseDto, value);
  }

  @Put('/:id')
  @ApiOperation({ description: 'Update volume Tracking' })
  @Responder.handle('Update volume Tracking')
  @HttpCode(HttpStatus.CREATED)
  async update(
    @CurrentUser() user: User,
    @Param('id') id: string,
    @Body() data: UpdateVolumeTrackingDto,
  ): Promise<boolean> {
    return this.service.update(user, id, data);
  }

  @Get('/:id')
  @ApiOperation({ description: 'Get volume Tracking detail' })
  @Responder.handle('Get volume Tracking detail')
  @HttpCode(HttpStatus.OK)
  async detail(@Param('id') id: string): Promise<VolumeTrackingDetailResponseDto> {
    const data = await this.service.findOne(id);
    return toDto(VolumeTrackingDetailResponseDto, data);
  }

  @Delete('/:id')
  @ApiOperation({ description: 'Delete volume Tracking' })
  @Responder.handle('Delete volume Tracking')
  @HttpCode(HttpStatus.OK)
  delete(@CurrentUser() user: User, @Param('id') id: string): Promise<boolean> {
    return this.service.softDeleteById(user, id);
  }
}
