import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { AuthGuard } from '../auth/guards/auth.guard';
import { toDto } from '../../common/transformers/dto.transformer';

import { RolesDecorator } from '../auth/decorators/roles.decorator';
import { UserRole } from '../roles/enums/roles.enum';
import { RolesGuard } from '../auth/guards/roles.guard';
import { SolidControlEquipmentTimeService } from './solid-control-equipment-time.service';
import { Responder } from '../../common/decorators/responder.decorator';
import { CreateSolidControlEquipmentTimeDto } from './dtos/requests/create-solid-control-equipment-time.dto';
import { SolidControlEquipmentTimeResponseDto } from './dtos/responses/solid-control-equipment-time.response.dto';
import { UpdateSolidControlEquipmentTimeDto } from './dtos/requests/update-solid-control-equipment-time.dto';

@ApiTags('SolidControlEquipmentTime')
@Controller('solidControlEquipmentTimes')
@ApiBearerAuth('access-token')
@RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN, UserRole.SUPERVISOR, UserRole.ENGINEER)
@UseGuards(AuthGuard, RolesGuard)
export class SolidControlEquipmentTimeController {
  constructor(private readonly service: SolidControlEquipmentTimeService) {}

  @Post()
  @ApiOperation({ description: 'Create solid control equipment times' })
  @Responder.handle('Create solid control equipment times')
  @HttpCode(HttpStatus.CREATED)
  async create(
    @Body() data: CreateSolidControlEquipmentTimeDto,
  ): Promise<SolidControlEquipmentTimeResponseDto> {
    const value = await this.service.create(data);
    return toDto(SolidControlEquipmentTimeResponseDto, value);
  }

  @Put('/:id')
  @ApiOperation({ description: 'Update solid control equipment time' })
  @Responder.handle('Update volume tracking item')
  @HttpCode(HttpStatus.CREATED)
  async update(
    @Param('id') id: string,
    @Body() data: UpdateSolidControlEquipmentTimeDto,
  ): Promise<boolean> {
    return this.service.update(id, data);
  }

  @Get('/:id')
  @ApiOperation({ description: 'Get solid control equipment time detail' })
  @Responder.handle('Get volume tracking item detail')
  @HttpCode(HttpStatus.OK)
  async detail(@Param('id') id: string): Promise<SolidControlEquipmentTimeResponseDto> {
    const data = await this.service.findOne(id);
    return toDto(SolidControlEquipmentTimeResponseDto, data);
  }

  @Delete('/:id')
  @ApiOperation({ description: 'Delete solid control equipment time' })
  @Responder.handle('Delete volume tracking item')
  @HttpCode(HttpStatus.OK)
  delete(@Param('id') id: string): Promise<boolean> {
    return this.service.softDeleteById(id);
  }
}
