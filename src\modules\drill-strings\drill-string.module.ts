import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IsExist } from '../../utils/validators/is-exists.validator';
import { IsNotExist } from '../../utils/validators/is-not-exists.validator';
import { DrillStringService } from './drill-string.service';
import { DrillString } from './entities/drill-string.entity';
import { JwtModule } from '@nestjs/jwt';
import { DrillStringController } from './drill-string.controller';
import { DrillStringRepository } from './drill-string.repository';
import { DailyReportRepository } from '../daily-reports/daily-report.repository';

@Module({
  imports: [TypeOrmModule.forFeature([DrillString]), JwtModule.register({})],
  controllers: [DrillStringController],
  providers: [
    IsExist,
    IsNotExist,
    DrillStringService,
    DrillStringRepository,
    DailyReportRepository,
  ],
  exports: [DrillStringService],
})
export class DrillStringModule {}
