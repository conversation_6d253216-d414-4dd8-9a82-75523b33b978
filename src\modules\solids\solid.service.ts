import { Injectable } from '@nestjs/common';

import { SolidRepository } from './solid.repository';
import { Solid } from './entities/solid.entity';
import { CreateSolidDto } from './dtos/requests/create-solid.dto';
import { UpdateSolidDto } from './dtos/requests/update-solid.dto';
import { DailyReportRepository } from '../daily-reports/daily-report.repository';
import { User } from '../users/entities/user.entity';

@Injectable()
export class SolidService {
  constructor(
    private repository: SolidRepository,
    private reportRepository: DailyReportRepository,
  ) {}

  async create(user: User, data: CreateSolidDto): Promise<Solid> {
    await this.reportRepository.updateReportUpdatedBy(user, data.dailyReportId);
    return this.repository.save(this.repository.create(data));
  }

  async update(user: User, id: string, data: UpdateSolidDto): Promise<boolean> {
    await this.reportRepository.updateReportUpdatedBy(user, data.dailyReportId);
    await this.repository.update({ id }, data);
    return true;
  }

  async findByReport(dailyReportId: string): Promise<Solid | null> {
    return this.repository
      .createQueryBuilder('solid')
      .where('solid.dailyReportId = :dailyReportId', { dailyReportId })
      .orderBy('solid.createdAt', 'DESC')
      .getOne();
  }

  async softDeleteById(user: User, id: string): Promise<boolean> {
    const item = await this.repository
      .createQueryBuilder('solid')
      .where('solid.id = :id', { id })
      .getOne();
    if (item?.dailyReportId) {
      await this.reportRepository.updateReportUpdatedBy(user, item!.dailyReportId);
    }
    await this.repository.softDelete({ id });
    return true;
  }
}
