import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm';
import { EntityHelper } from '../../../utils/entity-helper';
import { Well } from '../../wells/entities/well.entity';

@Entity()
export class Interval extends EntityHelper {
  @ManyToOne(() => Well)
  @JoinColumn({ name: 'wellId' })
  wellId: string;

  @Column({ type: 'int', nullable: false })
  interval: number;

  @Column({ type: String, nullable: false })
  notes: string;
}
