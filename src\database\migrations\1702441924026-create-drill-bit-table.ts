import { MigrationInterface, QueryRunner, Table } from 'typeorm';
import {
  columnCreatedAt,
  columnDeletedAt,
  columnId,
  columnUpdatedAt,
} from '../constants/columns.constant';

export class CreateDrillBitTable1702441924026 implements MigrationInterface {
  private tableName = 'drill_bit';
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: this.tableName,
        columns: [
          columnId,
          {
            name: 'dailyReportId',
            type: 'uuid',
            isNullable: false,
          },
          {
            name: 'bitNo',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'type',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'iadcType',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'bitSize',
            type: 'float8',
            isNullable: true,
          },
          {
            name: 'depth',
            type: 'float8',
            isNullable: true,
          },
          {
            name: 'bitRunDuration',
            type: 'float8',
            isNullable: true,
          },
          columnCreatedAt,
          columnUpdatedAt,
          columnDeletedAt,
        ],
      }),
      false,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable(this.tableName);
  }
}
