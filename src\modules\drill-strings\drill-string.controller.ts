import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { PaginationResponseDto } from '../../common/dtos/pagination.response.dto';
import { AuthGuard } from '../auth/guards/auth.guard';

import { PaginationDto } from '../../common/dtos/paginationDto';
import { toDto, toPaginateDtos } from '../../common/transformers/dto.transformer';

import { RolesDecorator } from '../auth/decorators/roles.decorator';
import { UserRole } from '../roles/enums/roles.enum';
import { RolesGuard } from '../auth/guards/roles.guard';
import { DrillStringService } from './drill-string.service';
import { GetDrillStringDto } from './dtos/requests/get-drill-string.dto';
import { DrillStringResponseDto } from './dtos/responses/drill-string.response.dto';
import { CreateDrillStringDto } from './dtos/requests/create-drill-string.dto';
import { UpdateDrillStringDto } from './dtos/requests/update-drill-string.dto';
import { Responder } from '../../common/decorators/responder.decorator';
import { CurrentUser } from '../../common/decorators/current-user.decorator';
import { User } from '../users/entities/user.entity';

@ApiTags('DrillString')
@Controller('drillStrings')
@ApiBearerAuth('access-token')
@RolesDecorator(UserRole.ADMIN, UserRole.SUPERVISOR, UserRole.ENGINEER, UserRole.COMPANY_ADMIN)
@UseGuards(AuthGuard, RolesGuard)
export class DrillStringController {
  constructor(private readonly service: DrillStringService) {}

  @Get()
  @ApiOperation({ description: 'Get drill strings' })
  @Responder.handle('Get drill strings')
  @HttpCode(HttpStatus.OK)
  async findAll(
    @Query() query: GetDrillStringDto,
    @Query() paginationQuery: PaginationDto,
  ): Promise<PaginationResponseDto<DrillStringResponseDto>> {
    const data = await this.service.findAll(query, paginationQuery);
    return toPaginateDtos(DrillStringResponseDto, data);
  }

  @Post()
  @ApiOperation({ description: 'Create drill strings' })
  @Responder.handle('Create drill strings')
  @HttpCode(HttpStatus.CREATED)
  async create(
    @CurrentUser() user: User,
    @Body() data: CreateDrillStringDto,
  ): Promise<DrillStringResponseDto> {
    const value = await this.service.create(user, data);
    return toDto(DrillStringResponseDto, value);
  }

  @Put('/:id')
  @ApiOperation({ description: 'Update drill string' })
  @Responder.handle('Update drill string')
  @HttpCode(HttpStatus.CREATED)
  async update(
    @CurrentUser() user: User,
    @Param('id') id: string,
    @Body() data: UpdateDrillStringDto,
  ): Promise<boolean> {
    return this.service.update(user, id, data);
  }

  @Get('/:id')
  @ApiOperation({ description: 'Get drill string detail' })
  @Responder.handle('Get drill string detail')
  @HttpCode(HttpStatus.OK)
  async detail(@Param('id') id: string): Promise<DrillStringResponseDto> {
    const data = await this.service.findOne(id);
    return toDto(DrillStringResponseDto, data);
  }

  @Delete('/:id')
  @ApiOperation({ description: 'Delete drill string' })
  @Responder.handle('Delete drill string')
  @HttpCode(HttpStatus.OK)
  delete(@CurrentUser() user, @Param('id') id: string): Promise<boolean> {
    return this.service.softDeleteById(user, id);
  }
}
