import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { PaginationResponseDto } from '../../common/dtos/pagination.response.dto';
import { AuthGuard } from '../auth/guards/auth.guard';

import { PaginationDto } from '../../common/dtos/paginationDto';
import { toDto, toPaginateDtos } from '../../common/transformers/dto.transformer';

import { RolesDecorator } from '../auth/decorators/roles.decorator';
import { UserRole } from '../roles/enums/roles.enum';
import { RolesGuard } from '../auth/guards/roles.guard';
import { CustomerService } from './customer.service';
import { CustomerResponseDto } from './dtos/responses/customer.response.dto';
import { Responder } from '../../common/decorators/responder.decorator';
import { GetCustomerQueryDto } from './dtos/requests/get-customer.dto';
import { CurrentUser } from '../../common/decorators/current-user.decorator';
import { User } from '../users/entities/user.entity';
import { CreateCustomerDto } from './dtos/requests/create-customer.dto';
import { UpdateCustomerDto } from './dtos/requests/update-customer.dto';
import { DeleteCustomerQueryDto } from "./dtos/requests/delete-customer.dto";
import { CustomerDetailResponseDto } from "./dtos/responses/customer-detail.response.dto";

@ApiTags('Customer')
@Controller('customers')
@ApiBearerAuth('access-token')
@UseGuards(AuthGuard, RolesGuard)
@RolesDecorator(UserRole.ADMIN, UserRole.ENGINEER, UserRole.SUPERVISOR, UserRole.COMPANY_ADMIN)
export class CustomerController {
  constructor(private readonly service: CustomerService) {}

  @Get()
  @ApiOperation({ description: 'Get customers' })
  @Responder.handle('Get customers')
  @HttpCode(HttpStatus.OK)
  async findAll(
    @Query() query: GetCustomerQueryDto,
    @Query() paginationQuery: PaginationDto,
  ): Promise<PaginationResponseDto<CustomerResponseDto>> {
    const data = await this.service.findAll(query, paginationQuery);
    return toPaginateDtos(CustomerResponseDto, data);
  }

  @Post()
  @ApiOperation({ description: 'Create customer' })
  @Responder.handle('Create customer')
  @HttpCode(HttpStatus.CREATED)
  async create(
    @CurrentUser() user: User,
    @Body() data: CreateCustomerDto,
  ): Promise<CustomerResponseDto> {
    const value = await this.service.create(user, data);
    return toDto(CustomerResponseDto, value);
  }

  @Put('/:id')
  @ApiOperation({ description: 'Update customer' })
  @Responder.handle('Update customer')
  @HttpCode(HttpStatus.CREATED)
  async update(
    @CurrentUser() user: User,
    @Param('id') id: string,
    @Body() data: UpdateCustomerDto,
  ): Promise<CustomerResponseDto> {
    const value = await this.service.update(id, user, data);
    return toDto(CustomerResponseDto, value);
  }

  @Get('/:id')
  @ApiOperation({ description: 'Get customer detail' })
  @Responder.handle('Get customer detail')
  @HttpCode(HttpStatus.OK)
  async detail(@Param('id') id: string): Promise<CustomerDetailResponseDto> {
    const data = await this.service.findOne(id);
    return toDto(CustomerDetailResponseDto, data);
  }

  @Post('/delete')
  @ApiOperation({ description: 'Delete customer' })
  @Responder.handle('Delete customer')
  @HttpCode(HttpStatus.OK)
  delete(@Body() body: DeleteCustomerQueryDto): Promise<boolean> {
    return this.service.softDeleteById(body);
  }
}
