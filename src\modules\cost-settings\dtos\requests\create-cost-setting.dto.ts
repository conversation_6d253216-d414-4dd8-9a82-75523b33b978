import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString, IsUUID } from "class-validator";
import { CostSettingTypeEnum } from '../../enums/cost-setting-type.enum';
import { Transform } from 'class-transformer';

export class CreateCostSettingDto {
  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  cost: number;

  @ApiProperty({
    required: true,
    enum: CostSettingTypeEnum,
    description: 'Product: 1, Service: 2, Engineer: 3',
  })
  @Transform(params => parseInt(params.value))
  @IsNumber()
  type: number;

  @ApiProperty({ required: false, example: 'xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx' })
  @IsUUID()
  @IsOptional()
  @IsString()
  companyId?: string;
}
