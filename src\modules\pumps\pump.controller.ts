import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { PaginationResponseDto } from '../../common/dtos/pagination.response.dto';
import { AuthGuard } from '../auth/guards/auth.guard';
import { PumpResponseDto } from './dtos/responses/pump.response.dto';

import { PaginationDto } from '../../common/dtos/paginationDto';
import { toDto, toPaginateDtos } from '../../common/transformers/dto.transformer';

import { RolesDecorator } from '../auth/decorators/roles.decorator';
import { UserRole } from '../roles/enums/roles.enum';
import { RolesGuard } from '../auth/guards/roles.guard';

import { PumpService } from './pump.service';
import { GetPumpsQueryDto } from './dtos/requests/get-pumps.dto';
import { CreatePumpDto } from './dtos/requests/create-pump.dto';
import { UpdatePumpDto } from './dtos/requests/update-pump.dto';
import { PumpDetailResponseDto } from './dtos/responses/pump-detail.response.dto';
import { Responder } from '../../common/decorators/responder.decorator';
import { CurrentUser } from '../../common/decorators/current-user.decorator';
import { User } from '../users/entities/user.entity';
import { PumpsSummaryResponseDto } from './dtos/responses/pumps-summary.response.dto';

@ApiTags('Pump')
@Controller('pumps')
@ApiBearerAuth('access-token')
@RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN, UserRole.SUPERVISOR, UserRole.ENGINEER)
@UseGuards(AuthGuard, RolesGuard)
export class PumpController {
  constructor(private readonly planService: PumpService) {}

  @Get()
  @ApiOperation({ description: 'Get pumps' })
  @Responder.handle('Get pumps')
  @HttpCode(HttpStatus.OK)
  async findAll(
    @Query() query: GetPumpsQueryDto,
    @Query() paginationQuery: PaginationDto,
  ): Promise<PaginationResponseDto<PumpResponseDto>> {
    const data = await this.planService.findAll(query, paginationQuery);
    return toPaginateDtos(PumpResponseDto, data);
  }

  @Post()
  @ApiOperation({ description: 'Create pump' })
  @Responder.handle('Create pump')
  @HttpCode(HttpStatus.CREATED)
  async create(@CurrentUser() user: User, @Body() data: CreatePumpDto): Promise<PumpResponseDto> {
    const value = await this.planService.create(user, data);
    return toDto(PumpResponseDto, value);
  }

  @Put('/:id')
  @ApiOperation({ description: 'Update pump' })
  @Responder.handle('Update pump')
  @HttpCode(HttpStatus.CREATED)
  async update(
    @CurrentUser() user: User,
    @Param('id') id: string,
    @Body() data: UpdatePumpDto,
  ): Promise<boolean> {
    return this.planService.update(user, id, data);
  }

  @Get('/:id')
  @ApiOperation({ description: 'Get pump detail' })
  @Responder.handle('Get pump detail')
  @HttpCode(HttpStatus.OK)
  async detail(@Param('id') id: string): Promise<PumpDetailResponseDto> {
    const data = await this.planService.findOne(id);
    return toDto(PumpDetailResponseDto, data);
  }

  @Get('/summary/:dailyReportId')
  @ApiOperation({ description: 'Get pump summary' })
  @Responder.handle('Get pump summary')
  @HttpCode(HttpStatus.OK)
  async summaryInfo(
    @Param('dailyReportId') dailyReportId: string,
  ): Promise<PumpsSummaryResponseDto> {
    const data = await this.planService.summaryInfo(dailyReportId);
    return toDto(PumpsSummaryResponseDto, data);
  }

  @Delete('/:id')
  @ApiOperation({ description: 'Delete pump' })
  @Responder.handle('Delete pump')
  @HttpCode(HttpStatus.OK)
  delete(@CurrentUser() user: User, @Param('id') id: string): Promise<boolean> {
    return this.planService.softDeleteById(user, id);
  }
}
