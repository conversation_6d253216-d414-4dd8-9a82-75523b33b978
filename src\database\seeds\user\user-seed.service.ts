import { Injectable } from '@nestjs/common';
import { admins, companyAdmins, engineers, supervisors } from '../datas/users';
import { UserStatus } from '../../../modules/users/enums/statuses.enum';
import { InjectRepository } from '@nestjs/typeorm';
import { User } from '../../../modules/users/entities/user.entity';
import { Repository } from 'typeorm';
import { Company } from '../../../modules/companies/entities/company.entity';
import bcrypt from 'bcryptjs';
import { faker } from '@faker-js/faker';
import { Role } from '../../../modules/roles/entities/role.entity';

@Injectable()
export class UserSeedService {
  constructor(
    @InjectRepository(Role)
    private rolesRepository: Repository<Role>,
    @InjectRepository(User)
    private usersRepository: Repository<User>,
    @InjectRepository(Company)
    private companyRepository: Repository<Company>,
  ) {}

  roles: Role[];

  async run(): Promise<void> {
    const company = await this.companyRepository.findOne({ where: {} });
    this.roles = await this.rolesRepository.find();
    await this.fakeAdmins(company?.id);
    await this.fakeCompanyAdmins(company?.id);
    await this.fakeSupervisors(company?.id);
    await this.fakeEngineers(company?.id);
  }

  private async fakeAdmins(companyId?: string): Promise<void> {
    for (const admin of admins) {
      const user = this.usersRepository.create({
        email: admin.email,
        firstName: faker.person.firstName(),
        lastName: faker.person.lastName(),
        password: await bcrypt.hash(admin.password, 10),
        officePhone: faker.phone.number(),
        mobilePhone: faker.phone.number(),
        address: faker.location.streetAddress(),
        note: faker.lorem.words(),
        status: UserStatus.ACTIVE,
        companyId: companyId,
        assignedDate: new Date(),
        roles: [this.roles[0]],
      });
      await this.usersRepository.save(user);
    }
  }

  private async fakeCompanyAdmins(companyId?: string): Promise<void> {
    for (const admin of companyAdmins) {
      const user = this.usersRepository.create({
        email: admin.email,
        firstName: faker.person.firstName(),
        lastName: faker.person.lastName(),
        password: await bcrypt.hash(admin.password, 10),
        officePhone: faker.phone.number(),
        mobilePhone: faker.phone.number(),
        address: faker.location.streetAddress(),
        note: faker.lorem.words(),
        status: UserStatus.ACTIVE,
        companyId: companyId,
        assignedDate: new Date(),
        roles: [this.roles[3]],
      });
      await this.usersRepository.save(user);
    }
  }

  private async fakeSupervisors(companyId?: string): Promise<void> {
    for (const admin of supervisors) {
      const user = this.usersRepository.create({
        email: admin.email,
        firstName: faker.person.firstName(),
        lastName: faker.person.lastName(),
        password: await bcrypt.hash(admin.password, 10),
        officePhone: faker.phone.number(),
        mobilePhone: faker.phone.number(),
        address: faker.location.streetAddress(),
        note: faker.lorem.words(),
        status: UserStatus.ACTIVE,
        companyId: companyId,
        assignedDate: new Date(),
        roles: [this.roles[1]],
      });
      await this.usersRepository.save(user);
    }
  }

  private async fakeEngineers(companyId?: string): Promise<void> {
    const supervisor = await this.usersRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.roles', 'role')
      .where({email: '<EMAIL>'})
      .getOne();
    for (const admin of engineers) {
      const user = this.usersRepository.create({
        email: admin.email,
        firstName: faker.person.firstName(),
        lastName: faker.person.lastName(),
        password: await bcrypt.hash(admin.password, 10),
        officePhone: faker.phone.number(),
        mobilePhone: faker.phone.number(),
        address: faker.location.streetAddress(),
        note: faker.lorem.words(),
        status: UserStatus.ACTIVE,
        companyId: companyId,
        assignedDate: new Date(),
        roles: [this.roles[2]],
        supervisorId: supervisor?.id,
      });
      await this.usersRepository.save(user);
    }
  }
}
