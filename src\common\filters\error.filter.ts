import { ArgumentsHost, Catch, ExceptionFilter, HttpException, HttpStatus } from '@nestjs/common';
import { I18nContext } from 'nestjs-i18n';
import { HttpResponseError } from '../types/http-response-error';
import { ErrorCode } from '../../errors/error-code';
import { ValidationError } from '../../errors/validation.error';
import { HttpBadRequestError } from '../../errors/bad-request.error';
import { HttpForbiddenError } from '../../errors/forbidden.error';
import { HttpNotFoundError } from '../../errors/not-found.error';
import { HttpUnauthorizedError } from '../../errors/unauthorized.error';
import { HttpDTOValidationError } from '../../errors/dto-validation.error';

@Catch(HttpException)
export class HttpExceptionFilter implements ExceptionFilter {
  isCustomException(error: HttpException) {
    return [
      ValidationError.name,
      HttpBadRequestError.name,
      HttpForbiddenError.name,
      HttpNotFoundError.name,
      HttpUnauthorizedError.name,
    ].includes(error.name);
  }

  isDTOException(error: HttpException) {
    return (
      error.name === HttpDTOValidationError.name &&
      Object.values(error.getResponse()).length &&
      Object.values(error.getResponse())[0]
    );
  }

  catch(exception: HttpException, host: ArgumentsHost) {
    const response = host.switchToHttp().getResponse();
    const exceptionStatus = exception.getStatus() || HttpStatus.INTERNAL_SERVER_ERROR;
    const errorResponse = exception.getResponse() as string;
    const i18n = I18nContext.current();
    let data: HttpResponseError;
    if (this.isCustomException(exception)) {
      data = {
        statusCode: exceptionStatus,
        errorCode: errorResponse,
        message:
          (errorResponse
            ? i18n?.t(`errors.${errorResponse}`)
            : i18n?.t(`errors.${ErrorCode.INTERNAL_ERROR}`)) || '',
      };
    } else if (this.isDTOException(exception)) {
      data = {
        statusCode: HttpStatus.BAD_REQUEST,
        errorCode: 'VALIDATION_ERROR',
        message: Object.values(errorResponse)[0],
      };
    } else {
      data = {
        statusCode: exceptionStatus,
        errorCode: errorResponse,
        message: i18n?.t(`errors.${ErrorCode.INTERNAL_ERROR}`) || '',
      };
    }
    return response.status(exceptionStatus).jsonp(data);
  }
}
