import { Module } from '@nestjs/common';
import { DailyReportSeedService } from './daily-report-seed.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Company } from '../../../modules/companies/entities/company.entity';
import { Well } from '../../../modules/wells/entities/well.entity';
import { DailyReport } from '../../../modules/daily-reports/entities/daily-report.entity';
import { User } from '../../../modules/users/entities/user.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Well, Company, DailyReport, User])],
  providers: [DailyReportSeedService],
  exports: [DailyReportSeedService],
})
export class DailyReportSeedModule {}
