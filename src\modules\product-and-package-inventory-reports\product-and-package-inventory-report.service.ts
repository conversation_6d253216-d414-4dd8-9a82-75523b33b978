import { Injectable } from '@nestjs/common';
import { SelectQueryBuilder } from 'typeorm';
import { CreateProductAndPackageInventoryReportDto } from './dtos/requests/create-product-and-package-inventory-report.dto';
import { PaginationDto } from '../../common/dtos/paginationDto';
import { PaginationResponseDto } from '../../common/dtos/pagination.response.dto';
import { GetProductAndPackageInventoryDto } from './dtos/requests/get-product-and-package-inventory.dto';
import { ProductAndPackageInventoryReportRepository } from './product-and-package-inventory-report.repository';
import { User } from '../users/entities/user.entity';
import { ProductAndPackageInventoryReport } from './entities/product-and-package-inventory-report.entity';
import { ProductAndPackageInventoryItemRepository } from '../product-and-package-inventory-items/product-and-package-inventory-item.repository';
import { VolumeTracking } from '../volum-trackings/entities/volume-tracking.entity';
import { Product } from '../products/entities/product.entity';
import { ProductAndPackageInventoryRepository } from '../product-and-package-inventorys/product-and-package-inventory.repository';
import { ProductAndPackageInventory } from '../product-and-package-inventorys/entities/product-and-package-inventory.entity';
import { HttpBadRequestError } from '../../errors/bad-request.error';
import { ErrorCode } from '../../errors/error-code';
import { DailyReportRepository } from '../daily-reports/daily-report.repository';

@Injectable()
export class ProductAndPackageInventoryReportService {
  constructor(
    private dailyReportRepository: DailyReportRepository,
    private productAndPackageInventoryRepository: ProductAndPackageInventoryRepository,
    private reportRepository: ProductAndPackageInventoryReportRepository,
    private itemRepository: ProductAndPackageInventoryItemRepository,
  ) {}

  async findAll(
    query: GetProductAndPackageInventoryDto,
    paginationQuery: PaginationDto,
  ): Promise<PaginationResponseDto<ProductAndPackageInventoryReport>> {
    const queryBuilder: SelectQueryBuilder<ProductAndPackageInventoryReport> = this.reportRepository
      .createQueryBuilder('productAndPackageInventoryReport')
      .where(
        'productAndPackageInventoryReport.productAndPackageInventoryId = :productAndPackageInventoryId',
        {
          productAndPackageInventoryId: query.productAndPackageInventoryId,
        },
      )
      .leftJoinAndSelect('productAndPackageInventoryReport.product', 'product')
      .orderBy('productAndPackageInventoryReport.createdAt', 'DESC');
    return this.reportRepository.paginate(queryBuilder, paginationQuery);
  }

  async create(
    user: User,
    data: CreateProductAndPackageInventoryReportDto,
  ): Promise<ProductAndPackageInventoryReport> {
    await this.validateProduct(data.productAndPackageInventoryId, data.productId);
    const createdData = this.reportRepository.create(data);
    createdData.product = { id: data.productId } as Product;
    createdData.productAndPackageInventory = {
      id: data.productAndPackageInventoryId,
    } as ProductAndPackageInventory;
    const reportItem = await this.reportRepository.save(createdData);
    const location = { id: data.locationId } as VolumeTracking;
    const initialItemData = {
      productAndPackageInventoryReport: reportItem,
      location,
      type: 1,
      ...data,
    };
    await this.itemRepository.save(this.itemRepository.create(initialItemData));
    await this.updateProductAndPackageInventoryReport(reportItem.id);
    await this.updateProductAndPackageInventory(user, data.productAndPackageInventoryId);
    return reportItem;
  }

  async findOne(id: string): Promise<ProductAndPackageInventoryReport | null> {
    return this.reportRepository
      .createQueryBuilder('productAndPackageInventoryReport')
      .where('productAndPackageInventoryReport.id = :id', { id })
      .leftJoinAndSelect('productAndPackageInventoryReport.items', 'items')
      .leftJoinAndSelect('productAndPackageInventoryReport.product', 'product')
      .leftJoinAndSelect('items.location', 'location')
      .orderBy('items.createdAt', 'DESC')
      .getOne();
  }

  async softDeleteById(user: User, id: string): Promise<boolean> {
    const report = await this.reportRepository.findOne({ where: { id } });
    await this.reportRepository.softDelete({ id });
    if (report?.productAndPackageInventory?.id) {
      await this.updateProductAndPackageInventory(user, report!.productAndPackageInventory!.id!);
    }
    return true;
  }

  async validateProduct(productAndPackageInventoryId: string, productId: string) {
    const report = await this.reportRepository
      .createQueryBuilder('report')
      .where('report.productAndPackageInventoryId = :productAndPackageInventoryId', {
        productAndPackageInventoryId,
      })
      .andWhere('report.productId = :productId', { productId })
      .getOne();
    if (report) {
      throw new HttpBadRequestError(ErrorCode.REPORT_PRODUCT_EXIST);
    }
  }

  async updateProductAndPackageInventoryReport(id: string): Promise<void> {
    const totalCost = await this.itemRepository.getTotalCost(id);
    const totalQty = await this.itemRepository.getTotalQuantity(id);
    await this.reportRepository.update({ id }, { totalCost, quantity: totalQty });
  }

  async updateProductAndPackageInventory(user: User, id: string) {
    const totalProductCost = await this.reportRepository.getTotalCost(id);
    await this.productAndPackageInventoryRepository.update({ id }, { totalCost: totalProductCost });
    const productAndPackageInventory = await this.productAndPackageInventoryRepository.findOne({
      where: { id: id },
      relations: ['dailyReport'],
    });
    if (productAndPackageInventory?.dailyReport.id) {
      await this.dailyReportRepository.updateReportUpdatedBy(
        user,
        productAndPackageInventory!.dailyReport!.id!,
      );
    }
  }
}
