import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { BaseRepository } from '../../common/bases/base.repository';
import { SolidControlEquipmentTime } from './entities/solid-control-equipment-time.entity';

@Injectable()
export class SolidControlEquipmentTimeRepository extends BaseRepository<SolidControlEquipmentTime> {
  constructor(dataSource: DataSource) {
    super(SolidControlEquipmentTime, dataSource);
  }

  async getTotalDurations(solidControlEquipmentId: string): Promise<number> {
    const durations = await this.createQueryBuilder('duration')
      .where('duration.solidControlEquipmentId = :solidControlEquipmentId', {
        solidControlEquipmentId,
      })
      .getMany();
    if (!durations.length) {
      return 0;
    }
    return durations.map(e => e.duration).reduce((a, b) => a + b);
  }
}
