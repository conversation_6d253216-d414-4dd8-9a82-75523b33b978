import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsNotEmpty, IsNumber, IsString, IsUUID } from 'class-validator';

export class UpdateProductAndPackageInventoryItemDto {
  @ApiProperty({ required: true })
  @IsUUID()
  @IsNotEmpty()
  productAndPackageInventoryReportId?: string;

  @ApiProperty({ required: true, example: 'volume tracking id' })
  @IsUUID()
  @IsNotEmpty()
  locationId?: string;

  @ApiProperty({ required: true })
  @IsInt()
  quantity: number;

  @ApiProperty({ required: true })
  @IsNumber()
  cost: number;

  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty()
  bolNo: string;

  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty()
  notes: string;
}
