import { Module } from '@nestjs/common';
import { OpenHoleSeedService } from './open-hole-seed.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DailyReport } from '../../../modules/daily-reports/entities/daily-report.entity';
import { OpenHole } from '../../../modules/open-holes/entities/open-hole.entity';

@Module({
  imports: [TypeOrmModule.forFeature([DailyReport, OpenHole])],
  providers: [OpenHoleSeedService],
  exports: [OpenHoleSeedService],
})
export class OpenHoleSeedModule {}
