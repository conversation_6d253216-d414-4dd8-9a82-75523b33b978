import { Module } from '@nestjs/common';
import { TargetPropertySeedService } from './target-property-seed.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Well } from '../../../modules/wells/entities/well.entity';
import { TargetProperty } from '../../../modules/target-properties/entities/target-property.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Well, TargetProperty])],
  providers: [TargetPropertySeedService],
  exports: [TargetPropertySeedService],
})
export class TargetPropertySeedModule {}
