import { Module } from '@nestjs/common';
import { NozzleSeedService } from './nozzle-seed.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DailyReport } from '../../../modules/daily-reports/entities/daily-report.entity';
import { Nozzle } from '../../../modules/nozzles/entities/nozzle.entity';

@Module({
  imports: [TypeOrmModule.forFeature([DailyReport, Nozzle])],
  providers: [NozzleSeedService],
  exports: [NozzleSeedService],
})
export class NozzleSeedModule {}
