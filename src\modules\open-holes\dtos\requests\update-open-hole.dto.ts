import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString, IsUUID } from 'class-validator';

export class UpdateOpenHoleDto {
  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  dailyReportId?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  insideDiameter?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  measuredDepth?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  washout?: number;
}
