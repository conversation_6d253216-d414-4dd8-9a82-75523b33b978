import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Role } from '../../../modules/roles/entities/role.entity';
import { roles } from '../datas/roles';

@Injectable()
export class RoleSeedService {
  constructor(
    @InjectRepository(Role)
    private roleRepository: Repository<Role>,
  ) {}

  async run(): Promise<void> {
    for (const role of roles) {
      await this.roleRepository.save(
        this.roleRepository.create({
          id: role.id,
          value: role.value,
        }),
      );
    }
  }
}
