import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { BaseRepository } from '../../common/bases/base.repository';
import { WellInformation } from './entities/well-information.entity';

@Injectable()
export class WellInformationRepository extends BaseRepository<WellInformation> {
  constructor(dataSource: DataSource) {
    super(WellInformation, dataSource);
  }
}
