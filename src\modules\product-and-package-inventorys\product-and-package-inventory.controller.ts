import { Controller, Delete, Get, HttpCode, HttpStatus, Param, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { AuthGuard } from '../auth/guards/auth.guard';
import { ProductAndPackageInventoryResponseDto } from './dtos/responses/product-and-package-inventory.response.dto';

import { toDto } from '../../common/transformers/dto.transformer';

import { RolesDecorator } from '../auth/decorators/roles.decorator';
import { UserRole } from '../roles/enums/roles.enum';
import { RolesGuard } from '../auth/guards/roles.guard';
import { ProductAndPackageInventoryService } from './product-and-package-inventory.service';
import { Responder } from '../../common/decorators/responder.decorator';
import { CurrentUser } from '../../common/decorators/current-user.decorator';
import { User } from '../users/entities/user.entity';

@ApiTags('ProductAndPackageInventory')
@Controller('productAndPackageInventories')
@ApiBearerAuth('access-token')
@RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN, UserRole.SUPERVISOR, UserRole.ENGINEER)
@UseGuards(AuthGuard, RolesGuard)
export class ProductAndPackageInventoryController {
  constructor(private readonly service: ProductAndPackageInventoryService) {}

  @Get('/:dailyReportId')
  @ApiOperation({ description: 'Get product and package inventory detail' })
  @Responder.handle('Get product and package inventory detail')
  @HttpCode(HttpStatus.OK)
  async detail(
    @CurrentUser() user: User,
    @Param('dailyReportId') dailyReportId: string,
  ): Promise<ProductAndPackageInventoryResponseDto> {
    const data = await this.service.findOne(user, dailyReportId);
    return toDto(ProductAndPackageInventoryResponseDto, data);
  }

  @Delete('/:id')
  @ApiOperation({ description: 'Delete product and package inventory' })
  @Responder.handle('Delete product and package inventory')
  @HttpCode(HttpStatus.OK)
  delete(@CurrentUser() user: User, @Param('id') id: string): Promise<boolean> {
    return this.service.softDeleteById(user, id);
  }
}
