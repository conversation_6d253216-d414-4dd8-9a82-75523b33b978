import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsNumber, IsOptional, IsString, IsUUID } from 'class-validator';

export class UpdatePumpDto {
  @ApiProperty({ required: true })
  @IsUUID()
  @IsNotEmpty()
  dailyReportId: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  inUse?: boolean;

  @ApiProperty()
  @IsString()
  @IsOptional()
  model?: string;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  linearID?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  rodOD?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  strokeLength?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  efficiency?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  stroke?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  displacement?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  rate?: number;
}
