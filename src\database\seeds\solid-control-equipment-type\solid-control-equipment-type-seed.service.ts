import { Injectable } from '@nestjs/common';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { Company } from '../../../modules/companies/entities/company.entity';
import { SolidControlEquipmentType } from '../../../modules/solid-control-equipment-type/entities/solid-control-equipment-type.entity';

@Injectable()
export class SolidControlEquipmentTypeSeedService {
  constructor(
    @InjectRepository(Company)
    private companyRepository: Repository<Company>,
    @InjectRepository(SolidControlEquipmentType)
    private solidControlEquipmentTypeRepository: Repository<SolidControlEquipmentType>,
  ) {}

  async run(): Promise<void> {
    await this.fakeSolidControlEquipments();
  }

  private async fakeSolidControlEquipments(): Promise<void> {
    const types = ['Shale Shakers', 'Desanders', 'Desilters', 'Mud Cleaners', 'Centrifuges'];
    const company = await this.companyRepository.findOne({ where: {} });
    for (let i = 0; i < types.length; i++) {
      await this.solidControlEquipmentTypeRepository.save(
        this.solidControlEquipmentTypeRepository.create({
          companyId: company?.id,
          name: types[i],
        }),
      );
    }
  }
}
