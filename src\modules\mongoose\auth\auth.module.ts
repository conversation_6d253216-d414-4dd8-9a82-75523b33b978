import { Modu<PERSON> } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { IsExist } from '../../../utils/validators/is-exists.validator';
import { IsNotExist } from '../../../utils/validators/is-not-exists.validator';
import { MailModule } from '../../../mail/mail.module';
import { UsersModule } from '../users/users.module';
import { AuthService } from './auth.service';
import { AuthController } from './auth.controller';

@Module({
  imports: [
    UsersModule,
    JwtModule.register({}),
    MailModule,
  ],
  controllers: [AuthController],
  providers: [
    IsExist,
    IsNotExist,
    AuthService,
  ],
  exports: [AuthService],
})
export class AuthModule {}
 