import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { IsExist } from '../../../utils/validators/is-exists.validator';
import { IsNotExist } from '../../../utils/validators/is-not-exists.validator';
import { MailModule } from '../../../mail/mail.module';
import { UserModule } from '../users/user.module';
import { AuthService } from './auth.service';
import { AuthController } from './auth.controller';

@Module({
  imports: [UserModule, JwtModule.register({}), MailModule],
  controllers: [AuthController],
  providers: [IsExist, IsNotExist, AuthService],
  exports: [AuthService],
})
export class AuthModule {}
