import { Modu<PERSON> } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { IsExist } from '../../../utils/validators/is-exists.validator';
import { IsNotExist } from '../../../utils/validators/is-not-exists.validator';
import { MailModule } from '../../../mail/mail.module';
import { MongoUsersModule } from '../users/users.module';
import { MongoAuthService } from './auth.service';
import { MongoAuthController } from './auth.controller';

@Module({
  imports: [
    MongoUsersModule,
    JwtModule.register({}),
    MailModule,
  ],
  controllers: [MongoAuthController],
  providers: [
    IsExist,
    IsNotExist,
    MongoAuthService,
  ],
  exports: [MongoAuthService],
})
export class MongoAuthModule {}
