import { UserStatus } from '../../../modules/users/enums/statuses.enum';

export const admins = [
  {
    email: '<EMAIL>',
    password: 'B1s@2o2i',
    status: UserStatus.ACTIVE,
  },
];

export const companyAdmins = [
  {
    email: '<EMAIL>',
    password: 'B1s@2o2i',
    status: UserStatus.ACTIVE,
  },
  {
    email: '<EMAIL>',
    password: 'B1s@2o2i',
    status: UserStatus.ACTIVE,
  },
];

export const supervisors = [
  {
    email: '<EMAIL>',
    password: 'B1s@2o2i',
    status: UserStatus.ACTIVE,
  },
  {
    email: '<EMAIL>',
    password: 'B1s@2o2i',
    status: UserStatus.ACTIVE,
  },
  {
    email: '<EMAIL>',
    password: 'B1s@2o2i',
    status: UserStatus.ACTIVE,
  },
  {
    email: '<EMAIL>',
    password: 'B1s@2o2i',
    status: UserStatus.ACTIVE,
  },
  {
    email: '<EMAIL>',
    password: 'B1s@2o2i',
    status: UserStatus.ACTIVE,
  },
  {
    email: '<EMAIL>',
    password: 'B1s@2o2i',
    status: UserStatus.ACTIVE,
  },
  {
    email: '<EMAIL>',
    password: 'B1s@2o2i',
    status: UserStatus.ACTIVE,
  },
  {
    email: '<EMAIL>',
    password: 'B1s@2o2i',
    status: UserStatus.ACTIVE,
  },
  {
    email: '<EMAIL>',
    password: 'B1s@2o2i',
    status: UserStatus.ACTIVE,
  },
  {
    email: '<EMAIL>',
    password: 'B1s@2o2i',
    status: UserStatus.ACTIVE,
  },
  {
    email: '<EMAIL>',
    password: 'B1s@2o2i',
    status: UserStatus.ACTIVE,
  },
  {
    email: '<EMAIL>',
    password: 'B1s@2o2i',
    status: UserStatus.ACTIVE,
  },
];

export const engineers = [
  {
    email: '<EMAIL>',
    password: 'B1s@2o2i',
    status: UserStatus.ACTIVE,
  },
  {
    email: '<EMAIL>',
    password: 'B1s@2o2i',
    status: UserStatus.ACTIVE,
  },
  {
    email: '<EMAIL>',
    password: 'B1s@2o2i',
    status: UserStatus.ACTIVE,
  },
  {
    email: '<EMAIL>',
    password: 'B1s@2o2i',
    status: UserStatus.ACTIVE,
  },
  {
    email: '<EMAIL>',
    password: 'B1s@2o2i',
    status: UserStatus.ACTIVE,
  },
  {
    email: '<EMAIL>',
    password: 'B1s@2o2i',
    status: UserStatus.ACTIVE,
  },
  {
    email: '<EMAIL>',
    password: 'B1s@2o2i',
    status: UserStatus.ACTIVE,
  },
  {
    email: '<EMAIL>',
    password: 'B1s@2o2i',
    status: UserStatus.ACTIVE,
  },
  {
    email: '<EMAIL>',
    password: 'B1s@2o2i',
    status: UserStatus.ACTIVE,
  },
  {
    email: '<EMAIL>',
    password: 'B1s@2o2i',
    status: UserStatus.ACTIVE,
  },
  {
    email: '<EMAIL>',
    password: 'B1s@2o2i',
    status: UserStatus.ACTIVE,
  },
  {
    email: '<EMAIL>',
    password: 'B1s@2o2i',
    status: UserStatus.ACTIVE,
  },
  {
    email: '<EMAIL>',
    password: 'B1s@2o2i',
    status: UserStatus.ACTIVE,
  },
  {
    email: '<EMAIL>',
    password: 'B1s@2o2i',
    status: UserStatus.ACTIVE,
  },
  {
    email: '<EMAIL>',
    password: 'B1s@2o2i',
    status: UserStatus.ACTIVE,
  },
];
