import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm';
import { EntityHelper } from '../../../utils/entity-helper';
import { SolidControlEquipment } from '../../solid-control-equipment/entities/solid-control-equipment.entity';

@Entity()
export class SolidControlEquipmentTime extends EntityHelper {
  @ManyToOne(
    () => SolidControlEquipment,
    solidControlEquipment => solidControlEquipment.durations,
    { nullable: false },
  )
  @JoinColumn({ name: 'solidControlEquipmentId' })
  solidControlEquipment: SolidControlEquipment;

  @Column({ type: 'int', nullable: false })
  duration: number;
}
