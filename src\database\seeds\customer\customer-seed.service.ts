import { Injectable } from '@nestjs/common';
import { faker } from '@faker-js/faker';
import { Repository } from 'typeorm';
import { Customer } from '../../../modules/customers/entities/customer.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Company } from '../../../modules/companies/entities/company.entity';

@Injectable()
export class CustomerSeedService {
  constructor(
    @InjectRepository(Customer)
    private repository: Repository<Customer>,
    @InjectRepository(Company)
    private companyRepository: Repository<Company>,
  ) {}

  async run(): Promise<void> {
    await this.fakeCustomers();
  }

  private async fakeCustomers(): Promise<void> {
    const company = await this.companyRepository.findOne({ where: {} });
    for (let i = 0; i < 10; i++) {
      await this.repository.save(
        this.repository.create({
          customerName: faker.company.name(),
          notes: faker.lorem.text(),
          companyId: company?.id,
        }),
      );
    }
  }
}
