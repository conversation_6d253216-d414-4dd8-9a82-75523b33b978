import { ApiProperty } from '@nestjs/swagger';
import { IsDateString, IsNotEmpty, IsNumber, IsOptional, IsString, IsUUID } from 'class-validator';

export class UpdateWellInformationDto {
  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  dailyReportId?: string;

  @ApiProperty({ example: 'xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx', required: true })
  @IsUUID()
  @IsNotEmpty()
  engineerId?: string;

  @ApiProperty({ example: '2023-12-26 22:30:38.334', description: 'Iso 8601 date string' })
  @IsDateString()
  @IsOptional()
  reportedAt?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  activity?: string;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  measuredDepth?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  trueVerticalDepth?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  inclination?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  azimuth?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  weightOnBit?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  rotaryWeight?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  standOffWeight?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  pullUpWeight?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  revolutionsPerMinute?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  rateOfPenetration?: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  drillingInterval?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  formation?: string;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  depthDrilled?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  totalStringLength?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  totalLength?: number;
}
