import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { PaginationResponseDto } from '../../common/dtos/pagination.response.dto';
import { AuthGuard } from '../auth/guards/auth.guard';

import { PaginationDto } from '../../common/dtos/paginationDto';
import { toDto, toPaginateDtos } from '../../common/transformers/dto.transformer';

import { RolesDecorator } from '../auth/decorators/roles.decorator';
import { UserRole } from '../roles/enums/roles.enum';
import { RolesGuard } from '../auth/guards/roles.guard';
import { CustomerContactResponseDto } from './dtos/responses/customer-contact.response.dto';
import { Responder } from '../../common/decorators/responder.decorator';
import { CurrentUser } from '../../common/decorators/current-user.decorator';
import { User } from '../users/entities/user.entity';
import { GetCustomerContactQueryDto } from './dtos/requests/get-customer-contact.dto';
import { CustomerContactService } from './customer-contact.service';
import { CreateCustomerContactDto } from './dtos/requests/create-customer-contact.dto';
import { UpdateCustomerContactDto } from './dtos/requests/update-customer-contact.dto';
import { DeleteCustomerContactsQueryDto } from "./dtos/requests/delete-customer-contacts.dto";

@ApiTags('Customer Contact')
@Controller('customersContact')
@ApiBearerAuth('access-token')
@UseGuards(AuthGuard, RolesGuard)
@RolesDecorator(UserRole.ADMIN)
export class CustomerContactController {
  constructor(private readonly service: CustomerContactService) {}

  @Get()
  @ApiOperation({ description: 'Get customers contact' })
  @Responder.handle('Get customers contact')
  @HttpCode(HttpStatus.OK)
  async findAll(
    @Query() query: GetCustomerContactQueryDto,
    @Query() paginationQuery: PaginationDto,
  ): Promise<PaginationResponseDto<CustomerContactResponseDto>> {
    const data = await this.service.findAll(query, paginationQuery);
    return toPaginateDtos(CustomerContactResponseDto, data);
  }

  @Post()
  @ApiOperation({ description: 'Create customer contact' })
  @Responder.handle('Create customer contact')
  @HttpCode(HttpStatus.CREATED)
  async create(
    @CurrentUser() user: User,
    @Body() data: CreateCustomerContactDto,
  ): Promise<CustomerContactResponseDto> {
    const value = await this.service.create(user, data);
    return toDto(CustomerContactResponseDto, value);
  }

  @Put('/:id')
  @ApiOperation({ description: 'Update customer contact' })
  @Responder.handle('Update customer contact')
  @HttpCode(HttpStatus.CREATED)
  async update(
    @CurrentUser() user: User,
    @Param('id') id: string,
    @Body() data: UpdateCustomerContactDto,
  ): Promise<CustomerContactResponseDto> {
    const value = await this.service.update(id, user, data);
    return toDto(CustomerContactResponseDto, value);
  }

  @Get('/:id')
  @ApiOperation({ description: 'Get customer detail contact' })
  @Responder.handle('Get customer detail contact')
  @HttpCode(HttpStatus.OK)
  async detail(@Param('id') id: string): Promise<CustomerContactResponseDto> {
    const data = await this.service.findOne(id);
    return toDto(CustomerContactResponseDto, data);
  }

  @Post('/delete')
  @ApiOperation({ description: 'Delete customer contact' })
  @Responder.handle('Delete customer contact')
  @HttpCode(HttpStatus.OK)
  delete(@Body() body: DeleteCustomerContactsQueryDto): Promise<boolean> {
    return this.service.softDeleteById(body);
  }
}
