import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsOptional, Validate } from "class-validator";
import { Transform } from 'class-transformer';
import { lowerCaseTransformer } from 'src/common/transformers/lower-case.transformer';
import { IsEmailUnique } from '../../../../common/decorators';

export class RegisterRequestDto {
  @ApiProperty({ example: '<EMAIL>', required: true })
  @Transform(lowerCaseTransformer)
  @IsNotEmpty()
  @IsEmail()
  @Validate(IsEmailUnique)
  email: string;

  @ApiProperty({ example: 'B1s@2o2i', required: true })
  @IsNotEmpty()
  password: string;

  @ApiProperty({ required: true })
  @IsNotEmpty()
  firstName: string;

  @ApiProperty({ required: true })
  @IsNotEmpty()
  lastName: string;

  @ApiProperty({ required: false })
  @IsNotEmpty()
  @IsOptional()
  companyId: string;
}
