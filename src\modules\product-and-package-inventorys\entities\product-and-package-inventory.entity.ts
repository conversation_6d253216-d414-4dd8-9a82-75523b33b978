import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, OneToMany } from 'typeorm';
import { EntityHelper } from '../../../utils/entity-helper';
import { DailyReport } from '../../daily-reports/entities/daily-report.entity';
import { ProductAndPackageInventoryReport } from '../../product-and-package-inventory-reports/entities/product-and-package-inventory-report.entity';

@Entity()
export class ProductAndPackageInventory extends EntityHelper {
  @ManyToOne(() => DailyReport, { nullable: false })
  @JoinColumn({ name: 'dailyReportId' })
  dailyReport: DailyReport;

  @Column({ type: 'float', nullable: false })
  totalCost: number;

  @Column({ type: 'float', nullable: false })
  totalProductVolume: number;

  @Column({ type: 'float', nullable: false })
  weightMaterials: number;

  @Column({ type: 'float', nullable: false })
  baseFluid: number;

  @Column({ type: 'float', nullable: false })
  addWater: number;

  @Column({ type: 'float', nullable: false })
  totalVolume: number;

  @OneToMany(
    () => ProductAndPackageInventoryReport,
    productAndPackageInventoryReport => productAndPackageInventoryReport.productAndPackageInventory,
  )
  reportItems: ProductAndPackageInventoryReport[];
}
