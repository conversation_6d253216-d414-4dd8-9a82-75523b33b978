import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IsExist } from '../../utils/validators/is-exists.validator';
import { IsNotExist } from '../../utils/validators/is-not-exists.validator';
import { DrillBitService } from './drill-bit.service';
import { JwtModule } from '@nestjs/jwt';
import { DrillBitController } from './drill-bit.controller';
import { DrillBitRepository } from './drill-bit.repository';
import { DrillBit } from './entities/drill-bit.entity';
import { DailyReportRepository } from '../daily-reports/daily-report.repository';

@Module({
  imports: [TypeOrmModule.forFeature([DrillBit]), JwtModule.register({})],
  controllers: [DrillBitController],
  providers: [IsExist, IsNotExist, DrillBitService, DrillBitRepository, DailyReportRepository],
  exports: [DrillBitService],
})
export class DrillBitModule {}
