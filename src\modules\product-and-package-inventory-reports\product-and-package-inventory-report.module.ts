import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IsExist } from '../../utils/validators/is-exists.validator';
import { IsNotExist } from '../../utils/validators/is-not-exists.validator';
import { ProductAndPackageInventoryReportService } from './product-and-package-inventory-report.service';
import { JwtModule } from '@nestjs/jwt';
import { ProductAndPackageInventoryReportRepository } from './product-and-package-inventory-report.repository';
import { ProductAndPackageInventoryReportController } from './product-and-package-inventory-report.controller';
import { ProductAndPackageInventoryReport } from './entities/product-and-package-inventory-report.entity';
import { ProductAndPackageInventoryItemRepository } from '../product-and-package-inventory-items/product-and-package-inventory-item.repository';
import { ProductAndPackageInventoryItem } from '../product-and-package-inventory-items/entities/product-and-package-inventory-item.entity';
import { ProductAndPackageInventoryRepository } from '../product-and-package-inventorys/product-and-package-inventory.repository';
import { DailyReportRepository } from '../daily-reports/daily-report.repository';

@Module({
  imports: [
    TypeOrmModule.forFeature([ProductAndPackageInventoryReport, ProductAndPackageInventoryItem]),
    JwtModule.register({}),
  ],
  controllers: [ProductAndPackageInventoryReportController],
  providers: [
    IsExist,
    IsNotExist,
    DailyReportRepository,
    ProductAndPackageInventoryReportService,
    ProductAndPackageInventoryRepository,
    ProductAndPackageInventoryReportRepository,
    ProductAndPackageInventoryItemRepository,
  ],
  exports: [ProductAndPackageInventoryReportService],
})
export class ProductAndPackageInventoryReportModule {}
