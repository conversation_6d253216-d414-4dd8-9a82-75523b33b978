import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsDateString, IsNumber, IsOptional, IsString, IsUUID } from 'class-validator';
import { IsArray } from '../../../../common/decorators';
import { IsUuidArray } from '../../../../common/decorators/class-validator/is-array-uuid.decorator';

export class UpdateWellDto {
  @ApiProperty({ example: ['xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx'] })
  @IsUuidArray()
  @IsArray()
  @IsOptional()
  userIds?: string[];

  @ApiProperty({ example: ['xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx'] })
  @IsUuidArray()
  @IsArray()
  @IsOptional()
  customerIds?: string[];

  @ApiProperty({ required: false, example: 'xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx' })
  @IsUUID()
  @IsOptional()
  @IsString()
  companyId?: string;

  @ApiProperty({ required: false, example: 'xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx' })
  @IsUUID()
  @IsOptional()
  @IsString()
  stateOrProvinceId?: string;

  @ApiProperty({ required: false, example: 'xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx' })
  @IsUUID()
  @IsOptional()
  @IsString()
  countryId?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  nameOrNo?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  apiWellNo?: string;

  @ApiProperty({ example: -74.194149 })
  @IsOptional()
  @IsNumber()
  latitude?: number;

  @ApiProperty({ example: -74.194149 })
  @IsOptional()
  @IsNumber()
  longitude?: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  fieldOrBlock?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  sectionOrTownshipOrRange?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  countyOrParishOrOffshoreArea?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  rigName?: string;

  @ApiProperty()
  @IsDateString()
  @IsOptional()
  spudDate?: Date;

  @ApiProperty()
  @IsString()
  @IsOptional()
  stockPoint?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  stockPointContact?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  operator?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  contractor?: string;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  kickOffPoint?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  landingPoint?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  seaLevel?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  airGap?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  waterDepth?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  riserId?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  riserOD?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  chokeLineId?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  killLineId?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  boostLineId?: number;

  @ApiProperty({ default: false })
  @IsBoolean()
  @IsOptional()
  rateOfPenetration?: boolean;

  @ApiProperty({ default: false })
  @IsBoolean()
  @IsOptional()
  revolutionsPerMinute?: boolean;

  @ApiProperty({ default: false })
  @IsBoolean()
  @IsOptional()
  eccentricity?: boolean;

  @ApiProperty({ default: false })
  @IsBoolean()
  @IsOptional()
  archived?: boolean;
}
