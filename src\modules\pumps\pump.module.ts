import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IsExist } from '../../utils/validators/is-exists.validator';
import { IsNotExist } from '../../utils/validators/is-not-exists.validator';
import { PumpService } from './pump.service';
import { JwtModule } from '@nestjs/jwt';
import { PumpRepository } from './pump.repository';
import { PumpController } from './pump.controller';
import { Pump } from './entities/pump.entity';
import { DailyReportRepository } from '../daily-reports/daily-report.repository';

@Module({
  imports: [TypeOrmModule.forFeature([Pump]), JwtModule.register({})],
  controllers: [PumpController],
  providers: [IsExist, IsNotExist, PumpService, PumpRepository, DailyReportRepository],
  exports: [PumpService],
})
export class PumpModule {}
