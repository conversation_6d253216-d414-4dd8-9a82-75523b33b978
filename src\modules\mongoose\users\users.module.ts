import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { JwtModule } from '@nestjs/jwt';
import { IsExist } from '../../../utils/validators/is-exists.validator';
import { IsNotExist } from '../../../utils/validators/is-not-exists.validator';
import { IsEmailUnique } from '../../../common/decorators';
import { IsEmailUniqueWithoutSelf } from '../../../common/decorators';
import { MailModule } from '../../../mail/mail.module';
import { MailService } from '../../../mail/mail.service';
import { UserSchema } from './user.schema';
import { MongoUserService } from './user.mongo.service';
import { MongoUserController } from './user.mongo.controller';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: 'User', schema: UserSchema }]),
    JwtModule.register({}),
    MailModule,
  ],
  controllers: [MongoUserController],
  providers: [
    IsExist,
    IsNotExist,
    MongoUserService,
    IsEmailUnique,
    IsEmailUniqueWithoutSelf,
    MailService,
  ],
  exports: [MongoUserService],
})
export class MongoUsersModule {}
