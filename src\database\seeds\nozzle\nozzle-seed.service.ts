import { Injectable } from '@nestjs/common';
import { faker } from '@faker-js/faker';
import { Repository } from 'typeorm';
import { DailyReport } from '../../../modules/daily-reports/entities/daily-report.entity';
import { Nozzle } from '../../../modules/nozzles/entities/nozzle.entity';
import { InjectRepository } from '@nestjs/typeorm';

@Injectable()
export class NozzleSeedService {
  constructor(
    @InjectRepository(DailyReport)
    private dailyReportRepository: Repository<DailyReport>,
    @InjectRepository(Nozzle)
    private nozzleRepository: Repository<Nozzle>,
  ) {}

  async run(): Promise<void> {
    await this.fakeNozzles();
  }

  private async fakeNozzles(): Promise<void> {
    const reports = await this.dailyReportRepository.find({ where: {} });
    for (let i = 0; i < reports.length; i++) {
      for (let j = 0; j < 20; j++) {
        await this.nozzleRepository.save(
          this.nozzleRepository.create({
            dailyReportId: reports[i]?.id,
            identificationNumber: faker.string.alphanumeric(6),
            orificeSize: faker.number.float({ max: 200, precision: 0.01 }),
          }),
        );
      }
    }
  }
}
