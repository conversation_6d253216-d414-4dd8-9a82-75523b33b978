import { Injectable } from '@nestjs/common';
import { ProductRepository } from './product.repository';
import { CreateProductDto } from './dtos/requests/create-product.dto';
import { User } from '../users/entities/user.entity';
import { Product } from './entities/product.entity';
import { UpdateProductDto } from './dtos/requests/update-product.dto';
import { PaginationDto } from '../../common/dtos/paginationDto';
import { PaginationResponseDto } from '../../common/dtos/pagination.response.dto';
import { GetProductsQueryDto } from './dtos/requests/get-products.query.dto';
import { HttpBadRequestError } from "../../errors/bad-request.error";
import { ErrorCode } from "../../errors/error-code";

@Injectable()
export class ProductService {
  constructor(private repository: ProductRepository) {}

  async findAll(
    user: User,
    query: GetProductsQueryDto,
    paginationQuery: PaginationDto,
  ): Promise<PaginationResponseDto<Product>> {
    let queryBuilder = this.repository
      .createQueryBuilder('product')
      .leftJoinAndSelect('product.company', 'company');
    if (query.companyId) {
      queryBuilder = queryBuilder.andWhere('product.companyId = :companyId', { companyId: query.companyId});
    }
    if (query.keyword) {
      queryBuilder = queryBuilder.andWhere('product.name ILIKE :name', {
        name: `%${query.keyword}%`,
      });
    }
    if (query.priceFrom) {
      queryBuilder = queryBuilder.andWhere('product.price >= :priceFrom', {priceFrom: query.priceFrom });
    }
    if (query.priceTo) {
      queryBuilder = queryBuilder.andWhere('product.price <= :priceTo', { priceTo: query.priceTo });
    }
    if (query.sortBy) {
      const sortDirection = query.sortDirection ?? "ASC";
      queryBuilder = queryBuilder
        .addOrderBy(`product.${query.sortBy}`, sortDirection === "DESC" ? "DESC" : "ASC");
    } else {
      queryBuilder = queryBuilder.addOrderBy('product.createdAt', 'DESC');
    }
    return this.repository.paginate(queryBuilder, paginationQuery);
  }

  async create(user: User, data: CreateProductDto): Promise<Product> {
    const existProduct = await this.repository.findOne({where: {name: data.name}});
    if (existProduct) {
      throw new HttpBadRequestError(ErrorCode.PRODUCT_EXIST)
    }
    const product = this.repository.create(data);
    product.companyId = data.companyId ?? user.companyId;
    return this.repository.save(product);
  }

  async update(id: string, data: UpdateProductDto): Promise<boolean> {
    await this.repository.update({ id }, { ...data });
    return true;
  }

  async findOne(id: string): Promise<Product | null> {
    return this.repository.findOne({ where: { id },relations: ['company'] });
  }

  async softDeleteById(id: string): Promise<boolean> {
    const product = await this.repository.findOne({where: {id: id}, relations: ['reports']});
    if (product?.reports.length) {
      throw new HttpBadRequestError(ErrorCode.PRODUCT_BEING_USED);
    }
    await this.repository.softDelete({ id });
    return true;
  }
}
