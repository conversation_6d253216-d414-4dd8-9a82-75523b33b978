import { Controller, Get, HttpCode, HttpStatus, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { AuthGuard } from '../auth/guards/auth.guard';
import { StateResponseDto } from './dtos/responses/state.response.dto';
import { StateService } from './state.service';
import { GetStatesQueryDto } from './dtos/requests/get-states.dto';
import { PaginationDto } from '../../common/dtos/paginationDto';
import { PaginationResponseDto } from '../../common/dtos/pagination.response.dto';
import { toPaginateDtos } from '../../common/transformers/dto.transformer';
import { Responder } from '../../common/decorators/responder.decorator';

@ApiTags('State')
@Controller('states')
@ApiBearerAuth('access-token')
@UseGuards(AuthGuard)
export class StateController {
  constructor(private readonly stateService: StateService) {}

  @Get()
  @ApiOperation({ description: 'Get states' })
  @Responder.handle('Get states')
  @HttpCode(HttpStatus.OK)
  async findAll(
    @Query() query: GetStatesQueryDto,
    @Query() paginationQuery: PaginationDto,
  ): Promise<PaginationResponseDto<StateResponseDto>> {
    const data = await this.stateService.findAll(query, paginationQuery);
    return toPaginateDtos(StateResponseDto, data);
  }
}
