import { Injectable } from '@nestjs/common';
import { faker } from '@faker-js/faker';
import { Repository } from 'typeorm';
import { SolidControlEquipment } from '../../../modules/solid-control-equipment/entities/solid-control-equipment.entity';
import { SolidControlEquipmentInput } from '../../../modules/solid-control-equipment-input/entities/solid-control-equipment-input.entity';
import { InjectRepository } from '@nestjs/typeorm';

@Injectable()
export class SolidControlEquipmentInputSeedService {
  constructor(
    @InjectRepository(SolidControlEquipment)
    private solidControlEquipmentService: Repository<SolidControlEquipment>,
    @InjectRepository(SolidControlEquipmentInput)
    private solidControlEquipmentInputService: Repository<SolidControlEquipmentInput>,
  ) {}

  async run(): Promise<void> {
    await this.fakeSolidControlEquipmentInputs();
  }

  private async fakeSolidControlEquipmentInputs(): Promise<void> {
    const equipments = await this.solidControlEquipmentService.find({ where: {} });
    for (let i = 0; i < equipments.length; i++) {
      await this.solidControlEquipmentInputService.save(
        this.solidControlEquipmentInputService.create({
          solidControlEquipment: { id: equipments[i]?.id },
          description: faker.lorem.text(),
          value: faker.number.float({ max: 200, precision: 0.01 }),
          units: faker.science.unit().name,
        }),
      );
    }
  }
}
