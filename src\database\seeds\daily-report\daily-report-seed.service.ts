import { Injectable } from '@nestjs/common';
import { Repository } from 'typeorm';
import { DailyReport } from '../../../modules/daily-reports/entities/daily-report.entity';
import { Well } from '../../../modules/wells/entities/well.entity';
import { Company } from '../../../modules/companies/entities/company.entity';
import { InjectRepository } from '@nestjs/typeorm';
import moment from 'moment';
import { User } from '../../../modules/users/entities/user.entity';
import { UserRole } from '../../../modules/roles/enums/roles.enum';

@Injectable()
export class DailyReportSeedService {
  constructor(
    @InjectRepository(DailyReport)
    private dailyReportService: Repository<DailyReport>,
    @InjectRepository(Well)
    private wellService: Repository<Well>,
    @InjectRepository(Company)
    private companyService: Repository<Company>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {}

  async run(): Promise<void> {
    await this.fakePlans();
  }

  private async fakePlans(): Promise<void> {
    const wells = await this.wellService.find({ where: {} });
    const company = await this.companyService.findOne({ where: {} });
    const user = await this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.roles', 'role')
      .where('role.value IN (:...roles)', { roles: [UserRole.ENGINEER] })
      .getOne();
    for (let i = 0; i < wells.length; i++) {
      await this.dailyReportService.save(
        this.dailyReportService.create({
          well: { id: wells[i]?.id },
          companyId: company?.id,
          // reportDate: moment().format('yyyy-MM-DD').toString(),
          createdBy: user!,
          updatedBy: user!,
        }),
      );
      await this.dailyReportService.save(
        this.dailyReportService.create({
          well: { id: wells[i]?.id },
          companyId: company?.id,
          // reportDate: moment().format('yyyy-MM-DD').toString(),
          createdBy: user!,
          updatedBy: user!,
        }),
      );
    }
  }
}
