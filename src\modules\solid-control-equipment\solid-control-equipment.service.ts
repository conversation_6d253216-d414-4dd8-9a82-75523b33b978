import { Injectable } from '@nestjs/common';
import { SelectQueryBuilder } from 'typeorm';
import { PaginationDto } from '../../common/dtos/paginationDto';
import { PaginationResponseDto } from '../../common/dtos/pagination.response.dto';

import { SolidControlEquipmentRepository } from './solid-control-equipment.repository';
import { GetSolidControlEquipmentDto } from './dtos/requests/get-solid-control-equipments.dto';
import { SolidControlEquipment } from './entities/solid-control-equipment.entity';
import { CreateSolidControlEquipmentDto } from './dtos/requests/create-solid-control-equipment.dto';
import { SolidControlEquipmentType } from '../solid-control-equipment-type/entities/solid-control-equipment-type.entity';
import { DailyReportRepository } from '../daily-reports/daily-report.repository';
import { User } from '../users/entities/user.entity';
import { UpdateSolidControlEquipmentDto } from './dtos/requests/update-solid-control-equipment.dto';

@Injectable()
export class SolidControlEquipmentService {
  constructor(
    private repository: SolidControlEquipmentRepository,
    private reportRepository: DailyReportRepository,
  ) {}

  async findAll(
    query: GetSolidControlEquipmentDto,
    paginationQuery: PaginationDto,
  ): Promise<PaginationResponseDto<SolidControlEquipment>> {
    const queryBuilder: SelectQueryBuilder<SolidControlEquipment> = this.repository
      .createQueryBuilder('solidControlEquipment')
      .where('solidControlEquipment.dailyReportId = :dailyReportId', {
        dailyReportId: query.dailyReportId,
      })
      .leftJoinAndSelect('solidControlEquipment.type', 'type')
      .leftJoinAndSelect('solidControlEquipment.durations', 'durations')
      .leftJoinAndSelect('solidControlEquipment.inputs', 'inputs')
      .orderBy('solidControlEquipment.createdAt', 'DESC')
      .addOrderBy('type.name', 'ASC');
    return this.repository.paginate(queryBuilder, paginationQuery);
  }

  async create(user: User, data: CreateSolidControlEquipmentDto): Promise<SolidControlEquipment> {
    await this.reportRepository.updateReportUpdatedBy(user, data.dailyReportId);
    const volume = this.repository.create(data);
    volume.type = { id: data.typeId } as SolidControlEquipmentType;
    return this.repository.save(volume);
  }

  async update(user: User, id: string, data: UpdateSolidControlEquipmentDto): Promise<boolean> {
    await this.reportRepository.updateReportUpdatedBy(user, data.dailyReportId);
    const type = { id: data.typeId } as SolidControlEquipmentType;
    const dataUpdate = { type, ...data };
    delete dataUpdate.typeId;
    await this.repository.save({ id, ...dataUpdate });
    return true;
  }

  async findOne(id: string): Promise<SolidControlEquipment | null> {
    return this.repository
      .createQueryBuilder('solidControlEquipment')
      .where('solidControlEquipment.id = :id', { id })
      .leftJoinAndSelect('solidControlEquipment.type', 'type')
      .leftJoinAndSelect('solidControlEquipment.durations', 'durations')
      .leftJoinAndSelect('solidControlEquipment.inputs', 'inputs')
      .orderBy('durations.createdAt', 'DESC')
      .addOrderBy('inputs.createdAt', 'DESC')
      .getOne();
  }

  async softDeleteById(user: User, id: string): Promise<boolean> {
    const item = await this.repository
      .createQueryBuilder('solid')
      .where('solid.id = :id', { id })
      .getOne();
    if (item?.dailyReportId) {
      await this.reportRepository.updateReportUpdatedBy(user, item!.dailyReportId);
    }
    await this.repository.softDelete({ id });
    return true;
  }
}
