import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { UserService } from './user.service';
import { Controller, Get, HttpCode, HttpStatus, Param, UseGuards } from '@nestjs/common';
import { User } from './user.schema';
import { Responder } from 'src/common/decorators/responder.decorator';
import { RolesDecorator } from 'src/modules/auth/decorators/roles.decorator';
import { UserRole } from 'src/modules/roles/enums/roles.enum';
import { AuthGuard } from 'src/modules/auth/guards/auth.guard';
import { RolesGuard } from 'src/modules/auth/guards/roles.guard';
import { CurrentUser } from './current-user.decorator';

@ApiTags('User')
@Controller('users')
@ApiBearerAuth('access-token')
export class UserController {
  constructor(
    private readonly service: UserService,
  ) {}

  @Get()
  @ApiOperation({ description: 'Get users' })
  @Responder.handle('Get users')
  @HttpCode(HttpStatus.OK)
  @RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN, UserRole.SUPERVISOR, UserRole.ENGINEER)
  @UseGuards(AuthGuard, RolesGuard)
  async findAll(): Promise<User[] | null> {
    return this.service.findAll();
  }

  @Get('/:id')
  @ApiOperation({ description: 'Get single user by ID' })
  @Responder.handle('Get user')
  @HttpCode(HttpStatus.OK)
  @RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN, UserRole.SUPERVISOR)
  @UseGuards(AuthGuard, RolesGuard)
  async getUserById(@Param('id') id: string): Promise<User | null> {
    return this.service.findOne(id);
  }

  @Get('/profile')
  @ApiOperation({ description: 'Get my profile' })
  @Responder.handle('Get my user profile')
  @HttpCode(HttpStatus.OK)
  @RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN, UserRole.SUPERVISOR, UserRole.ENGINEER)
  @UseGuards(AuthGuard, RolesGuard)
  async findProfile(@CurrentUser() user: User): Promise<User | null> {
    return this.service.findOne(user.id);
  }
}
