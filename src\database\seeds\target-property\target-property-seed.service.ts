import { Injectable } from '@nestjs/common';
import { FluidTypeEnum } from '../../../modules/samples/enums/fluid-type.enum';
import { faker } from '@faker-js/faker';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { Well } from '../../../modules/wells/entities/well.entity';
import { TargetProperty } from '../../../modules/target-properties/entities/target-property.entity';

@Injectable()
export class TargetPropertySeedService {
  constructor(
    @InjectRepository(Well)
    private wellRepository: Repository<Well>,
    @InjectRepository(TargetProperty)
    private targetPropertyRepository: Repository<TargetProperty>,
  ) {}

  async run(): Promise<void> {
    await this.fakeTargetProperties();
  }

  private async fakeTargetProperties(): Promise<void> {
    const wells = await this.wellRepository.find({ where: {} });
    for (let i = 0; i < wells.length; i++) {
      await this.targetPropertyRepository.save(
        this.targetPropertyRepository.create({
          wellId: wells[i]?.id,
          fluidType: FluidTypeEnum.oil,
          mudWeight: faker.number.float({ max: 200, precision: 0.01 }),
          funnelViscosity: faker.number.float({ max: 200, precision: 0.01 }),
          plasticViscosity: faker.number.float({ max: 200, precision: 0.01 }),
          yieldPoint: faker.number.float({ max: 200, precision: 0.01 }),
          apiFiltrate: faker.number.float({ max: 200, precision: 0.01 }),
          apiCakeThickness: faker.number.float({ max: 200, precision: 0.01 }),
          pH: faker.number.float({ max: 200, precision: 0.01 }),
          mudAlkalinity: faker.number.float({ max: 200, precision: 0.01 }),
          filtrateAlkalinity: faker.number.float({ max: 200, precision: 0.01 }),
          chlorides: faker.number.float({ max: 200, precision: 0.01 }),
          totalHardness: faker.number.float({ max: 200, precision: 0.01 }),
          linearGelStrengthPercent: faker.number.float({ max: 200, precision: 0.01 }),
          rpm: faker.number.float({ max: 200, precision: 0.01 }),
        }),
      );
    }
  }
}
