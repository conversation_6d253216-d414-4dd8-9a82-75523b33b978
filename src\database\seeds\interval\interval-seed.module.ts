import { Module } from '@nestjs/common';
import { IntervalSeedService } from './interval-seed.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Interval } from '../../../modules/intervals/entities/interval.entity';
import { Well } from '../../../modules/wells/entities/well.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Interval, Well])],
  providers: [IntervalSeedService],
  exports: [IntervalSeedService],
})
export class IntervalSeedModule {}
