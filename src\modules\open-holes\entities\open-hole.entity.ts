import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm';
import { EntityHelper } from '../../../utils/entity-helper';
import { DailyReport } from '../../daily-reports/entities/daily-report.entity';

@Entity()
export class OpenHole extends EntityHelper {
  @ManyToOne(() => DailyReport, { nullable: false })
  @JoinColumn({ name: 'dailyReportId' })
  dailyReport: DailyReport;

  @Column({ type: String, nullable: false })
  description: string;

  @Column({ type: 'float', nullable: false })
  insideDiameter: number;

  @Column({ type: 'float', nullable: false })
  measuredDepth: number;

  @Column({ type: 'float', nullable: false })
  washout: number;
}
