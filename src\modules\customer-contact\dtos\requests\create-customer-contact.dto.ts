import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsEmail, IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator';
import { Transform } from 'class-transformer';
import { lowerCaseTransformer } from '../../../../common/transformers/lower-case.transformer';

export class CreateCustomerContactDto {
  @ApiProperty({ required: true })
  @IsUUID()
  @IsNotEmpty()
  customerId: string;

  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ required: true })
  @Transform(lowerCaseTransformer)
  @IsNotEmpty()
  @IsEmail()
  emailAddress: string;

  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty()
  mobilePhone: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  officePhone?: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  address?: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  notes?: string;

  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  primaryContact?: boolean;

  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  notifyOnNewReport?: boolean;
}
