import { Module } from '@nestjs/common';
import { CasedHoleSeedService } from './cased-hole-seed.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CasedHole } from '../../../modules/cased-holes/entities/cased-hole.entity';
import { DailyReport } from '../../../modules/daily-reports/entities/daily-report.entity';

@Module({
  imports: [TypeOrmModule.forFeature([CasedHole, DailyReport])],
  providers: [CasedHoleSeedService],
  exports: [CasedHoleSeedService],
})
export class CasedHoleSeedModule {}
