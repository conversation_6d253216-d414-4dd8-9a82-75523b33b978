import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { WellService } from './well.service';
import { GetWellsQueryDto } from './dtos/requests/get-wells.query.dto';
import { PaginationResponseDto } from '../../common/dtos/pagination.response.dto';
import { CurrentUser } from '../../common/decorators/current-user.decorator';
import { User } from '../users/entities/user.entity';
import { AuthGuard } from '../auth/guards/auth.guard';
import { PaginationDto } from '../../common/dtos/paginationDto';
import { toDto, toPaginateDtos } from '../../common/transformers/dto.transformer';
import { WellResponseDto } from './dtos/responses/well.response.dto';
import { RolesDecorator } from '../auth/decorators/roles.decorator';
import { UserRole } from '../roles/enums/roles.enum';
import { RolesGuard } from '../auth/guards/roles.guard';
import { CreateWellDto } from './dtos/requests/create-well.dto';
import { UpdateWellDto } from './dtos/requests/update-well.dto';
import { Responder } from '../../common/decorators/responder.decorator';
import { WellDetailResponseDto } from './dtos/responses/well-detail.response.dto';

@ApiTags('Well')
@Controller('wells')
@ApiBearerAuth('access-token')
@RolesDecorator(UserRole.ADMIN, UserRole.SUPERVISOR, UserRole.ENGINEER, UserRole.COMPANY_ADMIN)
@UseGuards(AuthGuard, RolesGuard)
export class WellController {
  constructor(private readonly wellService: WellService) {}

  @Get()
  @ApiOperation({ description: 'Get wells' })
  @Responder.handle('Get wells')
  @HttpCode(HttpStatus.OK)
  async findAll(
    @CurrentUser() user: User,
    @Query() query: GetWellsQueryDto,
    @Query() paginationQuery: PaginationDto,
  ): Promise<PaginationResponseDto<WellResponseDto>> {
    const data = await this.wellService.findAll(user, query, paginationQuery);
    return toPaginateDtos(WellResponseDto, data);
  }

  @Post()
  @ApiOperation({ description: 'Create well' })
  @Responder.handle('Create well')
  @HttpCode(HttpStatus.CREATED)
  async create(@CurrentUser() user: User, @Body() data: CreateWellDto): Promise<WellResponseDto> {
    const well = await this.wellService.create(user, data);
    return toDto(WellResponseDto, well);
  }

  @Put('/:id')
  @ApiOperation({ description: 'Update well' })
  @Responder.handle('Update well')
  @HttpCode(HttpStatus.CREATED)
  async update(
    @CurrentUser() user: User,
    @Param('id') id: string,
    @Body() data: UpdateWellDto,
  ): Promise<boolean> {
    return this.wellService.update(id, user, data);
  }

  @Get('/:id')
  @ApiOperation({ description: 'Get well detail' })
  @Responder.handle('Get well detail')
  @HttpCode(HttpStatus.OK)
  async detail(@CurrentUser() user: User, @Param('id') id: string): Promise<WellDetailResponseDto> {
    const data = await this.wellService.findOne(id, user);
    return toDto(WellDetailResponseDto, data);
  }

  @Delete('/:id')
  @ApiOperation({ description: 'Delete well' })
  @Responder.handle('Delete well')
  @HttpCode(HttpStatus.OK)
  delete(@CurrentUser() user: User, @Param('id') id: string): Promise<boolean> {
    return this.wellService.softDeleteById(id);
  }
}
