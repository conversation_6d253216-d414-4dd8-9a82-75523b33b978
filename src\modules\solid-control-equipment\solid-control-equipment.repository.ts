import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { BaseRepository } from '../../common/bases/base.repository';
import { SolidControlEquipment } from './entities/solid-control-equipment.entity';

@Injectable()
export class SolidControlEquipmentRepository extends BaseRepository<SolidControlEquipment> {
  constructor(dataSource: DataSource) {
    super(SolidControlEquipment, dataSource);
  }
}
