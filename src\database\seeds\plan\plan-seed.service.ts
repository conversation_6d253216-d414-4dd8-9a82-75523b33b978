import { Injectable } from '@nestjs/common';
import { faker } from '@faker-js/faker';
import { Repository } from 'typeorm';
import { Plan } from '../../../modules/plans/entities/plan.entity';
import { Well } from '../../../modules/wells/entities/well.entity';
import { InjectRepository } from '@nestjs/typeorm';

@Injectable()
export class PlanSeedService {
  constructor(
    @InjectRepository(Plan)
    private planService: Repository<Plan>,
    @InjectRepository(Well)
    private wellService: Repository<Well>,
  ) {}

  async run(): Promise<void> {
    await this.fakePlans();
  }

  private async fakePlans(): Promise<void> {
    const wells = (await this.wellService.find({})) ?? [];
    for (let i = 0; i < wells.length; i++) {
      await this.planService.save(
        this.planService.create({
          wellId: wells[i].id,
          mudDepth: faker.number.float({ max: 200, precision: 0.01 }),
          day: faker.number.int(20),
          cost: faker.number.float(100000),
        }),
      );
      if (i % 2 == 0) {
        await this.planService.save(
          this.planService.create({
            wellId: wells[i].id,
            mudDepth: faker.number.float({ max: 200, precision: 0.01 }),
            day: faker.number.int(20),
            cost: faker.number.float(100000),
          }),
        );
      }
    }
  }
}
