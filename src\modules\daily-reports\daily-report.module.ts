import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IsExist } from '../../utils/validators/is-exists.validator';
import { IsNotExist } from '../../utils/validators/is-not-exists.validator';
import { DailyReportService } from './daily-report.service';
import { JwtModule } from '@nestjs/jwt';
import { DailyReportRepository } from './daily-report.repository';
import { DailyReportController } from './daily-report.controller';
import { DailyReport } from './entities/daily-report.entity';
import { VolumeTrackingRepository } from '../volum-trackings/volume-tracking.repository';
import { Nozzle } from "../nozzles/entities/nozzle.entity";
import { NozzleRepository } from "../nozzles/nozzle.repository";
import { NozzleService } from "../nozzles/nozzle.service";

@Module({
  imports: [TypeOrmModule.forFeature([DailyReport, Nozzle]), JwtModule.register({})],
  controllers: [DailyReportController],
  providers: [
    IsExist,
    IsNotExist,
    DailyReportService,
    DailyReportRepository,
    VolumeTrackingRepository,
    NozzleRepository,
    NozzleService
  ],
  exports: [DailyReportService],
})
export class DailyReportModule {}
