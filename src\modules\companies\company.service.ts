import { Injectable } from '@nestjs/common';
import { Company } from './entities/company.entity';
import { CreateCompanyDto } from './dtos/requests/create-company.dto';
import { CompanyRepository } from './company.repository';
import { User } from '../users/entities/user.entity';
import { PaginationDto } from '../../common/dtos/paginationDto';
import { Pagination } from '../../common/types/request-response.type';
import { GetCompanyQueryDto } from './dtos/requests/get-company.dto';
import { UpdateCompanyDto } from './dtos/requests/update-company.dto';
import { PaginationResponseDto } from '../../common/dtos/pagination.response.dto';
import { In, SelectQueryBuilder } from 'typeorm';
import { UserRole } from '../roles/enums/roles.enum';
import { UserRepository } from '../users/user.repository';
import { AssignAdminsDto } from './dtos/requests/assign-users.request.dto';
import { RemoveAdminsDto } from './dtos/requests/remove-users.request.dto';
import { GetCompanyUsersDto } from './dtos/requests/get-company-users.dto';
import { RoleRepository } from '../roles/role.repository';
import { HttpBadRequestError } from '../../errors/bad-request.error';
import { ErrorCode } from '../../errors/error-code';

@Injectable()
export class CompanyService {
  constructor(
    private repository: CompanyRepository,
    private userRepository: UserRepository,
    private roleRepository: RoleRepository,
  ) {}

  async findAll(
    user: User,
    query: GetCompanyQueryDto,
    paginationQuery: PaginationDto,
  ): Promise<Pagination<Company>> {
    const { keyword } = query;
    let builder = this.repository.createQueryBuilder('company');
    keyword && (builder = builder.where('company.name ILIKE :name', { name: `%${keyword}%` }));
    builder = builder
      .leftJoinAndSelect('company.users', 'user')
      .leftJoinAndSelect('user.roles', 'role');
    if (query.sortBy) {
      const sortDirection = query.sortDirection ?? "ASC";
      builder = builder
        .orderBy(`company.${query.sortBy}`, sortDirection === "DESC" ? "DESC" : "ASC");
    } else {
      builder = builder
        .orderBy(`company.createdAt`, 'DESC');
    }
    return this.repository.paginate(builder, paginationQuery);
  }

  async create(data: CreateCompanyDto): Promise<Company> {
    const company = this.repository.create(data);
    // company.users = [user];
    return this.repository.save(company);
  }

  async update(id: string, data: UpdateCompanyDto): Promise<Company> {
    return await this.repository.save({ id, ...data });
  }

  async findOne(id: string): Promise<Company | null> {
    return this.repository.findOne({ where: { id } });
  }

  async softDeleteById(id: string): Promise<boolean> {
    await this.repository.softDelete({ id });
    return true;
  }

  async getUsers(
    user: User,
    query: GetCompanyUsersDto,
    paginationQuery: PaginationDto,
  ): Promise<PaginationResponseDto<User>> {
    let queryBuilder: SelectQueryBuilder<User> = this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.roles', 'role')
      .where({ companyId: query.companyId })
      .andWhere('role.value IN (:...roles)', { roles: [query.role] });
    if (query.name) {
      queryBuilder = queryBuilder
        .andWhere("CONCAT(user.firstName, ' ', user.lastName) ILIKE :name", { name: `%${query.name}%` });
    }
    if (query.sortBy) {
      const sortDirection = query.sortDirection ?? 'ASC';
      if (query.sortBy === 'name') {
        queryBuilder = queryBuilder
          .orderBy('user.firstName', sortDirection === 'DESC' ? 'DESC' : 'ASC') // Sort by first name
          .addOrderBy('user.lastName', sortDirection === 'DESC' ? 'DESC' : 'ASC');
      } else {
        queryBuilder = queryBuilder.orderBy(
          `user.${query.sortBy}`,
          sortDirection === 'DESC' ? 'DESC' : 'ASC',
        );
      }
    } else {
      queryBuilder = queryBuilder.orderBy('user.assignedDate', 'DESC');
    }
    return this.userRepository.paginate(queryBuilder, paginationQuery);
  }

  async addUsers(body: AssignAdminsDto): Promise<Boolean> {
    if (body.role == UserRole.ADMIN) {
      throw new HttpBadRequestError(ErrorCode.NOT_PERMITTED_ROLE);
    }
    const users = await this.userRepository.find({
      where: { id: In(body.userIds) },
      relations: ['roles'],
    });
    const newRole = await this.roleRepository.findOne({ where: { value: body.role } });
    for (const item of users) {
      const roles = item.roles;
      if (!roles?.map(e => e.value).includes(body.role)) {
        roles?.push(newRole!);
      }
      await this.userRepository.save({
        id: item.id,
        companyId: body.companyId,
        assignedDate: new Date(),
        roles,
      });
    }
    return true;
  }

  async removeUsers(body: RemoveAdminsDto): Promise<Boolean> {
    const users = await this.userRepository.find({
      where: { id: In(body.userIds) },
      relations: ['roles'],
    });
    for (const item of users) {
      const roles = item.roles?.filter(e => e.value != body.role);
      await this.userRepository.save({ id: item.id, roles });
    }
    return true;
  }
}
