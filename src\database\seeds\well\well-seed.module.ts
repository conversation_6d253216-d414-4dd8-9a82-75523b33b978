import { Module } from '@nestjs/common';
import { WellSeedService } from './well-seed.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from '../../../modules/users/entities/user.entity';
import { Company } from '../../../modules/companies/entities/company.entity';
import { Customer } from '../../../modules/customers/entities/customer.entity';
import { State } from '../../../modules/states/entities/state.entity';
import { Country } from '../../../modules/countries/entities/country.entity';
import { Well } from '../../../modules/wells/entities/well.entity';

@Module({
  imports: [TypeOrmModule.forFeature([User, Company, Customer, State, Country, Well])],
  providers: [WellSeedService],
  exports: [WellSeedService],
})
export class WellSeedModule {}
