import { Exclude, Expose, Type } from 'class-transformer';
import { RoleResponseDto } from '../../../roles/dtos/responses/role.response.dto.js';
import { CompanyWithUserResponseDto } from "./company-user.response.dto";
import { UserSortResponseDto } from "./user-sort.response.dto";

@Exclude()
export class UserProfileFullResponseDto {
  @Expose()
  id: string;

  @Expose()
  email: string;

  @Expose()
  firstName: string;

  @Expose()
  lastName: string;

  @Expose()
  companyId: string;

  @Expose()
  officePhone: string;

  @Expose()
  mobilePhone: string;

  @Expose()
  address: string;

  @Expose()
  note: string;

  @Expose()
  avatar?: string;

  @Expose()
  @Type(() => CompanyWithUserResponseDto)
  company: CompanyWithUserResponseDto;

  @Expose()
  @Type(() => UserSortResponseDto)
  supervisor: UserSortResponseDto;

  @Expose()
  @Type(() => RoleResponseDto)
  roles: RoleResponseDto[];

  @Expose()
  status: number;
}
