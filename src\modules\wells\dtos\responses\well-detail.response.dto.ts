import { Exclude, Expose, Type } from 'class-transformer';
import { LatestReportInfoResponseDto } from './latest-report-info.response.dto';
import { CustomerInfoResponseDto } from './customer-info.response.dto';
import { EngineerInfoResponseDto } from './engineer-info.response.dto';
import { CompanyResponseDto } from "../../../companies/dtos/responses/company.response.dto";

@Exclude()
export class WellDetailResponseDto {
  @Expose()
  id: string;

  @Expose()
  stateOrProvinceId: string;

  @Expose()
  countryId: string;

  @Expose()
  nameOrNo: string;

  @Expose()
  apiWellNo: string;

  @Expose()
  latitude: number;

  @Expose()
  longitude: number;

  @Expose()
  fieldOrBlock: string;

  @Expose()
  sectionOrTownshipOrRange: string;

  @Expose()
  countyOrParishOrOffshoreArea: string;

  @Expose()
  rigName: string;

  @Expose()
  spudDate: Date;

  @Expose()
  stockPoint: string;

  @Expose()
  stockPointContact: string;

  @Expose()
  operator: string;

  @Expose()
  contractor: string;

  @Expose()
  kickOffPoint: number;

  @Expose()
  landingPoint: number;

  @Expose()
  seaLevel: number;

  @Expose()
  airGap: number;

  @Expose()
  waterDepth: number;

  @Expose()
  riserId: number;

  @Expose()
  riserOD: number;

  @Expose()
  chokeLineId: number;

  @Expose()
  killLineId: number;

  @Expose()
  boostLineId: number;

  @Expose()
  rateOfPenetration: boolean;

  @Expose()
  revolutionsPerMinute: boolean;

  @Expose()
  eccentricity: boolean;

  @Expose()
  archived: boolean;

  @Expose()
  @Type(() => CompanyResponseDto)
  company: CompanyResponseDto;

  @Expose()
  @Type(() => EngineerInfoResponseDto)
  users: EngineerInfoResponseDto[];

  @Expose()
  @Type(() => CustomerInfoResponseDto)
  customers: CustomerInfoResponseDto[];

  @Expose()
  @Type(() => LatestReportInfoResponseDto)
  dailyReport: LatestReportInfoResponseDto;
}
