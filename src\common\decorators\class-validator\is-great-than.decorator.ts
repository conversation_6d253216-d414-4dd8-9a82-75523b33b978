import { registerDecorator, ValidationArguments, ValidationOptions } from 'class-validator';

export function IsGreatThanField(property: string, validationOptions?: ValidationOptions) {
  return (object: any, propertyName: string) => {
    registerDecorator({
      name: 'IsGreatThanField',
      target: object.constructor,
      propertyName,
      constraints: [property],
      options: Object.assign(
        {
          message: `${propertyName} must be great than ${property}`,
        },
        validationOptions,
      ),
      validator: {
        validate(value: any, args: ValidationArguments) {
          const [relatedPropertyName] = args.constraints;
          const relatedValue = (args.object as any)[relatedPropertyName];
          const valueNumber = Number(value);
          const relatedValueNumber = Number(relatedValue);
          return (
            !isNaN(valueNumber) && !isNaN(relatedValueNumber) && valueNumber > relatedValueNumber
          );
        },
      },
    });
  };
}
