import { MigrationInterface, QueryRunner, Table } from 'typeorm';
import {
  columnCreatedAt,
  columnDeletedAt,
  columnId,
  columnUpdatedAt,
} from '../constants/columns.constant';

export class CreateSolidTable1702441958524 implements MigrationInterface {
  private tableName = 'solid';
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: this.tableName,
        columns: [
          columnId,
          {
            name: 'dailyReportId',
            type: 'uuid',
            isNullable: false,
          },
          {
            name: 'shaleCEC',
            type: 'float8',
            isNullable: true,
          },
          {
            name: 'bentCEC',
            type: 'float8',
            isNullable: true,
          },
          {
            name: 'highGelStrength',
            type: 'float8',
            isNullable: true,
          },
          {
            name: 'linearGelStrength',
            type: 'float8',
            isNullable: true,
          },
          columnCreatedAt,
          columnUpdatedAt,
          columnDeletedAt,
        ],
      }),
      false,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable(this.tableName);
  }
}
