import { registerAs } from '@nestjs/config';
import { IsBoolean, IsEmail, IsInt, IsOptional, IsString, Max, Min } from 'class-validator';
import validateConfig from 'src/utils/validate-config';

class EnvironmentVariablesValidator {
  @IsInt()
  @Min(0)
  @Max(65535)
  @IsOptional()
  MAIL_PORT: number;

  @IsString()
  MAIL_HOST: string;

  @IsString()
  @IsOptional()
  MAIL_USER: string;

  @IsString()
  @IsOptional()
  MAIL_PASSWORD: string;

  @IsEmail()
  MAIL_DEFAULT_EMAIL: string;

  @IsString()
  MAIL_DEFAULT_NAME: string;

  @IsBoolean()
  MAIL_SECURE: boolean;

  @IsString()
  @IsOptional()
  MAIL_DEV_SUPPORT: string;

  @IsString()
  @IsOptional()
  MAIL_PROD_SUPPORT: string;

  @IsString()
  @IsOptional()
  MAILJET_API_KEY: string;

  @IsString()
  @IsOptional()
  MAILJET_SECRET_KEY: string;
}

export default registerAs<MailConfig>('mail', () => {
  validateConfig(process.env, EnvironmentVariablesValidator);

  return {
    port: process.env.MAIL_PORT ? parseInt(process.env.MAIL_PORT, 10) : 587,
    host: process.env.MAIL_HOST,
    user: process.env.MAIL_USER,
    password: process.env.MAIL_PASSWORD,
    defaultEmail: process.env.MAIL_DEFAULT_EMAIL,
    defaultName: process.env.MAIL_DEFAULT_NAME,
    secure: process.env.MAIL_SECURE === 'true',
    devSupport: process.env.MAIL_DEV_SUPPORT,
    prodSupport: process.env.MAIL_PROD_SUPPORT,
    mailjetApiKey: process.env.MAILJET_API_KEY,
    mailjetSecretKey: process.env.MAILJET_SECRET_KEY,
  };
});

export type MailConfig = {
  port: number;
  host?: string;
  user?: string;
  password?: string;
  defaultEmail?: string;
  defaultName?: string;
  secure: boolean;
  devSupport?: string;
  prodSupport?: string;
  mailjetApiKey?: string;
  mailjetSecretKey?: string;
};
