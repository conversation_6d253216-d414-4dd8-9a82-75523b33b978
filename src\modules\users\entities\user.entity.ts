import {
  Column,
  Entity,
  ManyToOne,
  JoinColumn,
  ManyToMany,
  OneToMany,
  JoinTable,
  CreateDateColumn,
} from 'typeorm';
import { EntityHelper } from '../../../utils/entity-helper';
import { UserStatus } from '../enums/statuses.enum';
import { Company } from '../../companies/entities/company.entity';
import { Well } from '../../wells/entities/well.entity';
import { WellInformation } from '../../well-informations/entities/well-information.entity';
import { Role } from '../../roles/entities/role.entity';

@Entity()
export class User extends EntityHelper {
  @ManyToOne(() => Company, { nullable: true })
  @JoinColumn({ name: 'companyId' })
  @Column({ type: String, nullable: true })
  companyId: string | null;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'supervisorId' })
  @Column({ type: String, nullable: true })
  supervisorId: string | null;

  @ManyToOne(() => Company, company => company.users)
  company: Company;

  @Column({ type: String, nullable: false })
  email: string;

  @Column({ type: String, nullable: true })
  firstName?: string;

  @Column({ type: String, nullable: true })
  lastName?: string;

  @Column({ type: String, nullable: false })
  password: string;

  @Column({ type: String, nullable: true })
  officePhone?: string;

  @Column({ type: String, nullable: true })
  mobilePhone?: string;

  @Column({ type: String, nullable: true })
  address?: string;

  @Column({ type: String, nullable: true })
  note?: string;

  @Column({ nullable: false })
  status: UserStatus;

  @Column({ nullable: true })
  avatar?: string;

  @Column({ type: String, nullable: true })
  confirmCode?: string;

  @Column({ type: 'timestamp', nullable: true })
  expiredConfirmCode?: Date;

  @ManyToMany(() => Well, well => well.users)
  wells?: Well[] | null;

  @OneToMany(() => WellInformation, wellInfo => wellInfo.engineer)
  wellInformation?: WellInformation[] | null;

  @ManyToMany(() => Role, role => role.users)
  @JoinTable({
    name: 'user_role',
    joinColumn: {
      name: 'userId',
      referencedColumnName: 'id',
    },
    inverseJoinColumn: {
      name: 'roleId',
      referencedColumnName: 'id',
    },
  })
  roles?: Role[];

  @ManyToOne(() => User, user => user.subordinates, { nullable: true })
  supervisor: User | null;

  @OneToMany(() => User, user => user.supervisor)
  subordinates: User[] | null;

  @CreateDateColumn({ type: 'timestamp' })
  assignedDate: Date;
}
