import { Module } from '@nestjs/common';
import { PlanSeedService } from './plan-seed.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Plan } from '../../../modules/plans/entities/plan.entity';
import { Well } from '../../../modules/wells/entities/well.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Plan, Well])],
  providers: [PlanSeedService],
  exports: [PlanSeedService],
})
export class PlanSeedModule {}
