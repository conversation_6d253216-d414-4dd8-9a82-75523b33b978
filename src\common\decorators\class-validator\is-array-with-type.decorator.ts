import { registerDecorator, ValidationOptions } from 'class-validator';
import { isArray } from 'util';

export function IsArray(dataType: string = 'string', validationOptions?: ValidationOptions) {
  return (object: any, propertyName: string) => {
    registerDecorator({
      name: 'IsArray',
      target: object.constructor,
      propertyName,
      constraints: [],
      options: Object.assign(
        {
          message: `${propertyName} must be an array ${dataType}`,
        },
        validationOptions,
      ),
      validator: {
        validate(value: any) {
          const array = isArray(value);
          if (!array) {
            return false;
          }
          const values = value as [];
          return !values.some(item => {
            switch (dataType) {
              case 'number':
                return isNaN(Number(item));
              case 'boolean':
                return !(item === 'true' || item === 'false' || item === true || item === false);
            }
            return !(typeof item === dataType);
          });
        },
      },
    });
  };
}
