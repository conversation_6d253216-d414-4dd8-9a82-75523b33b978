import { MigrationInterface, QueryRunner, Table } from 'typeorm';
import {
  columnCreatedAt,
  columnDeletedAt,
  columnId,
  columnUpdatedAt,
} from '../constants/columns.constant';

export class CreateProductAndPackageInventoryReportTable1702442058853
  implements MigrationInterface
{
  private tableName = 'product_and_package_inventory_report';
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: this.tableName,
        columns: [
          columnId,
          {
            name: 'productAndPackageInventoryId',
            type: 'uuid',
            isNullable: false,
          },
          {
            name: 'productId',
            type: 'uuid',
            isNullable: false,
          },
          {
            name: 'totalCost',
            type: 'float8',
            isNullable: true,
          },
          {
            name: 'quantity',
            type: 'int',
            isNullable: true,
          },
          columnCreatedAt,
          columnUpdatedAt,
          columnDeletedAt,
        ],
      }),
      false,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable(this.tableName);
  }
}
