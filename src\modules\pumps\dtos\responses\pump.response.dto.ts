import { Exclude, Expose } from 'class-transformer';

@Exclude()
export class PumpResponseDto {
  @Expose()
  id: string;

  @Expose()
  description: string;

  @Expose()
  inUse: boolean;

  @Expose()
  model: string;

  @Expose()
  linearID: number;

  @Expose()
  rodOD: number;

  @Expose()
  strokeLength: number;

  @Expose()
  efficiency: number;

  @Expose()
  stroke: number;

  @Expose()
  displacement: number;

  @Expose()
  rate: number;

  @Expose()
  totalDurations: number;
}
