import { Body, Controller, HttpCode, HttpStatus, Post, UseGuards } from '@nestjs/common';
import { AuthService } from './auth.service';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { AuthLoginRequestDto } from './dto/requests/auth-login.request.dto';
import { toDto } from '../../common/transformers/dto.transformer';
import { AuthLoginResponseDto } from './dto/responses/auth-login.response.dto';
import { ForgotPasswordRequestDto } from './dto/requests/forgot-password.request.dto';
import { ForgotPasswordResponseDto } from './dto/responses/forgot-password.response.dto';
import { ValidateOtpRequestDto } from './dto/requests/validate-otp.request.dto';
import { ValidateOtpResponseDto } from './dto/responses/validate-otp.response.dto';
import { ResetPasswordRequestDto } from './dto/requests/reset-password.request.dto';
import { ResetPasswordResponseDto } from './dto/responses/reset-password.response.dto';
import { AuthGuard } from './guards/auth.guard';
import { Responder } from '../../common/decorators/responder.decorator';
import { RegisterRequestDto } from './dto/requests/register.request.dto';
import { UserProfileAdditionInfoResponseDto } from "../users/dtos/responses/user-profile-addition-info.response.dto";

@ApiTags('Auth')
@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('/login')
  @ApiOperation({ description: 'Login' })
  @Responder.handle('Login')
  @HttpCode(HttpStatus.OK)
  async login(@Body() loginDto: AuthLoginRequestDto): Promise<AuthLoginResponseDto> {
    const response = await this.authService.login(loginDto);
    return toDto(AuthLoginResponseDto, response);
  }

  @ApiBearerAuth()
  @Post('/logout')
  @Responder.handle('Logout')
  @UseGuards(AuthGuard)
  @HttpCode(HttpStatus.NO_CONTENT)
  public async logout(): Promise<void> {
    await this.authService.logout();
  }

  @Post('/forgot-password')
  @Responder.handle('Forgot Password')
  @HttpCode(HttpStatus.OK)
  async forgotPassword(
    @Body() forgotDto: ForgotPasswordRequestDto,
  ): Promise<ForgotPasswordResponseDto> {
    const response = await this.authService.forgotPassword(forgotDto);
    return toDto(ForgotPasswordResponseDto, response);
  }

  @Post('/validate-otp')
  @Responder.handle('Validate OTP')
  @HttpCode(HttpStatus.OK)
  async validateOtp(
    @Body() validateOtpDto: ValidateOtpRequestDto,
  ): Promise<ValidateOtpResponseDto> {
    const response = await this.authService.validateOtp(validateOtpDto);
    let message: string;
    let status: string;
    if (!response) {
      message = 'Invalid or expired OTP';
      status = 'false';
    } else {
      message = 'OTP is valid';
      status = 'true';
    }
    return toDto(ValidateOtpResponseDto, { message, status });
  }

  @Post('/reset-password')
  @Responder.handle('Reset Password')
  @HttpCode(HttpStatus.OK)
  async resetPassword(
    @Body() resetPasswordDto: ResetPasswordRequestDto,
  ): Promise<ResetPasswordResponseDto> {
    let message: string;
    let status: string;
    const { email, otp, newPassword } = resetPasswordDto;
    const validate = { email, otp };
    const isValid = await this.authService.validateOtp(validate);
    if (!isValid) {
      message = 'Invalid or expired OTP';
      status = 'false';
    } else {
      await this.authService.resetPassword(email, newPassword);
      message = 'Password has been reset';
      status = 'true';
    }
    return toDto(ResetPasswordResponseDto, { message, status });
  }

  @Post('/register')
  @Responder.handle('Register')
  @HttpCode(HttpStatus.OK)
  async register(@Body() registerRequestDto: RegisterRequestDto): Promise<UserProfileAdditionInfoResponseDto> {
    const data = await this.authService.register(registerRequestDto);
    return toDto(UserProfileAdditionInfoResponseDto, data);
  }
}
