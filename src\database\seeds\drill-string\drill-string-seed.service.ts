import { Injectable } from '@nestjs/common';
import { faker } from '@faker-js/faker';
import { Repository } from 'typeorm';
import { DailyReport } from '../../../modules/daily-reports/entities/daily-report.entity';
import { DrillString } from '../../../modules/drill-strings/entities/drill-string.entity';
import { InjectRepository } from '@nestjs/typeorm';

@Injectable()
export class DrillStringSeedService {
  constructor(
    @InjectRepository(DailyReport)
    private dailyReportRepository: Repository<DailyReport>,
    @InjectRepository(DrillString)
    private drillStringRepository: Repository<DrillString>,
  ) {}

  async run(): Promise<void> {
    await this.fakeDrillStrings();
  }

  private async fakeDrillStrings(): Promise<void> {
    const reports = await this.dailyReportRepository.find({ where: {} });
    for (let i = 0; i < reports.length; i++) {
      for (let j = 0; j < 20; j++) {
        await this.drillStringRepository.save(
          this.drillStringRepository.create({
            dailyReportId: reports[i]?.id,
            description: faker.lorem.text(),
            outsideDiameter: faker.number.float({ max: 200, precision: 0.01 }),
            weight: faker.number.float({ max: 200, precision: 0.01 }),
            insideDiameter: faker.number.float({ max: 200, precision: 0.01 }),
            length: faker.number.float({ max: 200, precision: 0.01 }),
          }),
        );
      }
    }
  }
}
