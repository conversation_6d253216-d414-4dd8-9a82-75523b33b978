import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IsExist } from '../../utils/validators/is-exists.validator';
import { IsNotExist } from '../../utils/validators/is-not-exists.validator';
import { WellService } from './well.service';
import { Well } from './entities/well.entity';
import { WellController } from './well.controller';
import { JwtModule } from '@nestjs/jwt';
import { WellRepository } from './well.repository';
import { UserRepository } from '../users/user.repository';

@Module({
  imports: [TypeOrmModule.forFeature([Well]), JwtModule.register({})],
  controllers: [WellController],
  providers: [IsExist, IsNotExist, WellService, WellRepository, UserRepository],
  exports: [WellService],
})
export class WellModule {}
