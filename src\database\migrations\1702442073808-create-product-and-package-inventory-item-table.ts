import { MigrationInterface, QueryRunner, Table } from 'typeorm';
import {
  columnCreatedAt,
  columnDeletedAt,
  columnId,
  columnUpdatedAt,
} from '../constants/columns.constant';

export class CreateProductAndPackageInventoryItemTable1702442073808 implements MigrationInterface {
  private tableName = 'product_and_package_inventory_item';
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: this.tableName,
        columns: [
          columnId,
          {
            name: 'productAndPackageInventoryReportId',
            type: 'uuid',
            isNullable: true,
          },
          {
            name: 'locationId',
            type: 'uuid',
            isNullable: true,
          },
          {
            name: 'type',
            type: 'int',
            isNullable: false,
          },
          {
            name: 'quantity',
            type: 'int',
            isNullable: true,
          },
          {
            name: 'cost',
            type: 'float8',
            isNullable: true,
          },
          {
            name: 'bolNo',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'notes',
            type: 'text',
            isNullable: true,
          },
          columnCreatedAt,
          columnUpdatedAt,
          columnDeletedAt,
        ],
      }),
      false,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable(this.tableName);
  }
}
