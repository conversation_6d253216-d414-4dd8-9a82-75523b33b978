import { MigrationInterface, QueryRunner, Table } from 'typeorm';
import {
  columnCreatedAt,
  columnDeletedAt,
  columnId,
  columnUpdatedAt,
} from '../constants/columns.constant';

export class CreateVolumeTrackingTable1702442112932 implements MigrationInterface {
  private tableName = 'volume_tracking';
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: this.tableName,
        columns: [
          columnId,
          {
            name: 'dailyReportId',
            type: 'uuid',
            isNullable: false,
          },
          {
            name: 'name',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'storageType',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'status',
            type: 'int',
            isNullable: true,
          },
          {
            name: 'measuredVolume',
            type: 'float8',
            isNullable: true,
          },
          {
            name: 'mudWeight',
            type: 'float8',
            isNullable: true,
          },
          {
            name: 'mudType',
            type: 'float8',
            isNullable: true,
          },
          {
            name: 'totalAdditions',
            type: 'float8',
            isNullable: true,
          },
          {
            name: 'totalLosses',
            type: 'float8',
            isNullable: true,
          },
          {
            name: 'totalTransfers',
            type: 'float8',
            isNullable: true,
          },
          {
            name: 'calculatedVolume',
            type: 'float8',
            isNullable: true,
          },
          columnCreatedAt,
          columnUpdatedAt,
          columnDeletedAt,
        ],
      }),
      false,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable(this.tableName);
  }
}
