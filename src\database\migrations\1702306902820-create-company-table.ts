import { MigrationInterface, QueryRunner, Table } from 'typeorm';
import {
  columnCreatedAt,
  columnDeletedAt,
  columnId,
  columnUpdatedAt,
} from '../constants/columns.constant';

export class CreateCompanyTable1702306902820 implements MigrationInterface {
  private tableName = 'company';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: this.tableName,
        columns: [
          columnId,
          {
            name: 'name',
            type: 'varchar',
            isNullable: false,
          },
          {
            name: 'registerNumber',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'description',
            type: 'text',
            isNullable: true,
          },
          columnCreatedAt,
          columnUpdatedAt,
          columnDeletedAt,
        ],
      }),
      false,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable(this.tableName);
  }
}
