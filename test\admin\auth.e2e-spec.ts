import request from 'supertest';
import { APP_URL } from '../utils/constants';

describe('Auth (e2e)', () => {
  const app = APP_URL;

  it('Login: /api/auth/login (POST)', () => {
    return request(app)
      .post('/api/auth/login')
      .send({ email: 'test', password: 'test' })
      .expect(404)
      .expect(({ body }) => {
        expect(body.statusCode).toBeDefined();
        expect(body.errorCode).toBeDefined();
        expect(body.message).toBeDefined();
      });
  });
});
