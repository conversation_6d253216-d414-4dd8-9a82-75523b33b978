import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm';
import { EntityHelper } from '../../../utils/entity-helper';
import { SolidControlEquipment } from '../../solid-control-equipment/entities/solid-control-equipment.entity';

@Entity()
export class SolidControlEquipmentInput extends EntityHelper {
  @ManyToOne(() => SolidControlEquipment, { nullable: false })
  @JoinColumn({ name: 'solidControlEquipmentId' })
  solidControlEquipment: SolidControlEquipment;

  @Column({ type: String, nullable: false })
  description: string;

  @Column({ type: 'float', nullable: false })
  value: number;

  @Column({ type: String, nullable: false })
  units: string;
}
