import { Body, Controller, HttpCode, HttpStatus, Post, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { MongoAuthService } from './auth.service';
import { AuthLoginRequestDto } from '../../auth/dto/requests/auth-login.request.dto';
import { AuthLoginResponseDto } from '../../auth/dto/responses/auth-login.response.dto';
import { toDto } from '../../../common/transformers/dto.transformer';
import { AuthGuard } from '../../auth/guards/auth.guard';
import { Responder } from '../../../common/decorators/responder.decorator';

@ApiTags('MongoDB Auth')
@Controller('mongo-auth')
export class MongoAuthController {
  constructor(private readonly authService: MongoAuthService) {}

  @Post('/login')
  @ApiOperation({ description: 'MongoDB Login' })
  @Responder.handle('MongoDB Login')
  @HttpCode(HttpStatus.OK)
  async login(@Body() loginDto: AuthLoginRequestDto): Promise<AuthLoginResponseDto> {
    const response = await this.authService.login(loginDto);
    return toDto(AuthLoginResponseDto, response);
  }

  @ApiBearerAuth()
  @Post('/logout')
  @Responder.handle('MongoDB Logout')
  @UseGuards(AuthGuard)
  @HttpCode(HttpStatus.NO_CONTENT)
  public async logout(): Promise<void> {
    await this.authService.logout();
  }
}
