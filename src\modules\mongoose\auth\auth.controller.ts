import { Body, Controller, HttpCode, HttpStatus, Post, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { AuthService } from './auth.service';
import { AuthLoginRequestDto } from '../../auth/dto/requests/auth-login.request.dto';
import { AuthLoginResponseDto } from '../../auth/dto/responses/auth-login.response.dto';
import { toDto } from '../../../common/transformers/dto.transformer';
import { AuthGuard } from '../../auth/guards/auth.guard';
import { Responder } from '../../../common/decorators/responder.decorator';

@ApiTags('Auth')
@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('/login')
  @ApiOperation({ description: 'Login' })
  @Responder.handle('Login')
  @HttpCode(HttpStatus.OK)
  async login(@Body() loginDto: AuthLoginRequestDto): Promise<AuthLoginResponseDto> {
    const response = await this.authService.login(loginDto);
    return toDto(AuthLoginResponseDto, response);
  }

  @ApiBearerAuth()
  @Post('/logout')
  @Responder.handle('Logout')
  @UseGuards(AuthGuard)
  @HttpCode(HttpStatus.NO_CONTENT)
  public async logout(): Promise<void> {
    await this.authService.logout();
  }
}
