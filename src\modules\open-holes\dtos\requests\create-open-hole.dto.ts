import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString, IsUUID } from 'class-validator';

export class CreateOpenHoleDto {
  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  dailyReportId: string;

  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  insideDiameter?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  measuredDepth?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  washout?: number;
}
