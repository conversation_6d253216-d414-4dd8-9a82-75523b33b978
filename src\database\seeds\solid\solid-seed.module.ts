import { Module } from '@nestjs/common';
import { SolidSeedService } from './solid-seed.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DailyReport } from '../../../modules/daily-reports/entities/daily-report.entity';
import { Solid } from '../../../modules/solids/entities/solid.entity';

@Module({
  imports: [TypeOrmModule.forFeature([DailyReport, Solid])],
  providers: [SolidSeedService],
  exports: [SolidSeedService],
})
export class SolidSeedModule {}
