import { Global, Module } from '@nestjs/common';
import { AuthService } from './auth.service';
import { IsNotExist } from '../../utils/validators/is-not-exists.validator';
import { IsExist } from '../../utils/validators/is-exists.validator';
import { AuthController } from './auth.controller';
import { JwtModule } from '@nestjs/jwt';
import { UserModule } from '../users/user.module';
import { MailModule } from 'src/mail/mail.module';
import { UserRepository } from '../users/user.repository';
import { RoleRepository } from '../roles/role.repository';
import { CompanyRepository } from '../companies/company.repository';

@Global()
@Module({
  imports: [UserModule, JwtModule.register({}), MailModule],
  controllers: [AuthController],
  providers: [IsExist, IsNotExist, AuthService, UserRepository, RoleRepository, CompanyRepository],
  exports: [AuthService],
})
export class AuthModule {}
