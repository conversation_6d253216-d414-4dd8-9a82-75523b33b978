import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IsExist } from '../../utils/validators/is-exists.validator';
import { IsNotExist } from '../../utils/validators/is-not-exists.validator';
import { JwtModule } from '@nestjs/jwt';
import { ProductController } from './product.controller';
import { ProductRepository } from './product.repository';
import { ProductService } from './product.service';
import { Product } from './entities/product.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Product]), JwtModule.register({})],
  controllers: [ProductController],
  providers: [IsExist, IsNotExist, ProductService, ProductRepository],
  exports: [ProductService],
})
export class ProductModule {}
