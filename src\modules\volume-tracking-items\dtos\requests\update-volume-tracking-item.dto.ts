import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString, IsUUID } from 'class-validator';

export class UpdateVolumeTrackingItemDto {
  @ApiProperty({ required: true })
  @IsUUID()
  @IsNotEmpty()
  volumeTrackingId?: string;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  volume?: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  description?: string;
}
