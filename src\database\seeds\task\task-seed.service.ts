import { Injectable } from '@nestjs/common';
import { faker } from '@faker-js/faker';
import { Repository } from 'typeorm';
import { DailyReport } from '../../../modules/daily-reports/entities/daily-report.entity';
import { Task } from '../../../modules/tasks/entities/task.entity';
import { InjectRepository } from '@nestjs/typeorm';

@Injectable()
export class TaskSeedService {
  constructor(
    @InjectRepository(DailyReport)
    private dailyReportService: Repository<DailyReport>,
    @InjectRepository(Task)
    private taskService: Repository<Task>,
  ) {}

  async run(): Promise<void> {
    await this.fakeTasks();
  }

  private async fakeTasks(): Promise<void> {
    const reports = await this.dailyReportService.find({ where: {} });
    for (let i = 0; i < reports.length; i++) {
      await this.taskService.save(
        this.taskService.create({
          dailyReportId: reports[i]?.id,
          durations: faker.number.int(100),
          description: faker.lorem.text(),
        }),
      );
    }
  }
}
