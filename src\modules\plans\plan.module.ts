import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IsExist } from '../../utils/validators/is-exists.validator';
import { IsNotExist } from '../../utils/validators/is-not-exists.validator';
import { PlanService } from './plan.service';
import { Plan } from './entities/plan.entity';
import { JwtModule } from '@nestjs/jwt';
import { PlanRepository } from './plan.repository';
import { PlanController } from './plan.controller';

@Module({
  imports: [TypeOrmModule.forFeature([Plan]), JwtModule.register({})],
  controllers: [PlanController],
  providers: [IsExist, IsNotExist, PlanService, PlanRepository],
  exports: [PlanService],
})
export class PlanModule {}
