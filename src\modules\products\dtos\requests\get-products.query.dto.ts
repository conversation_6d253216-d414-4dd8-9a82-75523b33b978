import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, IsUUID } from "class-validator";

export class GetProductsQueryDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  keyword: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsUUID()
  @IsNotEmpty()
  companyId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  priceFrom?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  priceTo?: number;

  @ApiProperty({
    required: false,
    description: 'name | price | createdAt',
  })
  @IsOptional()
  @IsString()
  sortBy?: string;

  @ApiProperty({
    required: false,
    description: 'ASC | DESC',
  })
  @IsOptional()
  @IsString()
  sortDirection?: string;
}
