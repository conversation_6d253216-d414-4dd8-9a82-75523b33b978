import { Injectable } from '@nestjs/common';
import { AuthLoginRequestDto } from './dto/requests/auth-login.request.dto';
import { ForgotPasswordRequestDto } from './dto/requests/forgot-password.request.dto';
import { ValidateOtpRequestDto } from './dto/requests/validate-otp.request.dto';
import bcrypt from 'bcryptjs';
import { JwtService } from '@nestjs/jwt';
import { AuthLoginResponseDto } from './dto/responses/auth-login.response.dto';
import { ForgotPasswordResponseDto } from './dto/responses/forgot-password.response.dto';
import { HttpNotFoundError } from '../../errors/not-found.error';
import { HttpBadRequestError } from '../../errors/bad-request.error';
import { UserRole } from '../roles/enums/roles.enum';
import { ConfigService } from '@nestjs/config';
import { AllConfigType } from '../../config/config.type';
import ms from 'ms';
import { ErrorCode } from '../../errors/error-code';
import { UserService } from '../users/user.service';
import { UserRepository } from '../users/user.repository';
import { MailService } from 'src/mail/mail.service';
import { RegisterRequestDto } from './dto/requests/register.request.dto';
import { UserStatus } from '../users/enums/statuses.enum';
import { RoleRepository } from '../roles/role.repository';
import { CompanyRepository } from '../companies/company.repository';
import { generateOtp } from '../../utils/common/generate-otp';
import { getOtpExpiration } from '../../utils/common/get-otp-expiration';
import { Role } from '../roles/entities/role.entity';
import { In } from 'typeorm';
import { MailjetService } from '../../mail/mailjet.service';
import { MongoUserService } from '../mongoose/users/user.mongo.service';

@Injectable()
export class AuthService {
  constructor(
    private userService: UserService,
    private jwtService: JwtService,
    private userRepository: UserRepository,
    private roleRepository: RoleRepository,
    private companyRepository: CompanyRepository,
    private readonly mailService: MailService,
    private readonly mailjetService: MailjetService,
    private configService: ConfigService<AllConfigType>,
    private readonly mongoUserService: MongoUserService,
  ) {}

  async login(body: AuthLoginRequestDto): Promise<AuthLoginResponseDto> {
    const { email, password } = body;
    const user = await this.userService.findOne({ email }, ['roles']);
    if (!user) {
      throw new HttpNotFoundError(ErrorCode.USER_NOT_FOUND);
    }

    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      throw new HttpBadRequestError(ErrorCode.INCORRECT_EMAIL_PASSWORD);
    }
    if (user.status === UserStatus.INACTIVE) {
      throw new HttpBadRequestError(ErrorCode.USER_IN_ACTIVE);
    }

    const profile = await this.userService.findProfile(user);
    const tokenData = await this.generateToken({
      id: user.id,
      roles: user.roles,
      email: user.email,
      companyId: user.companyId,
    });
    return { ...tokenData, profile } as AuthLoginResponseDto;
  }

  async mongoLogin(body: AuthLoginRequestDto): Promise<AuthLoginResponseDto> {
    const { email, password } = body;
    const user = await this.mongoUserService.findByEmail(email);
    if (!user) {
      throw new HttpNotFoundError(ErrorCode.USER_NOT_FOUND);
    }

    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      throw new HttpBadRequestError(ErrorCode.INCORRECT_EMAIL_PASSWORD);
    }

    const tokenData = await this.generateMongoToken({
      id: user.id,
      roles: user.roles,
      email: user.email,
      companyId: user.company[0],
    });

    // For MongoDB, we'll return a simplified profile structure
    // Note: This is a temporary solution until proper role mapping is implemented
    const profile = {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      companyId: user.company[0],
      officePhone: user.officePhone,
      mobilePhone: user.mobilePhone,
      address: user.address,
      note: user.note,
      roles: [], // Simplified - roles will be handled in JWT token
      status: user.status,
    };

    return { ...tokenData, profile } as AuthLoginResponseDto;
  }

  async logout(): Promise<void> {
    // TODO: remove refresh token session
  }

  private async generateToken(data: {
    id: string;
    roles?: Role[];
    email: string;
    companyId: string | null;
  }) {
    const tokenExpiresIn = this.configService.getOrThrow('auth.expires', { infer: true });
    const secret = this.configService.getOrThrow('auth.secret', { infer: true });
    const tokenExpires = Date.now() + ms(tokenExpiresIn);

    const token = await this.jwtService.signAsync(
      {
        id: data.id,
        roles: data.roles,
        email: data.email,
        companyId: data.companyId,
      },
      {
        secret,
        expiresIn: tokenExpiresIn,
      },
    );

    return {
      token,
      tokenExpires,
    };
  }

  private async generateMongoToken(data: {
    id: string;
    roles?: string[];
    email: string;
    companyId: string | null;
  }) {
    const tokenExpiresIn = this.configService.getOrThrow('auth.expires', { infer: true });
    const secret = this.configService.getOrThrow('auth.secret', { infer: true });
    const tokenExpires = Date.now() + ms(tokenExpiresIn);

    const token = await this.jwtService.signAsync(
      {
        id: data.id,
        roles: data.roles,
        email: data.email,
        companyId: data.companyId,
      },
      {
        secret,
        expiresIn: tokenExpiresIn,
      },
    );

    return {
      token,
      tokenExpires,
    };
  }

  async forgotPassword(body: ForgotPasswordRequestDto): Promise<ForgotPasswordResponseDto> {
    const { email } = body;
    const user = await this.userService.findOne({ email });
    if (!user) {
      throw new HttpBadRequestError(ErrorCode.EMAIL_NOT_FOUND);
    }
    const otp = generateOtp();
    const hashedOtp = await bcrypt.hash(otp, 10);
    const expiration = getOtpExpiration(3); // OTP expires in 3 minutes
    console.log(otp, expiration);
    await this.userRepository.update(user.id, {
      confirmCode: hashedOtp,
      expiredConfirmCode: expiration,
    });
    const name = user.firstName + ' ' + user.lastName;
    // await this.mailService.sendOTPConfirmation(email, name, otp);
    await this.mailjetService.sendOTPConfirmation(email, name, otp);
    return { name, email } as ForgotPasswordResponseDto;
  }

  async validateOtp(body: ValidateOtpRequestDto): Promise<boolean> {
    const { email, otp } = body;
    const user = await this.userService.findOne({ email });
    if (!user || !user.confirmCode || !user.expiredConfirmCode) {
      return false;
    }
    const now = new Date();
    if (now > user.expiredConfirmCode) {
      return false; // OTP has expired
    }
    return await bcrypt.compare(otp, user.confirmCode);
  }

  async resetPassword(email: string, newPassword: string) {
    const user = await this.userService.findOne({ email });
    if (!user) {
      throw new Error('User not found');
    }
    const otp = generateOtp();
    const hashedOtp = await bcrypt.hash(otp, 10);
    const hashedPassword = await bcrypt.hash(newPassword, 10);
    await this.userRepository.update(user.id, { password: hashedPassword, confirmCode: hashedOtp });
  }

  async register(body: RegisterRequestDto) {
    const roles = await this.roleRepository.find({
      where: { value: In([body.companyId == null ? UserRole.COMPANY_ADMIN : UserRole.ENGINEER]) },
    });
    let companyId;
    let company;
    if (body.companyId) {
      companyId = body.companyId;
      company = await this.companyRepository.findOne({ where: { id: body.companyId } });
    } else {
      company = await this.companyRepository.save(
        this.companyRepository.create({ name: 'My company', description: '' }),
      );
      companyId = company.id;
    }
    const newUser = await this.userRepository.create({
      email: body.email,
      firstName: body.firstName,
      lastName: body.lastName,
      password: await bcrypt.hash(body.password, 10),
      status: UserStatus.ACTIVE,
      companyId: companyId,
      roles: roles,
    });
    newUser.company = company;
    await this.userRepository.save(newUser);
    return newUser;
  }
}
