import { Exclude, Expose, Type } from "class-transformer";
import { CompanyResponseDto } from "../../../companies/dtos/responses/company.response.dto";

@Exclude()
export class CustomerDetailResponseDto {
  @Expose()
  id: string;

  @Expose()
  customerName: string;

  @Expose()
  notes: string;

  @Expose()
  createdAt: string;

  @Expose()
  @Type(() => CompanyResponseDto)
  company: CompanyResponseDto;
}
