import { Injectable } from '@nestjs/common';
import { DeepPartial, Repository } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { InjectRepository } from '@nestjs/typeorm';
import { Country } from '../../../modules/countries/entities/country.entity';
import { State } from '../../../modules/states/entities/state.entity';
import { keyBy } from '../../../utils/lodash';

import * as COUNTRIES from './metadata/countries.json';
import * as STATES from './metadata/states.json';

@Injectable()
export class CountryStateSeedService {
  constructor(
    @InjectRepository(Country)
    private countryRepository: Repository<Country>,
    @InjectRepository(State)
    private stateRepository: Repository<State>,
  ) {}

  async run(): Promise<void> {
    await this.fakeData();
  }

  private async fakeData(): Promise<void> {
    const countries = COUNTRIES.countries.map(item => ({
      tempId: item.id,
      name: item.name,
      id: uuidv4(),
    }));
    await this.countryRepository.save(Country.create(countries));
    const countriesKeyBy = keyBy(countries, 'tempId');
    const states: DeepPartial<State>[] = STATES.states.reduce((res: DeepPartial<State>[], item) => {
      if ((countriesKeyBy[item.countryId] || {}).id) {
        res.push({
          name: item.name,
          countryId: (countriesKeyBy[item.countryId] || {}).id,
        });
      } else {
        console.log(`'country ${item.countryId} is not found for state ${item.name}`);
      }
      return res;
    }, []);
    await this.stateRepository.save(State.create(states));
  }
}
