import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty } from 'class-validator';
import { Transform } from 'class-transformer';
import { lowerCaseTransformer } from 'src/common/transformers/lower-case.transformer';

export class AuthLoginRequestDto {
  @ApiProperty({ example: '<EMAIL>' })
  @Transform(lowerCaseTransformer)
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @ApiProperty({ example: 'B1s@2o2i' })
  @IsNotEmpty()
  password: string;
}
