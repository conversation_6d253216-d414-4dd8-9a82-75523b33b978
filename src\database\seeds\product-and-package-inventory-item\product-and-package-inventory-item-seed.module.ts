import { Module } from '@nestjs/common';
import { ProductAndPackageInventoryItemSeedService } from './product-and-package-inventory-item-seed.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ProductAndPackageInventoryItem } from '../../../modules/product-and-package-inventory-items/entities/product-and-package-inventory-item.entity';
import { VolumeTracking } from '../../../modules/volum-trackings/entities/volume-tracking.entity';
import { ProductAndPackageInventoryReport } from '../../../modules/product-and-package-inventory-reports/entities/product-and-package-inventory-report.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      VolumeTracking,
      ProductAndPackageInventoryReport,
      ProductAndPackageInventoryItem,
    ]),
  ],
  providers: [ProductAndPackageInventoryItemSeedService],
  exports: [ProductAndPackageInventoryItemSeedService],
})
export class ProductAndPackageInventoryItemSeedModule {}
