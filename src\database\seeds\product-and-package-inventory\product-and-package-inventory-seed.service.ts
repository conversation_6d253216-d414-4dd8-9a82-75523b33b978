import { Injectable } from '@nestjs/common';
import { faker } from '@faker-js/faker';
import { Repository } from 'typeorm';
import { DailyReport } from '../../../modules/daily-reports/entities/daily-report.entity';
import { ProductAndPackageInventory } from '../../../modules/product-and-package-inventorys/entities/product-and-package-inventory.entity';
import { InjectRepository } from '@nestjs/typeorm';

@Injectable()
export class ProductAndPackageInventorySeedService {
  constructor(
    @InjectRepository(DailyReport)
    private dailyReportRepository: Repository<DailyReport>,
    @InjectRepository(ProductAndPackageInventory)
    private productAndPackageInventoryRepository: Repository<ProductAndPackageInventory>,
  ) {}

  async run(): Promise<void> {
    await this.fakeProductAndPackageInventories();
  }

  private async fakeProductAndPackageInventories(): Promise<void> {
    const reports = await this.dailyReportRepository.find({ where: {} });
    for (let i = 0; i < reports.length; i++) {
      await this.productAndPackageInventoryRepository.save(
        this.productAndPackageInventoryRepository.create({
          dailyReport: { id: reports[i]?.id },
          totalCost: faker.number.float({ max: 200, precision: 0.01 }),
          totalProductVolume: faker.number.float({ max: 200, precision: 0.01 }),
          addWater: faker.number.float({ max: 200, precision: 0.01 }),
          baseFluid: faker.number.float({ max: 200, precision: 0.01 }),
          totalVolume: faker.number.float({ max: 200, precision: 0.01 }),
          weightMaterials: faker.number.float({ max: 200, precision: 0.01 }),
        }),
      );
    }
  }
}
