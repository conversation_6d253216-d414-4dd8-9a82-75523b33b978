import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class CreateEngineerWellTable1702306926359 implements MigrationInterface {
  private tableName = 'user_well';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: this.tableName,
        columns: [
          {
            name: 'userId',
            type: 'uuid',
            isNullable: false,
          },
          {
            name: 'wellId',
            type: 'uuid',
            isNullable: false,
          },
        ],
      }),
      false,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable(this.tableName);
  }
}
