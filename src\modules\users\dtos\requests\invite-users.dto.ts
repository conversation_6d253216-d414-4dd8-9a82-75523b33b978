import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsOptional, IsUUID } from "class-validator";
import { IsUuidArray } from "../../../../common/decorators/class-validator/is-array-uuid.decorator";

export class InviteUsersDto {
  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsArray()
  emails: string[];

  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsUUID()
  companyId: string;

  @ApiProperty({
    required: true,
    description: 'admin: 1, supervisor: 2, engineer: 3, companyAdmin: 4',
    example: [1],
  })
  @IsArray()
  userRoles?: number[];
}
