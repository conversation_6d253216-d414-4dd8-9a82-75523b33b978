import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm';
import { EntityHelper } from '../../../utils/entity-helper';
import { Pump } from '../../pumps/entities/pump.entity';

@Entity()
export class PumpDuration extends EntityHelper {
  @ManyToOne(() => Pump, pump => pump.durations, { nullable: false })
  @JoinColumn({ name: 'pumpId' })
  pump: Pump;

  @Column({ nullable: false })
  durations: number;
}
