import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString, IsUUID } from "class-validator";
import { CostSettingTypeEnum } from '../../enums/cost-setting-type.enum';
import { Transform } from 'class-transformer';

export class GetCostSettingsQueryDto {
  @ApiProperty({
    enum: CostSettingTypeEnum,
    description: 'Service: 1, Engineer: 2',
    required: false,
  })
  @Transform(params => parseInt(params.value))
  @IsNumber()
  @IsOptional()
  type?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  keyword?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsUUID()
  @IsNotEmpty()
  companyId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  priceFrom?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  priceTo?: number;

  @ApiProperty({
    required: false,
    description: 'name | cost | createdAt',
  })
  @IsOptional()
  @IsString()
  sortBy?: string;

  @ApiProperty({
    required: false,
    description: 'ASC | DESC',
  })
  @IsOptional()
  @IsString()
  sortDirection?: string;
}
