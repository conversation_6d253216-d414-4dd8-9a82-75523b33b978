import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { AuthGuard } from '../auth/guards/auth.guard';
import { toDto } from '../../common/transformers/dto.transformer';

import { RolesDecorator } from '../auth/decorators/roles.decorator';
import { UserRole } from '../roles/enums/roles.enum';
import { RolesGuard } from '../auth/guards/roles.guard';
import { VolumeTrackingItemService } from './volume-tracking-item.service';
import { CreateVolumeTrackingItemDto } from './dtos/requests/create-volume-tracking-item.dto';
import { VolumeTrackingItemResponseDto } from './dtos/responses/volume-tracking-item.response.dto';
import { UpdateVolumeTrackingItemDto } from './dtos/requests/update-volume-tracking-item.dto';
import { Responder } from '../../common/decorators/responder.decorator';

@ApiTags('VolumeTrackingItem')
@Controller('volumeTrackingItems')
@ApiBearerAuth('access-token')
@RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN, UserRole.SUPERVISOR, UserRole.ENGINEER)
@UseGuards(AuthGuard, RolesGuard)
export class VolumeTrackingItemController {
  constructor(private readonly service: VolumeTrackingItemService) {}

  @Post()
  @ApiOperation({ description: 'Create volume tracking item' })
  @Responder.handle('Create volume tracking item')
  @HttpCode(HttpStatus.CREATED)
  async create(@Body() data: CreateVolumeTrackingItemDto): Promise<VolumeTrackingItemResponseDto> {
    const value = await this.service.create(data);
    return toDto(VolumeTrackingItemResponseDto, value);
  }

  @Put('/:id')
  @ApiOperation({ description: 'Update volume tracking item' })
  @Responder.handle('Update volume tracking item')
  @HttpCode(HttpStatus.CREATED)
  async update(
    @Param('id') id: string,
    @Body() data: UpdateVolumeTrackingItemDto,
  ): Promise<boolean> {
    return this.service.update(id, data);
  }

  @Get('/:id')
  @ApiOperation({ description: 'Get volume tracking item detail' })
  @Responder.handle('Get volume tracking item detail')
  @HttpCode(HttpStatus.OK)
  async detail(@Param('id') id: string): Promise<VolumeTrackingItemResponseDto> {
    const data = await this.service.findOne(id);
    return toDto(VolumeTrackingItemResponseDto, data);
  }

  @Delete('/:id')
  @ApiOperation({ description: 'Delete volume tracking item' })
  @Responder.handle('Delete volume tracking item')
  @HttpCode(HttpStatus.OK)
  delete(@Param('id') id: string): Promise<boolean> {
    return this.service.softDeleteById(id);
  }
}
