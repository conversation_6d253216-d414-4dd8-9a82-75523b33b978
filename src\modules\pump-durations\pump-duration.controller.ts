import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { AuthGuard } from '../auth/guards/auth.guard';

import { toDto } from '../../common/transformers/dto.transformer';

import { RolesDecorator } from '../auth/decorators/roles.decorator';
import { UserRole } from '../roles/enums/roles.enum';
import { RolesGuard } from '../auth/guards/roles.guard';

import { PumpDurationService } from './pump-duration.service';
import { PumpDurationResponseDto } from './dtos/responses/pump-duration.response.dto';
import { CreatePumpDurationDto } from './dtos/requests/create-pump-duration.dto';
import { UpdatePumpDurationDto } from './dtos/requests/update-pump-duration.dto';
import { Responder } from '../../common/decorators/responder.decorator';

@ApiTags('PumpDuration')
@Controller('pumpDurations')
@ApiBearerAuth('access-token')
@RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN, UserRole.SUPERVISOR, UserRole.ENGINEER)
@UseGuards(AuthGuard, RolesGuard)
export class PumpDurationDurationController {
  constructor(private readonly service: PumpDurationService) {}

  @Post()
  @ApiOperation({ description: 'Create pump duration' })
  @Responder.handle('Create pump duration')
  @HttpCode(HttpStatus.CREATED)
  async create(@Body() data: CreatePumpDurationDto): Promise<PumpDurationResponseDto> {
    const value = await this.service.create(data);
    return toDto(PumpDurationResponseDto, value);
  }

  @Put('/:id')
  @ApiOperation({ description: 'Update pump duration' })
  @Responder.handle('Update pump duration')
  @HttpCode(HttpStatus.CREATED)
  async update(@Param('id') id: string, @Body() data: UpdatePumpDurationDto): Promise<boolean> {
    return this.service.update(id, data);
  }

  @Get('/:id')
  @ApiOperation({ description: 'Get pump duration detail' })
  @Responder.handle('Get pump duration detail')
  @HttpCode(HttpStatus.OK)
  async detail(@Param('id') id: string): Promise<PumpDurationResponseDto> {
    const data = await this.service.findOne(id);
    return toDto(PumpDurationResponseDto, data);
  }

  @Delete('/:id')
  @ApiOperation({ description: 'Delete pump duration' })
  @Responder.handle('Delete pump duration')
  @HttpCode(HttpStatus.OK)
  delete(@Param('id') id: string): Promise<boolean> {
    return this.service.softDeleteById(id);
  }
}
