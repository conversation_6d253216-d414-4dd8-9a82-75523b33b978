import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IsExist } from '../../utils/validators/is-exists.validator';
import { IsNotExist } from '../../utils/validators/is-not-exists.validator';
import { CostSetting } from './entities/cost-setting.entity';
import { JwtModule } from '@nestjs/jwt';
import { CostSettingController } from './cost-setting.controller';
import { CostSettingRepository } from './cost-setting.repository';
import { CostSettingService } from './cost-setting.service';
import { CostRepository } from "../costs/cost.repository";

@Module({
  imports: [TypeOrmModule.forFeature([CostSetting]), JwtModule.register({})],
  controllers: [CostSettingController],
  providers: [IsExist, IsNotExist, CostSettingService, CostSettingRepository, CostRepository],
  exports: [CostSettingService],
})
export class CostSettingModule {}
