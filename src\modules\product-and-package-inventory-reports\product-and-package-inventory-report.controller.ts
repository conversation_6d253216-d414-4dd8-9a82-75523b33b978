import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { PaginationResponseDto } from '../../common/dtos/pagination.response.dto';
import { AuthGuard } from '../auth/guards/auth.guard';
import { ProductAndPackageInventoryReportResponseDto } from './dtos/responses/product-and-package-inventory-report.response.dto';

import { PaginationDto } from '../../common/dtos/paginationDto';
import { toDto, toPaginateDtos } from '../../common/transformers/dto.transformer';

import { RolesDecorator } from '../auth/decorators/roles.decorator';
import { UserRole } from '../roles/enums/roles.enum';
import { RolesGuard } from '../auth/guards/roles.guard';
import { GetProductAndPackageInventoryDto } from './dtos/requests/get-product-and-package-inventory.dto';
import { ProductAndPackageInventoryReportService } from './product-and-package-inventory-report.service';
import { CreateProductAndPackageInventoryReportDto } from './dtos/requests/create-product-and-package-inventory-report.dto';
import { ProductAndPackageInventoryReportDetailResponseDto } from './dtos/responses/product-and-package-inventory-report-detail.response.dto';
import { Responder } from '../../common/decorators/responder.decorator';
import { CurrentUser } from '../../common/decorators/current-user.decorator';
import { User } from '../users/entities/user.entity';

@ApiTags('ProductAndPackageInventoryReport')
@Controller('productAndPackageInventoryReports')
@ApiBearerAuth('access-token')
@RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN, UserRole.SUPERVISOR, UserRole.ENGINEER)
@UseGuards(AuthGuard, RolesGuard)
export class ProductAndPackageInventoryReportController {
  constructor(private readonly service: ProductAndPackageInventoryReportService) {}

  @Get()
  @ApiOperation({ description: 'Get product and package inventory reports' })
  @Responder.handle('Get product and package inventory reports')
  @HttpCode(HttpStatus.OK)
  async findAll(
    @Query() query: GetProductAndPackageInventoryDto,
    @Query() paginationQuery: PaginationDto,
  ): Promise<PaginationResponseDto<ProductAndPackageInventoryReportResponseDto>> {
    const data = await this.service.findAll(query, paginationQuery);
    return toPaginateDtos(ProductAndPackageInventoryReportResponseDto, data);
  }

  @Post()
  @ApiOperation({ description: 'Create product and package inventory report' })
  @Responder.handle('Create product and package inventory report')
  @HttpCode(HttpStatus.CREATED)
  async create(
    @CurrentUser() user: User,
    @Body() data: CreateProductAndPackageInventoryReportDto,
  ): Promise<ProductAndPackageInventoryReportResponseDto> {
    const value = await this.service.create(user, data);
    return toDto(ProductAndPackageInventoryReportResponseDto, value);
  }

  @Get('/:id')
  @ApiOperation({ description: 'Get product and package inventory report detail' })
  @Responder.handle('Get product and package inventory report detail')
  @HttpCode(HttpStatus.OK)
  async detail(
    @Param('id') id: string,
  ): Promise<ProductAndPackageInventoryReportDetailResponseDto> {
    const data = await this.service.findOne(id);
    return toDto(ProductAndPackageInventoryReportDetailResponseDto, data);
  }

  @Delete('/:id')
  @ApiOperation({ description: 'Delete product and package inventory report' })
  @Responder.handle('Delete product and package inventory')
  @HttpCode(HttpStatus.OK)
  delete(@CurrentUser() user: User, @Param('id') id: string): Promise<boolean> {
    return this.service.softDeleteById(user, id);
  }
}
