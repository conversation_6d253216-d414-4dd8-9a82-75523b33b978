import { Module } from '@nestjs/common';
import { CustomerContactSeedService } from './customer-contact-seed.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Customer } from '../../../modules/customers/entities/customer.entity';
import { CustomerContact } from '../../../modules/customer-contact/entities/customer-contact.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Customer, CustomerContact])],
  providers: [CustomerContactSeedService],
  exports: [CustomerContactSeedService],
})
export class CustomerContactSeedModule {}
