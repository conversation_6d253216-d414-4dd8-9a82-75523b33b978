import { Injectable } from '@nestjs/common';
import { faker } from '@faker-js/faker';
import { Repository } from 'typeorm';
import { Cost } from '../../../modules/costs/entities/cost.entity';
import { DailyReport } from '../../../modules/daily-reports/entities/daily-report.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { CostSetting } from '../../../modules/cost-settings/entities/cost-setting.entity';

@Injectable()
export class CostSeedService {
  constructor(
    @InjectRepository(DailyReport)
    private dailyReportRepository: Repository<DailyReport>,
    @InjectRepository(Cost)
    private costRepository: Repository<Cost>,
    @InjectRepository(CostSetting)
    private costSettingRepository: Repository<CostSetting>,
  ) {}

  async run(): Promise<void> {
    await this.fakeCosts();
  }

  private async fakeCosts(): Promise<void> {
    const reports = await this.dailyReportRepository.find({ where: {} });
    const costSetting = await this.costSettingRepository.findOne({ where: {} });
    for (let i = 0; i < reports.length; i++) {
      await this.costRepository.save(
        this.costRepository.create({
          dailyReport: { id: reports[i]?.id },
          costSetting: { id: costSetting?.id } as CostSetting,
          unit: faker.number.float({ max: 200, precision: 0.01 }),
          quantity: faker.number.int({ min: 1, max: 1000 }),
          createdAt: faker.date.anytime(),
        }),
      );
    }
  }
}
