import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { HttpForbiddenError } from 'src/errors/forbidden.error';

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const roles = this.reflector.getAllAndOverride<number[]>('roles', [
      context.getClass(),
      context.getHandler(),
    ]);
    if (!roles.length) {
      return true;
    }
    const request = context.switchToHttp().getRequest();
    const userRoles = request.user?.roles?.map(role => role.value);
    const hasPermission = roles.some(item => userRoles.includes(item));
    if (!hasPermission) {
      throw new HttpForbiddenError('INVALID_PERMISSION');
    }
    return true;
  }
}
