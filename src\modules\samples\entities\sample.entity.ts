import { Column, <PERSON><PERSON><PERSON>, Join<PERSON><PERSON>umn, ManyToOne } from 'typeorm';
import { EntityHelper } from '../../../utils/entity-helper';
import { DailyReport } from '../../daily-reports/entities/daily-report.entity';

@Entity()
export class Sample extends EntityHelper {
  @ManyToOne(() => DailyReport, report => report.samples, { nullable: false })
  @JoinColumn({ name: 'dailyReportId' })
  dailyReport: DailyReport;

  @Column({ nullable: false })
  fluidType: number;

  @Column({ type: 'boolean', default: false })
  weightedMud: boolean;

  @Column({ type: String, nullable: false })
  sampleFrom: string;

  @Column({ type: String, nullable: false })
  timeSampleTaken: string;

  @Column({ type: 'float', nullable: false })
  flowlineTemperature: number;

  @Column({ type: 'float', nullable: false })
  measuredDepth: number;

  @Column({ type: 'float', nullable: false })
  mudWeight: number;

  @Column({ type: 'float', nullable: false })
  funnelViscosity: number;

  @Column({ type: 'float', nullable: false })
  temperatureForPlasticViscosity: number;

  @Column({ type: 'float', nullable: false })
  plasticViscosity: number;

  @Column({ type: 'float', nullable: false })
  yieldPoint: number;

  @Column({ type: 'float', nullable: false })
  gelStrength10s: number;

  @Column({ type: 'float', nullable: false })
  gelStrength10m: number;

  @Column({ type: 'float', nullable: false })
  gelStrength30m: number;

  @Column({ type: 'float', nullable: false })
  apiFiltrate: number;

  @Column({ type: 'float', nullable: false })
  apiCakeThickness: number;

  @Column({ type: 'float', nullable: false })
  temperatureForHTHP: number;

  @Column({ type: 'float', nullable: false })
  hthpFiltrate: number;

  @Column({ type: 'float', nullable: false })
  hthpCakeThickness: number;

  @Column({ type: 'float', nullable: false })
  solids: number;

  @Column({ type: 'float', nullable: false })
  oil: number;

  @Column({ type: 'float', nullable: false })
  water: number;

  @Column({ type: 'float', nullable: false })
  sandContent: number;

  @Column({ type: 'float', nullable: false })
  mbtCapacity: number;

  @Column({ type: 'float', nullable: false })
  pH: number;

  @Column({ type: 'float', nullable: false })
  mudAlkalinity: number;

  @Column({ type: 'float', nullable: false })
  filtrateAlkalinity: number;

  @Column({ type: 'float', nullable: false })
  calcium: number;

  @Column({ type: 'float', nullable: false })
  chlorides: number;

  @Column({ type: 'float', nullable: false })
  totalHardness: number;

  @Column({ type: 'float', nullable: false })
  excessLime: number;

  @Column({ type: 'float', nullable: false })
  kPlus: number;

  @Column({ type: 'float', nullable: false })
  makeUpWater: number;

  @Column({ type: 'float', nullable: false })
  solidsAdjustedForSalt: number;

  @Column({ type: 'float', nullable: false })
  fineLCM: number;

  @Column({ type: 'float', nullable: false })
  coarseLCM: number;

  @Column({ type: 'float', nullable: false })
  linearGelStrengthPercent: number;

  @Column({ type: 'float', nullable: false })
  linearGelStrengthLbBbl: number;

  @Column({ type: 'float', nullable: false })
  highGelStrengthPercent: number;

  @Column({ type: 'float', nullable: false })
  highGelStrengthLbBbl: number;

  @Column({ type: 'float', nullable: false })
  bentoniteConcentrationPercent: number;

  @Column({ type: 'float', nullable: false })
  bentoniteConcentrationLbBbl: number;

  @Column({ type: 'float', nullable: false })
  drillSolidsConcentrationPercent: number;

  @Column({ type: 'float', nullable: false })
  drillSolidsConcentrationLbBbl: number;

  @Column({ type: 'float', nullable: false })
  drillSolidsToBentoniteRatio: number;

  @Column({ type: 'float', nullable: false })
  averageSpecificGravityOfSolids: number;

  @Column({ type: 'float', nullable: false })
  shearRate600: number;

  @Column({ type: 'float', nullable: false })
  shearRate300: number;

  @Column({ type: 'float', nullable: false })
  shearRate200: number;

  @Column({ type: 'float', nullable: false })
  shearRate100: number;

  @Column({ type: 'float', nullable: false })
  shearRate6: number;

  @Column({ type: 'float', nullable: false })
  shearRate3: number;

  @Column({ type: 'float', nullable: false })
  apparentViscosity: number;

  @Column({ type: 'float', nullable: false })
  shearRate: number;

  @Column({ type: 'float', nullable: false })
  shearStress: number;
}
