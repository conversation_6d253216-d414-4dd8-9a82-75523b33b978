import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString, IsUUID } from 'class-validator';
import { UserRole } from '../../../roles/enums/roles.enum';
import { Transform } from 'class-transformer';
import { UserStatus } from '../../enums/statuses.enum';

export class GetUserDto {
  @ApiProperty({
    required: false,
    enum: UserRole,
    description: 'admin: 1, supervisor: 2, engineer: 3, companyAdmin: 4',
  })
  @IsOptional()
  @Transform(params => parseInt(params.value))
  @IsNumber()
  role?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    required: false,
    enum: UserStatus,
    description: 'active: 1, inactive: 2',
  })
  @IsOptional()
  @IsNumber()
  @Transform(params => parseInt(params.value))
  status?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsUUID()
  @IsNotEmpty()
  companyId?: string;

  @ApiProperty({
    required: false,
    example: 'name',
    description: 'name | email',
  })
  @IsOptional()
  @IsString()
  sortBy?: string;

  @ApiProperty({
    required: false,
    description: 'ASC | DESC',
  })
  @IsOptional()
  @IsString()
  sortDirection?: string;
}
