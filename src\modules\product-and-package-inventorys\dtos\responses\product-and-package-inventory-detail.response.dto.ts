import { Exclude, Expose, Type } from 'class-transformer';
import { ProductAndPackageInventoryItemResponseDto } from '../../../product-and-package-inventory-items/dtos/responses/product-and-package-inventory-item.response.dto';

@Exclude()
export class ProductAndPackageInventoryDetailResponseDto {
  @Expose()
  id: string;

  @Expose()
  totalCost: number;

  @Expose()
  totalProductVolume: number;

  @Expose()
  weightMaterials: number;

  @Expose()
  baseFluid: number;

  @Expose()
  addWater: number;

  @Expose()
  totalVolume: number;

  @Expose()
  @Type(() => ProductAndPackageInventoryItemResponseDto)
  items: ProductAndPackageInventoryItemResponseDto[];
}
