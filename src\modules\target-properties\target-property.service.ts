import { Injectable } from '@nestjs/common';

import { TargetPropertyRepository } from './target-property.repository';
import { UpdateTargetPropertyDto } from './dtos/requests/update-target-property.dto';
import { TargetProperty } from './entities/target-property.entity';

@Injectable()
export class TargetPropertyService {
  constructor(private repository: TargetPropertyRepository) {}

  async update(id: string, data: UpdateTargetPropertyDto): Promise<boolean> {
    await this.repository.update({ id }, { ...data });
    return true;
  }

  async findOne(wellId: string): Promise<TargetProperty | null> {
    const target = await this.repository
      .createQueryBuilder('target')
      .where('target.wellId = :wellId', { wellId: wellId })
      .getOne();
    if (target) {
      return target;
    }
    return this.repository.save(
      this.repository.create({
        wellId,
        fluidType: 0,
        mudWeight: 0,
        funnelViscosity: 0,
        plasticViscosity: 0,
        yieldPoint: 0,
        apiFiltrate: 0,
        apiCakeThickness: 0,
        pH: 0,
        mudAlkalinity: 0,
        filtrateAlkalinity: 0,
        chlorides: 0,
        totalHardness: 0,
        linearGelStrengthPercent: 0,
        rpm: 0,
      }),
    );
  }

  async softDeleteById(id: string): Promise<boolean> {
    await this.repository.softDelete({ id });
    return true;
  }
}
