import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsDateString, IsNotEmpty, IsOptional, IsString, IsUUID } from "class-validator";
import { Transform } from 'class-transformer';
import { IsUuidArray } from "../../../../common/decorators/class-validator/is-array-uuid.decorator";

export class GetWellsQueryDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  keyword: string;

  @ApiProperty({ required: false, default: false })
  @IsBoolean()
  @Transform(({ value }) => value === 'true')
  archived: boolean = false;

  @ApiProperty({
    example: '2024-01-12',
    description: 'from date, Example: 2024-01-12',
    required: false,
  })
  @IsNotEmpty()
  @IsOptional()
  @IsDateString()
  fromDate?: string;

  @ApiProperty({
    example: '2024-01-13',
    description: 'to date, Example: 2024-01-13',
    required: false,
  })
  @IsNotEmpty()
  @IsOptional()
  @IsDateString()
  toDate?: string;

  @ApiProperty({description: 'xxxx,xxxx',  required: false })
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  userIds?: string;

  @ApiProperty({ required: false })
  @IsUUID()
  @IsNotEmpty()
  @IsOptional()
  customerId?: string;

  @ApiProperty({ required: false })
  @IsUUID()
  @IsNotEmpty()
  @IsOptional()
  companyId?: string;
}
