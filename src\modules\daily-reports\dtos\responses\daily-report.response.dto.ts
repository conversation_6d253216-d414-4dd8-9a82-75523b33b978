import { Expose, Type } from 'class-transformer';
import { UserResponseDto } from '../../../users/dtos/responses/user.response.dto';

export class DailyReportResponseDto {
  @Expose()
  id: string;

  @Expose()
  companyId: string;

  // @Expose()
  // reportDate: string;

  @Expose()
  updatedAt: string;

  @Expose()
  createdAt: string;

  @Expose()
  @Type(() => UserResponseDto)
  createdBy: UserResponseDto;

  @Expose()
  @Type(() => UserResponseDto)
  updatedBy: UserResponseDto;
}
