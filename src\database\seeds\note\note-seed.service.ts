import { Injectable } from '@nestjs/common';
import { faker } from '@faker-js/faker';
import { Repository } from 'typeorm';
import { DailyReport } from '../../../modules/daily-reports/entities/daily-report.entity';
import { Note } from '../../../modules/notes/entities/note.entity';
import { InjectRepository } from '@nestjs/typeorm';

@Injectable()
export class NoteSeedService {
  constructor(
    @InjectRepository(DailyReport)
    private dailyReportRepository: Repository<DailyReport>,
    @InjectRepository(Note)
    private noteRepository: Repository<Note>,
  ) {}

  async run(): Promise<void> {
    await this.fakeNotes();
  }

  private async fakeNotes(): Promise<void> {
    const reports = await this.dailyReportRepository.find({ where: {} });
    for (let i = 0; i < reports.length; i++) {
      await this.noteRepository.save(
        this.noteRepository.create({
          dailyReport: { id: reports[i].id },
          notes: faker.lorem.text(),
          title: faker.word.sample(),
          createdAt: faker.date.anytime(),
        }),
      );
    }
  }
}
