import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsUUID } from 'class-validator';

export class GetCustomerContactQueryDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({ required: true })
  @IsUUID()
  customerId: string;

  @ApiProperty({
    required: false,
    example: 'name',
    description: 'name | createdAt',
  })
  @IsOptional()
  @IsString()
  sortBy?: string;

  @ApiProperty({
    required: false,
    description: 'ASC | DESC',
  })
  @IsOptional()
  @IsString()
  sortDirection?: string;
}
