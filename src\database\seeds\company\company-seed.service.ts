import { Injectable } from '@nestjs/common';
import { faker } from '@faker-js/faker';
import { Repository } from 'typeorm';
import { Company } from '../../../modules/companies/entities/company.entity';
import { InjectRepository } from '@nestjs/typeorm';

@Injectable()
export class CompanySeedService {
  constructor(
    @InjectRepository(Company)
    private companyRepository: Repository<Company>,
  ) {}

  async run(): Promise<void> {
    await this.fakeCompanies();
  }

  private async fakeCompanies(): Promise<void> {
    for (let i = 0; i < 1; i++) {
      const name = faker.company.name();
      await this.companyRepository.save(
        this.companyRepository.create({
          name: name,
          registerNumber: faker.finance.accountNumber(10),
          description: faker.lorem.text(),
        }),
      );
    }
  }
}
