import { Injectable } from "@nestjs/common";
import { Well } from "./entities/well.entity";
import { CreateWellDto } from "./dtos/requests/create-well.dto";
import { GetWellsQueryDto } from "./dtos/requests/get-wells.query.dto";
import { User } from "../users/entities/user.entity";
import { PaginationDto } from "../../common/dtos/paginationDto";
import { Pagination } from "../../common/types/request-response.type";
import { WellRepository } from "./well.repository";
import { Customer } from "../customers/entities/customer.entity";
import { Company } from "../companies/entities/company.entity";
import { UpdateWellDto } from "./dtos/requests/update-well.dto";
import { UserRole } from "../roles/enums/roles.enum";
import { setTimeOfDate } from "../../utils/datetime-helper";
import { UserRepository } from "../users/user.repository";
import { In, SelectQueryBuilder } from "typeorm";
import { HttpForbiddenError } from "../../errors/forbidden.error";
import { ErrorCode } from "../../errors/error-code";
import { Sample } from "../samples/entities/sample.entity";

@Injectable()
export class WellService {
  constructor(
    private wellRepository: WellRepository,
    private userRepository: UserRepository,
  ) {}

  async findAll(
    user: User,
    query: GetWellsQueryDto,
    paginationQuery: PaginationDto,
  ): Promise<Pagination<Well>> {
    const { keyword, archived } = query;
    const currentUserRoles = user.roles?.map(role => role.value) ?? [];
    let builder = this.wellRepository.createQueryBuilder('well').withDeleted().andWhere("well.deletedAt IS NULL");
    keyword &&
      (builder = builder.where('well.nameOrNo ILIKE :nameOrNo', { nameOrNo: `%${keyword}%` }));
    builder = builder.andWhere('well.archived = :archived', { archived: archived });

    if (query.fromDate || query.toDate) {
      const fromDate = setTimeOfDate(new Date((query.fromDate ?? query.toDate)!), 0, 0);
      const toDate = setTimeOfDate(new Date((query.toDate ?? query.fromDate)!), 23, 59, 59);
      builder = builder.andWhere('well.createdAt BETWEEN :fromDate AND :toDate', {
        fromDate: fromDate,
        toDate: toDate,
      });
    }
    builder = builder
      .leftJoinAndSelect('well.customers', 'customer')
      .leftJoinAndSelect('well.users', 'user')
      .leftJoinAndSelect('user.roles', 'role')
      .leftJoinAndSelect('well.company', 'company')
      .leftJoin('user_well', 'user_well', 'well.id = user_well.wellId')
      .leftJoin('customer_well', 'customer_well', 'well.id = customer_well.wellId')
      //The query in the leftJoinAndSelect('well.dailyReport', 'dailyReport', ...) fetches the latest report.
      // It orders the reports by createdAt in descending order to pick the latest one,
      // using LIMIT 1 to ensure only one report is selected per well.
      .leftJoinAndSelect('well.dailyReport', 'dailyReport', `"dailyReport".id = (
          SELECT r.id FROM daily_report r
          WHERE r."wellId" = well.id AND r."deletedAt" IS NULL
          ORDER BY r."createdAt" DESC
          LIMIT 1
        )`)
      .leftJoinAndSelect('dailyReport.wellInformation', 'wellInformation')
      .leftJoinAndSelect('dailyReport.notes', 'notes')
      //The query in the leftJoinAndSelect('well.reportWithLatestSample', 'reportWithLatestSample', ...) fetches the report that has the latest sample.
      // It joins the daily_report table and orders the samples by createdAt in descending order to pick the latest one,
      // using LIMIT 1 to ensure only one report is selected per well.
      .leftJoinAndSelect('well.reportWithLatestSample', 'reportWithLatestSample', `"reportWithLatestSample".id = (
          SELECT r.id FROM daily_report r
          INNER JOIN sample s ON s."dailyReportId" = r.id
          WHERE r."wellId" = well.id AND r."deletedAt" IS NULL
          ORDER BY s."createdAt" DESC
          LIMIT 1
        )`)
      //The leftJoinAndSelect('reportWithLatestSample.samples', 'sample', ...) part ensures that only the latest sample for the selected report is included.
      .leftJoinAndSelect(
        'reportWithLatestSample.samples',
        'sample',
        `sample.id = (
          SELECT s.id FROM sample s
          WHERE s."dailyReportId" = "reportWithLatestSample".id AND s."deletedAt" IS NULL
          ORDER BY s."createdAt" DESC
          LIMIT 1
        )`
      )
      .orderBy('well.createdAt', 'DESC');

    if (query.customerId) {
      builder = builder.andWhere('customer_well.customerId = :id', {id: query.customerId});
    }

    if (query.companyId) {
      builder = builder.andWhere('well.companyId = :companyId', { companyId: query.companyId })
    } else if (currentUserRoles.includes(UserRole.COMPANY_ADMIN)) {
      builder = builder.andWhere('well.companyId = :companyId', { companyId: user.companyId })
    }

    if (query.userIds) {
      const ids = query.userIds.split(',');
      builder = builder.andWhere('user_well.userId IN (:...ids)', {ids: ids});
    } else if (!currentUserRoles.includes(UserRole.ADMIN) && !currentUserRoles.includes(UserRole.COMPANY_ADMIN)) {
      builder = builder
        .andWhere('user_well.userId = :userId', { userId: user.id });
    }

    return this.wellRepository.paginate(builder, paginationQuery);
  }

  async create(user: User, data: CreateWellDto): Promise<Well> {
    const well = this.wellRepository.create(data);
    if (data.companyId) {
      well.company = { id: data.companyId } as Company;
    }
    if (data.customerIds?.length) {
      well.customers = data.customerIds.map(id => ({ id: id }) as Customer);
    }
    if (data.userIds?.length) {
      const users = await this.userRepository.find({
        where: { id: In(data.userIds) },
        relations: ['roles'],
      });
      if (
        users.filter(
          user =>
            user.roles
              ?.map(role => role.value)
              .every(role => role !== UserRole.ENGINEER && role !== UserRole.SUPERVISOR),
        ).length
      ) {
        throw new HttpForbiddenError(ErrorCode.IN_VALID_ENGINEER_OR_SUPERVISOR);
      }
      well.users = data.userIds.map(id => ({ id: id }) as User);
    }
    well.archived = false;
    return this.wellRepository.save(well);
  }

  async update(id: string, user: User, data: UpdateWellDto): Promise<boolean> {
    const customers = data.customerIds?.map(id => ({ id }) as Customer);
    const company = data.companyId != null ? {id: data.companyId} as Company : null;
    const validateUsers = await this.userRepository.find({
      where: { id: In(data?.userIds ?? []) },
      relations: ['roles'],
    });
    if (
      validateUsers.filter(
        user =>
          user.roles
            ?.map(role => role.value)
            .every(role => role !== UserRole.ENGINEER && role !== UserRole.SUPERVISOR),
      ).length
    ) {
      throw new HttpForbiddenError(ErrorCode.IN_VALID_ENGINEER_OR_SUPERVISOR);
    }
    const users = data.userIds?.map(id => ({ id }) as User);
    const updateData = { id, users: users, ...data };
    if (customers) {
      updateData['customers'] = customers;
    }
    if (company) {
      updateData['company'] = company;
    }
    await this.wellRepository.save(updateData);
    return true;
  }

  async findOne(id: string, user: User): Promise<Well | null> {
    // return this.wellRepository.findOne({where: {id}});
    let builder = this.wellRepository.createQueryBuilder('well').where('well.id = :id', { id }).withDeleted().andWhere("well.deletedAt IS Null");

    // if (user.roles?.map(role => role.value).includes(UserRole.ENGINEER)) {
    //   builder = builder
    //     .leftJoin('user_well', 'user_well', 'well.id = user_well.wellId')
    //     .andWhere('user_well.userId = :userId', { userId: user.id });
    // }
    return await builder
      .leftJoinAndSelect('well.customers', 'customer')
      .leftJoinAndSelect('well.users', 'user')
      .leftJoinAndSelect('user.roles', 'role')
      .leftJoinAndSelect('well.company', 'company')
      .leftJoinAndSelect('well.dailyReport', 'dailyReport', 'dailyReport.wellId = well.id')
      .leftJoin(
        'well.dailyReport',
        'nextDailyReport',
        'nextDailyReport.wellId = well.id AND dailyReport.createdAt < nextDailyReport.createdAt',
      )
      .andWhere('nextDailyReport.id IS NULL')
      .leftJoinAndSelect('dailyReport.wellInformation', 'wellInformation')
      .leftJoinAndSelect('dailyReport.notes', 'notes')
      .orderBy('dailyReport.createdAt', 'DESC')
      .addOrderBy('user.createdAt', 'DESC')
      .addOrderBy('customer.createdAt', 'DESC')
      .getOne();
  }

  async softDeleteById(id: string): Promise<boolean> {
    await this.wellRepository.softDelete({ id });
    return true;
  }
}
