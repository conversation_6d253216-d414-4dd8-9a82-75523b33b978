import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsNotEmpty, IsNumber, IsString, IsUUID } from 'class-validator';

export class CreateIntervalDto {
  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  wellId: string;

  @ApiProperty({ required: true, example: '10' })
  @IsNotEmpty()
  @IsNumber()
  @IsInt()
  interval: number;

  @ApiProperty({ required: true })
  @IsNotEmpty()
  notes: string;
}
