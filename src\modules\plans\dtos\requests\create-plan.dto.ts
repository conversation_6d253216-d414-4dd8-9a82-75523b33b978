import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsNotEmpty, IsNumber, IsString, IsUUID } from 'class-validator';

export class CreatePlanDto {
  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  wellId: string;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  mudDepth: number;

  @ApiProperty({ required: true })
  @IsInt()
  @IsNotEmpty()
  day: number;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  cost: number;
}
