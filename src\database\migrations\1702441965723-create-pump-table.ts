import { MigrationInterface, QueryRunner, Table } from 'typeorm';
import {
  columnCreatedAt,
  columnDeletedAt,
  columnId,
  columnUpdatedAt,
} from '../constants/columns.constant';

export class CreatePumpTable1702441965723 implements MigrationInterface {
  private tableName = 'pump';
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: this.tableName,
        columns: [
          columnId,
          {
            name: 'dailyReportId',
            type: 'uuid',
            isNullable: false,
          },
          {
            name: 'description',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'inUse',
            type: 'boolean',
            isNullable: true,
          },
          {
            name: 'model',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'linearID',
            type: 'float8',
            isNullable: true,
          },
          {
            name: 'rodOD',
            type: 'float8',
            isNullable: true,
          },
          {
            name: 'strokeLength',
            type: 'float8',
            isNullable: true,
          },
          {
            name: 'efficiency',
            type: 'float8',
            isNullable: true,
          },
          {
            name: 'stroke',
            type: 'float8',
            isNullable: true,
          },
          {
            name: 'displacement',
            type: 'float8',
            isNullable: true,
          },
          {
            name: 'rate',
            type: 'float8',
            isNullable: true,
          },
          {
            name: 'totalDurations',
            type: 'int',
            isNullable: true,
          },
          columnCreatedAt,
          columnUpdatedAt,
          columnDeletedAt,
        ],
      }),
      false,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable(this.tableName);
  }
}
