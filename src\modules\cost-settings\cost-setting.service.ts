import { Injectable } from '@nestjs/common';
import { GetCostSettingsQueryDto } from './dtos/requests/get-cost-settings.dto';
import { CostSettingRepository } from './cost-setting.repository';
import { CostSetting } from './entities/cost-setting.entity';
import { CreateCostSettingDto } from './dtos/requests/create-cost-setting.dto';
import { UpdateCostSettingDto } from './dtos/requests/update-cost-setting.dto';
import { User } from '../users/entities/user.entity';
import { PaginationResponseDto } from '../../common/dtos/pagination.response.dto';
import { PaginationDto } from '../../common/dtos/paginationDto';
import { HttpBadRequestError } from "../../errors/bad-request.error";
import { ErrorCode } from "../../errors/error-code";
import { CostRepository } from "../costs/cost.repository";

@Injectable()
export class CostSettingService {
  constructor(private repository: CostSettingRepository, private costReportRepository: CostRepository) {}

  async findAll(
    user: User,
    query: GetCostSettingsQueryDto,
    paginationQuery: PaginationDto,
  ): Promise<PaginationResponseDto<CostSetting>> {
    let queryBuilder = this.repository
      .createQueryBuilder('costSetting')
      .leftJoinAndSelect('costSetting.company', 'company');
    if (query.companyId) {
      queryBuilder = queryBuilder.andWhere('costSetting.companyId = :companyId', { companyId: query.companyId});
    }
    if (query.type) {
      queryBuilder = queryBuilder.andWhere('costSetting.type = :type', { type: query.type });
    }
    if (query.keyword) {
      queryBuilder = queryBuilder.andWhere('costSetting.name ILIKE :name', {
        name: `%${query.keyword}%`,
      });
    }
    if (query.priceFrom) {
      queryBuilder = queryBuilder.andWhere('costSetting.cost >= :priceFrom', {priceFrom: query.priceFrom });
    }
    if (query.priceTo) {
      queryBuilder = queryBuilder.andWhere('costSetting.cost <= :priceTo', { priceTo: query.priceTo });
    }
    if (query.sortBy) {
      const sortDirection = query.sortDirection ?? "ASC";
      queryBuilder = queryBuilder
        .addOrderBy(`costSetting.${query.sortBy}`, sortDirection === "DESC" ? "DESC" : "ASC");
    } else {
      queryBuilder = queryBuilder.addOrderBy('costSetting.createdAt', 'DESC');
    }
    return this.repository.paginate(queryBuilder, paginationQuery);
  }

  async create(user: User, data: CreateCostSettingDto): Promise<CostSetting> {
    const existCost = await this.repository.findOne({where: {name: data.name}});
    if (existCost) {
      throw new HttpBadRequestError(ErrorCode.COST_EXIST)
    }
    const costSetting = this.repository.create(data);
    costSetting.companyId = data.companyId ?? user.companyId;
    return this.repository.save(costSetting);
  }

  async update(id: string, data: UpdateCostSettingDto): Promise<boolean> {
    await this.repository.update({ id }, { ...data });
    return true;
  }

  async findOne(id: string): Promise<CostSetting | null> {
    return this.repository.findOne({ where: { id }, relations: ['company'] });
  }

  async softDeleteById(id: string): Promise<boolean> {
    const count = await this.costReportRepository.createQueryBuilder('cost').where('cost.costSettingId = :id', {id}).getCount();
    if (count) {
      throw new HttpBadRequestError(ErrorCode.COST_SETTING_BEING_USED);
    }
    await this.repository.softDelete({ id });
    return true;
  }
}
