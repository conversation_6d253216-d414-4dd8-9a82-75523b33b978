import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm';
import { EntityHelper } from '../../../utils/entity-helper';
import { Customer } from '../../customers/entities/customer.entity';

@Entity()
export class CustomerContact extends EntityHelper {
  @ManyToOne(() => Customer, { nullable: false })
  @JoinColumn({ name: 'customerId' })
  customerId: string;

  @Column({ type: String, nullable: false })
  name: string;

  @Column({ type: String, nullable: true })
  address: string;

  @Column({ type: String, nullable: true })
  officePhone: string;

  @Column({ type: String, nullable: true })
  mobilePhone: string;

  @Column({ type: String, nullable: true })
  emailAddress: string;

  @Column({ type: String, nullable: true })
  notes: string;

  @Column({ type: Boolean, default: false })
  primaryContact: boolean;

  @Column({ type: Boolean, default: false })
  notifyOnNewReport: boolean;
}
