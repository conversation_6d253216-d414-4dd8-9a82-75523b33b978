import { ApiProperty } from '@nestjs/swagger';
import {
  IsArray,
  IsEmail,
  IsNotEmpty,
  IsOptional,
  IsUUID,
  Length,
  Matches,
  Validate,
} from 'class-validator';
import { Transform } from 'class-transformer';
import { lowerCaseTransformer } from '../../../../common/transformers/lower-case.transformer';
import { IsEmailUnique } from '../../../../common/decorators';

export class CreateUserDto {
  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsUUID()
  companyId: string;

  @ApiProperty({ example: '<EMAIL>', required: true })
  @Transform(lowerCaseTransformer)
  @IsNotEmpty()
  @IsEmail()
  // @Validate(IsEmailUnique)
  email: string;

  @ApiProperty({ required: true })
  @IsNotEmpty()
  firstName: string;

  @ApiProperty({ required: true })
  @IsNotEmpty()
  lastName: string;

  @ApiProperty({ required: false })
  @IsNotEmpty()
  @IsOptional()
  // @Length(10, 15, { message: 'Phone number must be between 10 and 15 characters long' })
  // @Matches(/^\d+$/, { message: 'Phone number must contain only digits' })
  officePhone?: string;

  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsOptional()
  // @Length(10, 15, { message: 'Phone number must be between 10 and 15 characters long' })
  // @Matches(/^\d+$/, { message: 'Phone number must contain only digits' })
  mobilePhone?: string;

  @ApiProperty({ required: false })
  @IsNotEmpty()
  @IsOptional()
  address?: string;

  @ApiProperty({ required: false })
  @IsNotEmpty()
  @IsOptional()
  note?: string;

  @ApiProperty({
    required: true,
    description: 'admin: 1, supervisor: 2, engineer: 3, companyAdmin: 4',
    example: [1],
  })
  @IsArray()
  userRoles: number[];
}
