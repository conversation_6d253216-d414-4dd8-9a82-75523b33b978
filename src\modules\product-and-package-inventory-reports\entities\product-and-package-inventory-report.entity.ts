import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany } from 'typeorm';
import { EntityHelper } from '../../../utils/entity-helper';
import { ProductAndPackageInventoryItem } from '../../product-and-package-inventory-items/entities/product-and-package-inventory-item.entity';
import { Product } from '../../products/entities/product.entity';
import { ProductAndPackageInventory } from '../../product-and-package-inventorys/entities/product-and-package-inventory.entity';

@Entity()
export class ProductAndPackageInventoryReport extends EntityHelper {
  @ManyToOne(
    () => ProductAndPackageInventory,
    productAndPackageInventory => productAndPackageInventory.reportItems,
    { nullable: false },
  )
  @JoinColumn({ name: 'productAndPackageInventoryId' })
  productAndPackageInventory: ProductAndPackageInventory;

  @ManyToOne(() => Product, product => product.reports, { nullable: false })
  @JoinColumn({ name: 'productId' })
  product: Product;

  @Column({ type: 'float', nullable: false })
  totalCost: number;

  @Column({ type: 'int', nullable: false })
  quantity: number;

  @OneToMany(
    () => ProductAndPackageInventoryItem,
    productAndPackageInventoryItem =>
      productAndPackageInventoryItem.productAndPackageInventoryReport,
  )
  items: ProductAndPackageInventoryItem[];
}
