import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { PaginationResponseDto } from '../../common/dtos/pagination.response.dto';
import { AuthGuard } from '../auth/guards/auth.guard';

import { PaginationDto } from '../../common/dtos/paginationDto';
import { toDto, toDtos, toPaginateDtos } from '../../common/transformers/dto.transformer';

import { RolesDecorator } from '../auth/decorators/roles.decorator';
import { UserRole } from '../roles/enums/roles.enum';
import { RolesGuard } from '../auth/guards/roles.guard';
import { SampleService } from './sample.service';
import { GetSampleDto } from './dtos/requests/get-sample.dto';
import { SampleResponseDto } from './dtos/responses/sample.response.dto';
import { CreateSampleDto } from './dtos/requests/create-sample.dto';
import { UpdateSampleDto } from './dtos/requests/udpate-sample.dto';
import { Responder } from '../../common/decorators/responder.decorator';
import { CurrentUser } from '../../common/decorators/current-user.decorator';
import { User } from '../users/entities/user.entity';
import { SampleDataChartResponseDto } from './dtos/responses/sample-data-chart.response.dto';
import { SampleDetailResponseDto } from './dtos/responses/sample-detail.response.dto';

@ApiTags('Sample')
@Controller('samples')
@ApiBearerAuth('access-token')
@RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN, UserRole.SUPERVISOR, UserRole.ENGINEER)
@UseGuards(AuthGuard, RolesGuard)
export class SampleController {
  constructor(private readonly service: SampleService) {}

  @Get()
  @ApiOperation({ description: 'Get samples' })
  @Responder.handle('Get samples')
  @HttpCode(HttpStatus.OK)
  async findAll(
    @Query() query: GetSampleDto,
    @Query() paginationQuery: PaginationDto,
  ): Promise<PaginationResponseDto<SampleResponseDto>> {
    const data = await this.service.findAll(query, paginationQuery);
    return toPaginateDtos(SampleResponseDto, data);
  }

  @Get('/chart/:id')
  @ApiOperation({ description: 'Get samples for chart' })
  @Responder.handle('Get samples for chart')
  @HttpCode(HttpStatus.OK)
  async findAllChartData(@Param('id') id: string): Promise<SampleDataChartResponseDto[]> {
    const data = await this.service.findAllChartSample(id);
    return toDtos(SampleDataChartResponseDto, data);
  }

  @Post()
  @ApiOperation({ description: 'Create samples' })
  @Responder.handle('Create samples')
  @HttpCode(HttpStatus.CREATED)
  async create(
    @CurrentUser() user: User,
    @Body() data: CreateSampleDto,
  ): Promise<SampleResponseDto> {
    const value = await this.service.create(user, data);
    return toDto(SampleResponseDto, value);
  }

  @Put('/:id')
  @ApiOperation({ description: 'Update sample' })
  @Responder.handle('Update sample')
  @HttpCode(HttpStatus.CREATED)
  async update(
    @CurrentUser() user: User,
    @Param('id') id: string,
    @Body() data: UpdateSampleDto,
  ): Promise<boolean> {
    return this.service.update(user, id, data);
  }

  @Get('/:id')
  @ApiOperation({ description: 'Get sample detail' })
  @Responder.handle('Get sample detail')
  @HttpCode(HttpStatus.OK)
  async detail(@Param('id') id: string): Promise<SampleDetailResponseDto> {
    const data = await this.service.findOne(id);
    return toDto(SampleDetailResponseDto, data);
  }

  @Delete('/:id')
  @ApiOperation({ description: 'Delete sample' })
  @Responder.handle('Delete sample')
  @HttpCode(HttpStatus.OK)
  delete(@CurrentUser() user: User, @Param('id') id: string): Promise<boolean> {
    return this.service.softDeleteById(user, id);
  }
}
