import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsN<PERSON>ber, IsOptional, IsString, IsUUID } from 'class-validator';

export class UpdateDrillBitDto {
  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  dailyReportId: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  bitNo?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  type?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  iadcType?: string;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  bitSize?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  depth?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  bitRunDuration?: number;
}
