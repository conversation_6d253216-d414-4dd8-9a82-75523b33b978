import { MigrationInterface, QueryRunner, Table } from 'typeorm';
import {
  columnCreatedAt,
  columnDeletedAt,
  columnId,
  columnUpdatedAt,
} from '../constants/columns.constant';

export class CreateCasedHoleTable1702441889967 implements MigrationInterface {
  private tableName = 'cased_hole';
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: this.tableName,
        columns: [
          columnId,
          {
            name: 'dailyReportId',
            type: 'uuid',
            isNullable: false,
          },
          {
            name: 'description',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'outsideDiameter',
            type: 'float8',
            isNullable: true,
          },
          {
            name: 'weight',
            type: 'float8',
            isNullable: true,
          },
          {
            name: 'insideDiameter',
            type: 'float8',
            isNullable: true,
          },
          {
            name: 'topDepth',
            type: 'float8',
            isNullable: true,
          },
          {
            name: 'casingShoeDepth',
            type: 'float8',
            isNullable: true,
          },
          {
            name: 'casingLength',
            type: 'float8',
            isNullable: true,
          },
          columnCreatedAt,
          columnUpdatedAt,
          columnDeletedAt,
        ],
      }),
      false,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable(this.tableName);
  }
}
