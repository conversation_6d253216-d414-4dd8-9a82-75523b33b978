import { MigrationInterface, QueryRunner, Table } from 'typeorm';
import {
  columnCreatedAt,
  columnDeletedAt,
  columnId,
  columnUpdatedAt,
} from '../constants/columns.constant';

export class CreateProductAndPackageInventoryTable1702442058853 implements MigrationInterface {
  private tableName = 'product_and_package_inventory';
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: this.tableName,
        columns: [
          columnId,
          {
            name: 'dailyReportId',
            type: 'uuid',
            isNullable: true,
          },
          {
            name: 'description',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'totalCost',
            type: 'float8',
            isNullable: true,
          },
          {
            name: 'totalProductVolume',
            type: 'float8',
            isNullable: true,
          },
          {
            name: 'weightMaterials',
            type: 'float8',
            isNullable: true,
          },
          {
            name: 'baseFluid',
            type: 'float8',
            isNullable: true,
          },
          {
            name: 'addWater',
            type: 'float8',
            isNullable: true,
          },
          {
            name: 'totalVolume',
            type: 'float8',
            isNullable: true,
          },
          columnCreatedAt,
          columnUpdatedAt,
          columnDeletedAt,
        ],
      }),
      false,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable(this.tableName);
  }
}
