import { Module } from '@nestjs/common';
import { SolidControlEquipmentTypeSeedService } from './solid-control-equipment-type-seed.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DailyReport } from '../../../modules/daily-reports/entities/daily-report.entity';
import { Company } from '../../../modules/companies/entities/company.entity';
import { SolidControlEquipmentType } from '../../../modules/solid-control-equipment-type/entities/solid-control-equipment-type.entity';

@Module({
  imports: [TypeOrmModule.forFeature([DailyReport, SolidControlEquipmentType, Company])],
  providers: [SolidControlEquipmentTypeSeedService],
  exports: [SolidControlEquipmentTypeSeedService],
})
export class SolidControlEquipmentTypeSeedModule {}
