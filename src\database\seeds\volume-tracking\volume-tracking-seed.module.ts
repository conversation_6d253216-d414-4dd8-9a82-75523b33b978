import { Module } from '@nestjs/common';
import { VolumeTrackingSeedService } from './volume-tracking-seed.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DailyReport } from '../../../modules/daily-reports/entities/daily-report.entity';
import { VolumeTracking } from '../../../modules/volum-trackings/entities/volume-tracking.entity';

@Module({
  imports: [TypeOrmModule.forFeature([DailyReport, VolumeTracking])],
  providers: [VolumeTrackingSeedService],
  exports: [VolumeTrackingSeedService],
})
export class VolumeTrackingSeedModule {}
