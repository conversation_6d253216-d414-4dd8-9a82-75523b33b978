import { Module } from '@nestjs/common';
import { CostSettingSeedService } from './cost-setting-seed.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CostSetting } from '../../../modules/cost-settings/entities/cost-setting.entity';
import { Company } from '../../../modules/companies/entities/company.entity';

@Module({
  imports: [TypeOrmModule.forFeature([CostSetting, Company])],
  providers: [CostSettingSeedService],
  exports: [CostSettingSeedService],
})
export class CostSettingSeedModule {}
