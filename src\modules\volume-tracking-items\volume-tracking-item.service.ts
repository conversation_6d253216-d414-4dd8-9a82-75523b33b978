import { Injectable } from '@nestjs/common';
import { VolumeTrackingItemRepository } from './volume-tracking-item.repository';
import { CreateVolumeTrackingItemDto } from './dtos/requests/create-volume-tracking-item.dto';
import { VolumeTrackingItem } from './entities/volume-tracking-item.entity';
import { UpdateVolumeTrackingItemDto } from './dtos/requests/update-volume-tracking-item.dto';
import { VolumeTracking } from '../volum-trackings/entities/volume-tracking.entity';
import { VolumeTrackingRepository } from '../volum-trackings/volume-tracking.repository';
import { VolumeTrackingItemTypeEnum } from './enums/volume-tracking-item-type.enum';

@Injectable()
export class VolumeTrackingItemService {
  constructor(
    private repository: VolumeTrackingItemRepository,
    private volumeTrackingRepository: VolumeTrackingRepository,
  ) {}

  async create(data: CreateVolumeTrackingItemDto): Promise<VolumeTrackingItem> {
    const volumeItem = this.repository.create(data);
    volumeItem.volumeTracking = { id: data.volumeTrackingId } as VolumeTracking;
    const result = await this.repository.save(volumeItem);
    const totalAdditions = await this.repository.getTotalVolumes(
      data.volumeTrackingId!,
      VolumeTrackingItemTypeEnum.addition,
    );
    const totalLosses = await this.repository.getTotalVolumes(
      data.volumeTrackingId!,
      VolumeTrackingItemTypeEnum.loss,
    );
    const totalTransfers = await this.repository.getTotalVolumes(
      data.volumeTrackingId!,
      VolumeTrackingItemTypeEnum.transfer,
    );
    const calculatedVolume = totalAdditions - totalLosses - totalTransfers;
    await this.volumeTrackingRepository.update(
      { id: data.volumeTrackingId },
      { totalAdditions, totalLosses, totalTransfers, calculatedVolume },
    );
    return result;
  }

  async update(id: string, data: UpdateVolumeTrackingItemDto): Promise<boolean> {
    const volumeTracking = { id: data.volumeTrackingId } as VolumeTracking;
    const dataUpdate = { volumeTracking, ...data };
    delete dataUpdate.volumeTrackingId;
    await this.repository.update({ id }, dataUpdate);
    const totalAdditions = await this.repository.getTotalVolumes(
      data.volumeTrackingId!,
      VolumeTrackingItemTypeEnum.addition,
    );
    const totalLosses = await this.repository.getTotalVolumes(
      data.volumeTrackingId!,
      VolumeTrackingItemTypeEnum.loss,
    );
    const totalTransfers = await this.repository.getTotalVolumes(
      data.volumeTrackingId!,
      VolumeTrackingItemTypeEnum.transfer,
    );
    const calculatedVolume = totalAdditions - totalLosses - totalTransfers;
    await this.volumeTrackingRepository.update(
      { id: data.volumeTrackingId },
      { totalAdditions, totalLosses, totalTransfers, calculatedVolume },
    );
    return true;
  }

  async findOne(id: string): Promise<VolumeTrackingItem | null> {
    return this.repository.findOne({ where: { id } });
  }

  async softDeleteById(id: string): Promise<boolean> {
    const volumeTrackingItem = await this.repository.findOne({
      where: { id },
      relations: ['volumeTracking'],
    });
    await this.repository.softDelete({ id });
    if (volumeTrackingItem?.volumeTracking?.id) {
      const volumeTrackingId = volumeTrackingItem!.volumeTracking!.id!;
      const totalAdditions = await this.repository.getTotalVolumes(
        volumeTrackingId,
        VolumeTrackingItemTypeEnum.addition,
      );
      const totalLosses = await this.repository.getTotalVolumes(
        volumeTrackingId,
        VolumeTrackingItemTypeEnum.loss,
      );
      const totalTransfers = await this.repository.getTotalVolumes(
        volumeTrackingId,
        VolumeTrackingItemTypeEnum.transfer,
      );
      const calculatedVolume = totalAdditions - totalLosses - totalTransfers;
      await this.volumeTrackingRepository.update(
        { id: volumeTrackingId },
        { totalAdditions, totalLosses, totalTransfers, calculatedVolume },
      );
    }
    return true;
  }
}
