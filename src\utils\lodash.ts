import getLoash from 'lodash/get';
import uniqLoash from 'lodash/uniq';
import differenceLoash from 'lodash/difference';
import isStringLodash from 'lodash/isString';
import escapeRegExpLodash from 'lodash/escapeRegExp';
import isEmptyLodash from 'lodash/isEmpty';
import intersectionLodash from 'lodash/intersection';
import isNumberLodash from 'lodash/isNumber';
import setLoash from 'lodash/set';
import groupByLodash from 'lodash/groupBy';
import differenceByLoash from 'lodash/differenceBy';
import pickLoash from 'lodash/pick';
import toStringLoash from 'lodash/toString';
import sampleLodash from 'lodash/sample';
import firstLodash from 'lodash/first';
import isArrayLodash from 'lodash/isArray';
import keyByLodash from 'lodash/keyBy';
import isNilLodash from 'lodash/isNil';
import toArrayLodash from 'lodash/toArray';
import isNaNLodash from 'lodash/isNaN';
import replaceLodash from 'lodash/replace';
import chunkLodash from 'lodash/chunk';
import uniqByLodash from 'lodash/uniqBy';
import isObjectLodash from 'lodash/isObject';
import flattenLodash from 'lodash/flatten';
import camelCaseLodash from 'lodash/camelCase';
import sortByLodash from 'lodash/sortBy';
import sumByLodash from 'lodash/sumBy';
import intersectionWithLodash from 'lodash/intersectionWith';
import mapLodash from 'lodash/map';

export const get = getLoash;
export const uniq = uniqLoash;
export const isEmpty = isEmptyLodash;
export const difference = differenceLoash;
export const isString = isStringLodash;
export const escapeRegExp = escapeRegExpLodash;
export const intersection = intersectionLodash;
export const isNumber = isNumberLodash;
export const set = setLoash;
export const groupBy = groupByLodash;
export const differenceBy = differenceByLoash;
export const pick = pickLoash;
export const toString = toStringLoash;
export const sample = sampleLodash;
export const first = firstLodash;
export const isArray = isArrayLodash;
export const keyBy = keyByLodash;
export const isNil = isNilLodash;
export const toArray = toArrayLodash;
export const isNaN = isNaNLodash;
export const replace = replaceLodash;
export const chunk = chunkLodash;
export const uniqBy = uniqByLodash;
export const isObject = isObjectLodash;
export const camelCase = camelCaseLodash;
export const flatten = flattenLodash;
export const sortBy = sortByLodash;
export const sumBy = sumByLodash;
export const intersectionWith = intersectionWithLodash;
export const map = mapLodash;
