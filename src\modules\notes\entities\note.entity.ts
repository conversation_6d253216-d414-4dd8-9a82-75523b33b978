import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm';
import { EntityHelper } from '../../../utils/entity-helper';
import { DailyReport } from '../../daily-reports/entities/daily-report.entity';

@Entity()
export class Note extends EntityHelper {
  @ManyToOne(() => DailyReport, { nullable: false })
  @JoinColumn({ name: 'dailyReportId' })
  dailyReport: DailyReport;

  @Column({ type: String, nullable: false })
  title: string;

  @Column({ type: String, nullable: false })
  notes: string;
}
