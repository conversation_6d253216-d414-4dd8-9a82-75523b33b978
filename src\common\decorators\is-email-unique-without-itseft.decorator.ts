import {
  ValidationArguments,
  ValidatorConstraint,
  ValidatorConstraintInterface,
} from 'class-validator';
import { Injectable } from '@nestjs/common';
import { UserService } from 'src/modules/users/user.service';

@ValidatorConstraint({ async: true })
@Injectable()
export class IsEmailUniqueWithoutSelf implements ValidatorConstraintInterface {
  constructor(private readonly userService: UserService) {}

  async validate(email: string, args: ValidationArguments) {
    const id = (args.object as any).id;
    const user = await this.userService.findOne({ email: email });
    if (user && user.id === id) {
      return true;
    }
    return !user;
  }

  defaultMessage() {
    return 'Email $value already exists. Choose another email.';
  }
}
