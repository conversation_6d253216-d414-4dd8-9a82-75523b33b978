import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsN<PERSON>ber, IsOptional, IsString, IsUUID } from 'class-validator';

export class CreateCasedHoleDto {
  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  dailyReportId: string;

  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({ required: false })
  @IsNumber()
  @IsOptional()
  outsideDiameter?: number;

  @ApiProperty({ required: false })
  @IsNumber()
  @IsOptional()
  weight?: number;

  @ApiProperty({ required: false })
  @IsNumber()
  @IsOptional()
  insideDiameter?: number;

  @ApiProperty({ required: false })
  @IsNumber()
  @IsOptional()
  topDepth?: number;

  @ApiProperty({ required: false })
  @IsNumber()
  @IsOptional()
  casingShoeDepth?: number;

  @ApiProperty({ required: false })
  @IsNumber()
  @IsOptional()
  casingLength?: number;
}
