import { Body, Controller, Delete, Get, HttpCode, HttpStatus, Param, Post, Put, UseGuards } from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiTags } from "@nestjs/swagger";
import { AuthGuard } from "../auth/guards/auth.guard";
import { toDto } from "../../common/transformers/dto.transformer";

import { RolesDecorator } from "../auth/decorators/roles.decorator";
import { UserRole } from "../roles/enums/roles.enum";
import { RolesGuard } from "../auth/guards/roles.guard";
import { WellInformationService } from "./well-information.service";
import { CreateWellInformationDto } from "./dtos/requests/create-well-information.dto";
import { WellInformationResponseDto } from "./dtos/responses/well-information.response.dto";
import { UpdateWellInformationDto } from "./dtos/requests/update-well-information.dto";
import { Responder } from "../../common/decorators/responder.decorator";
import { CurrentUser } from "../../common/decorators/current-user.decorator";
import { NozzleService } from "../nozzles/nozzle.service";

@ApiTags('WellInformation')
@Controller('wellInformations')
@ApiBearerAuth('access-token')
@RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN, UserRole.SUPERVISOR, UserRole.ENGINEER)
@UseGuards(AuthGuard, RolesGuard)
export class WellInformationController {
  constructor(private readonly wellInformationService: WellInformationService, private readonly nozzleService: NozzleService) {}

  @Post()
  @ApiOperation({ description: 'Create well information' })
  @Responder.handle('Create well information')
  @HttpCode(HttpStatus.CREATED)
  async create(
    @CurrentUser() user,
    @Body() data: CreateWellInformationDto,
  ): Promise<WellInformationResponseDto> {
    const value = await this.wellInformationService.create(user, data);
    return toDto(WellInformationResponseDto, value);
  }

  @Put('/:id')
  @ApiOperation({ description: 'Update well information' })
  @Responder.handle('Update well information')
  @HttpCode(HttpStatus.CREATED)
  async update(
    @CurrentUser() user,
    @Param('id') id: string,
    @Body() data: UpdateWellInformationDto,
  ): Promise<boolean> {
    return this.wellInformationService.update(user, id, data);
  }

  @Get('/:id')
  @ApiOperation({ description: 'Get well information detail' })
  @Responder.handle('Get well information detail')
  @HttpCode(HttpStatus.OK)
  async detail(@Param('id') id: string): Promise<WellInformationResponseDto> {
    const data = await this.wellInformationService.findOne(id);
    if (data?.dailyReport?.id) {
      data['tfa'] = await this.nozzleService.getTfa(data!.dailyReport!.id);
    }
    return toDto(WellInformationResponseDto, data);
  }

  @Get('wellInformationToday/:dailyReportId')
  @ApiOperation({ description: 'Get well information by well today' })
  @Responder.handle('Get well information by well today')
  @HttpCode(HttpStatus.OK)
  async wellInformationToday(
    @Param('dailyReportId') dailyReportId: string,
  ): Promise<WellInformationResponseDto> {
    const data = await this.wellInformationService.findOneToday(dailyReportId);
    if (data?.dailyReport?.id) {
      data['tfa'] = await this.nozzleService.getTfa(data!.dailyReport!.id);
    }
    return toDto(WellInformationResponseDto, data);
  }

  @Delete('/:id')
  @ApiOperation({ description: 'Delete well information' })
  @Responder.handle('Delete well information')
  @HttpCode(HttpStatus.OK)
  delete(@Param('id') id: string): Promise<boolean> {
    return this.wellInformationService.softDeleteById(id);
  }
}
