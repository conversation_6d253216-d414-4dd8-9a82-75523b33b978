import { Module } from '@nestjs/common';
import { SolidControlEquipmentInputSeedService } from './solid-control-equipment-input-seed.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SolidControlEquipmentTime } from '../../../modules/solid-control-equipment-time/entities/solid-control-equipment-time.entity';
import { SolidControlEquipment } from '../../../modules/solid-control-equipment/entities/solid-control-equipment.entity';
import { SolidControlEquipmentInput } from '../../../modules/solid-control-equipment-input/entities/solid-control-equipment-input.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      SolidControlEquipmentTime,
      SolidControlEquipment,
      SolidControlEquipmentInput,
    ]),
  ],
  providers: [SolidControlEquipmentInputSeedService],
  exports: [SolidControlEquipmentInputSeedService],
})
export class SolidControlEquipmentInputSeedModule {}
