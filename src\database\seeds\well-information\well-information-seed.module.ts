import { Module } from '@nestjs/common';
import { WellInformationSeedService } from './well-information-seed.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DailyReport } from '../../../modules/daily-reports/entities/daily-report.entity';
import { WellInformation } from '../../../modules/well-informations/entities/well-information.entity';
import { User } from '../../../modules/users/entities/user.entity';

@Module({
  imports: [TypeOrmModule.forFeature([DailyReport, WellInformation, User])],
  providers: [WellInformationSeedService],
  exports: [WellInformationSeedService],
})
export class WellInformationSeedModule {}
