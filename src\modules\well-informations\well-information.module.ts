import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IsExist } from '../../utils/validators/is-exists.validator';
import { IsNotExist } from '../../utils/validators/is-not-exists.validator';
import { WellInformationService } from './well-information.service';
import { WellInformation } from './entities/well-information.entity';
import { JwtModule } from '@nestjs/jwt';
import { WellInformationController } from './well-information.controller';
import { WellInformationRepository } from './well-information.repository';
import { DailyReportRepository } from '../daily-reports/daily-report.repository';
import { NozzleService } from "../nozzles/nozzle.service";
import { NozzleRepository } from "../nozzles/nozzle.repository";
import { Nozzle } from "../nozzles/entities/nozzle.entity";

@Module({
  imports: [TypeOrmModule.forFeature([WellInformation, Nozzle]), JwtModule.register({})],
  controllers: [WellInformationController],
  providers: [
    IsExist,
    IsNotExist,
    WellInformationService,
    WellInformationRepository,
    DailyReportRepository,
    NozzleRepository,
    NozzleService,
  ],
  exports: [WellInformationService],
})
export class WellInformationModule {}
