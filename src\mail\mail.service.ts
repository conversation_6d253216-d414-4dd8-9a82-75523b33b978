import { Injectable } from '@nestjs/common';
import { MailerService } from '@nestjs-modules/mailer';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class MailService {
  constructor(
    private readonly mailerService: MailerService,
    private readonly configService: ConfigService,
  ) {}

  private readonly baseUrl = this.configService.get('app.backendDomain', { infer: true });

  async sendOTPConfirmation(email: string, name: string, otp: string) {
    try {
      await this.mailerService.sendMail({
        to: email,
        subject: 'The Muddy Software - Forgot password PIN',
        template: './forgot-password',
        context: {
          name: name,
          otp,
          baseUrl: this.baseUrl,
        },
      });
    } catch (error) {
      console.error('Send email failed', error);
    }
  }

  async sendActiveConfirmation(email: string, name: string, password: string) {
    try {
      await this.mailerService.sendMail({
        to: email,
        subject: 'The Muddy Software - Active confirm',
        template: './active-confirm',
        context: {
          name: name,
          password: password,
          baseUrl: this.baseUrl,
        },
      });
    } catch (error) {
      console.error('Send email failed', error);
    }
  }

  async sendResetPasswordMail(email: string, name: string, url: string) {
    await this.mailerService.sendMail({
      to: email,
      subject: 'The Muddy Software - Password reset',
      template: './password-reset',
      context: {
        email: email,
        name: name,
        url,
        baseUrl: this.baseUrl,
      },
    });
  }

  async sendInviteMail(email: string, company: string, name: string, url: string) {
    await this.mailerService.sendMail({
      to: email,
      subject: `The Muddy Software - You are invited to ${company}`,
      template: './invite-user',
      context: {
        email: email,
        name: name,
        url,
        baseUrl: this.baseUrl,
      },
    });
  }
}
