import { Exclude, Expose, Type } from 'class-transformer';
import { RoleResponseDto } from '../../../roles/dtos/responses/role.response.dto.js';

@Exclude()
export class UserProfileResponseDto {
  @Expose()
  id: string;

  @Expose()
  email: string;

  @Expose()
  firstName: string;

  @Expose()
  lastName: string;

  @Expose()
  companyId: string;

  @Expose()
  officePhone: string;

  @Expose()
  mobilePhone: string;

  @Expose()
  address: string;

  @Expose()
  note: string;

  @Expose()
  avatar?: string;

  @Expose()
  @Type(() => RoleResponseDto)
  roles: RoleResponseDto[];

  @Expose()
  status: number;
}
