import { Exclude, Expose, Type } from 'class-transformer';
import { SolidControlEquipmentTypeResponseDto } from '../../../solid-control-equipment-type/dtos/responses/solid-control-equipment-type.response.dto';
import { SolidControlEquipmentTimeResponseDto } from '../../../solid-control-equipment-time/dtos/responses/solid-control-equipment-time.response.dto';
import { SolidControlEquipmentInputResponseDto } from '../../../solid-control-equipment-input/dtos/responses/solid-control-equipment-input.response.dto';

@Exclude()
export class SolidControlEquipmentResponseDto {
  @Expose()
  id: string;

  @Expose()
  @Type(() => SolidControlEquipmentTypeResponseDto)
  type: SolidControlEquipmentTypeResponseDto;

  @Expose()
  screen: number;

  @Expose()
  totalDurations: number;

  @Expose()
  @Type(() => SolidControlEquipmentTimeResponseDto)
  durations: SolidControlEquipmentTimeResponseDto[];

  @Expose()
  @Type(() => SolidControlEquipmentInputResponseDto)
  inputs: SolidControlEquipmentInputResponseDto[];
}
