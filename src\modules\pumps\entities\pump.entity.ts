import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyTo<PERSON>ne, OneToMany } from 'typeorm';
import { <PERSON>tityHelper } from '../../../utils/entity-helper';
import { DailyReport } from '../../daily-reports/entities/daily-report.entity';
import { PumpDuration } from '../../pump-durations/entities/pump-duration.entity';

@Entity()
export class Pump extends EntityHelper {
  @ManyToOne(() => DailyReport, { nullable: false })
  @JoinColumn({ name: 'dailyReportId' })
  @Column({ type: String, nullable: false })
  dailyReportId: string;

  @OneToMany(() => PumpDuration, duration => duration.pump, { nullable: false })
  durations: PumpDuration[];

  @Column({ type: String, nullable: false })
  description: string;

  @Column({ type: 'boolean', nullable: false })
  inUse: boolean;

  @Column({ type: String, nullable: false })
  model: string;

  @Column({ type: 'float', nullable: false })
  linearID: number;

  @Column({ type: 'float', nullable: false })
  rodOD: number;

  @Column({ type: 'float', nullable: false })
  strokeLength: number;

  @Column({ type: 'float', nullable: false })
  efficiency: number;

  @Column({ type: 'float', nullable: false })
  stroke: number;

  @Column({ type: 'float', nullable: false })
  displacement: number;

  @Column({ type: 'float', nullable: false })
  rate: number;

  @Column({ type: 'int', nullable: false })
  totalDurations: number;
}
