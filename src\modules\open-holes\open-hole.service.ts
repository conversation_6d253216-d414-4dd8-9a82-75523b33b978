import { Injectable } from '@nestjs/common';
import { SelectQueryBuilder } from 'typeorm';
import { PaginationDto } from '../../common/dtos/paginationDto';
import { PaginationResponseDto } from '../../common/dtos/pagination.response.dto';

import { OpenHoleRepository } from './open-hole.repository';
import { GetOpenHoleDto } from './dtos/requests/get-open-hole.dto';
import { OpenHole } from './entities/open-hole.entity';
import { CreateOpenHoleDto } from './dtos/requests/create-open-hole.dto';
import { UpdateOpenHoleDto } from './dtos/requests/update-open-hole.dto';
import { DailyReport } from '../daily-reports/entities/daily-report.entity';
import { DailyReportRepository } from '../daily-reports/daily-report.repository';
import { User } from '../users/entities/user.entity';

@Injectable()
export class OpenHoleService {
  constructor(
    private openHoleRepository: OpenHoleRepository,
    private reportRepository: DailyReportRepository,
  ) {}

  async findAll(
    query: GetOpenHoleDto,
    paginationQuery: PaginationDto,
  ): Promise<PaginationResponseDto<OpenHole>> {
    const queryBuilder: SelectQueryBuilder<OpenHole> = this.openHoleRepository
      .createQueryBuilder('openHole')
      .where('openHole.dailyReportId = :dailyReportId', { dailyReportId: query.dailyReportId })
      .orderBy('openHole.createdAt', 'DESC')
      .addOrderBy('openHole.measuredDepth', 'DESC');
    return this.openHoleRepository.paginate(queryBuilder, paginationQuery);
  }

  async create(user: User, data: CreateOpenHoleDto): Promise<OpenHole> {
    await this.reportRepository.updateReportUpdatedBy(user, data.dailyReportId);
    const openHole = await this.openHoleRepository.create(data);
    openHole.dailyReport = { id: data.dailyReportId } as DailyReport;
    return this.openHoleRepository.save(openHole);
  }

  async update(user: User, id: string, data: UpdateOpenHoleDto): Promise<boolean> {
    await this.reportRepository.updateReportUpdatedBy(user, data.dailyReportId!);
    const dailyReport = { id: data.dailyReportId } as DailyReport;
    const dataUpdate = { dailyReport, ...data };
    delete dataUpdate.dailyReportId;
    await this.openHoleRepository.update({ id }, dataUpdate);
    return true;
  }

  async findOne(id: string): Promise<OpenHole | null> {
    return this.openHoleRepository.findOne({ where: { id } });
  }

  async softDeleteById(user: User, id: string): Promise<boolean> {
    const item = await this.openHoleRepository.findOne({
      where: { id },
      relations: ['dailyReport'],
    });
    if (item?.dailyReport?.id) {
      await this.reportRepository.updateReportUpdatedBy(user, item!.dailyReport.id!);
    }
    await this.openHoleRepository.softDelete({ id });
    return true;
  }
}
