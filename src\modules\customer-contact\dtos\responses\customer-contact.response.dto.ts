import { Exclude, Expose } from 'class-transformer';

@Exclude()
export class CustomerContactResponseDto {
  @Expose()
  id: string;

  @Expose()
  customerId: string;

  @Expose()
  name: string;

  @Expose()
  address: string;

  @Expose()
  officePhone: string;

  @Expose()
  mobilePhone: string;

  @Expose()
  emailAddress: string;

  @Expose()
  notes: string;

  @Expose()
  primaryContact: boolean;

  @Expose()
  notifyOnNewReport: boolean;

  @Expose()
  createdAt: string;
}
