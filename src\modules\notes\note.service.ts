import { Injectable } from '@nestjs/common';
import { SelectQueryBuilder } from 'typeorm';
import { CreateNoteDto } from './dtos/requests/create-note.dto';
import { NoteRepository } from './note.repository';
import { PaginationDto } from '../../common/dtos/paginationDto';
import { PaginationResponseDto } from '../../common/dtos/pagination.response.dto';
import { Note } from './entities/note.entity';
import { GetNoteDto } from './dtos/requests/get-note.dto';
import { UpdateNoteDto } from './dtos/requests/update-note.dto';
import { DailyReportRepository } from '../daily-reports/daily-report.repository';
import { User } from '../users/entities/user.entity';
import { DailyReport } from '../daily-reports/entities/daily-report.entity';

@Injectable()
export class NoteService {
  constructor(
    private repository: NoteRepository,
    private reportRepository: DailyReportRepository,
  ) {}

  async findAll(
    query: GetNoteDto,
    paginationQuery: PaginationDto,
  ): Promise<PaginationResponseDto<Note>> {
    const queryBuilder: SelectQueryBuilder<Note> = this.repository
      .createQueryBuilder('note')
      .where('note.dailyReportId = :dailyReportId', { dailyReportId: query.dailyReportId })
      .orderBy('note.createdAt', 'DESC');
    return this.repository.paginate(queryBuilder, paginationQuery);
  }

  async create(user: User, data: CreateNoteDto): Promise<Note> {
    await this.reportRepository.updateReportUpdatedBy(user, data.dailyReportId);
    const createData = {
      dailyReport: { id: data.dailyReportId } as DailyReport,
      ...data,
    };
    return this.repository.save(this.repository.create(createData));
  }

  async update(user: User, id: string, data: UpdateNoteDto): Promise<boolean> {
    await this.reportRepository.updateReportUpdatedBy(user, data.dailyReportId!);
    const dailyReport = { id: data.dailyReportId } as DailyReport;
    const dataUpdate = { dailyReport, ...data };
    delete dataUpdate.dailyReportId;
    await this.repository.update({ id }, dataUpdate);
    return true;
  }

  async findOne(id: string): Promise<Note | null> {
    return this.repository.findOne({ where: { id } });
  }

  async softDeleteById(user: User, id: string): Promise<boolean> {
    const item = await this.repository.findOne({ where: { id }, relations: ['dailyReport'] });
    if (item?.dailyReport.id) {
      await this.reportRepository.updateReportUpdatedBy(user, item!.dailyReport.id!);
    }
    await this.repository.softDelete({ id });
    return true;
  }
}
