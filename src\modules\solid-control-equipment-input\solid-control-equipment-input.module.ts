import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IsExist } from '../../utils/validators/is-exists.validator';
import { IsNotExist } from '../../utils/validators/is-not-exists.validator';
import { SolidControlEquipmentInputService } from './solid-control-equipment-input.service';
import { JwtModule } from '@nestjs/jwt';
import { SolidControlEquipmentInputRepository } from './solid-control-equipment-input.repository';
import { SolidControlEquipmentInputController } from './solid-control-equipment-input.controller';
import { SolidControlEquipmentInput } from './entities/solid-control-equipment-input.entity';

@Module({
  imports: [TypeOrmModule.forFeature([SolidControlEquipmentInput]), JwtModule.register({})],
  controllers: [SolidControlEquipmentInputController],
  providers: [
    IsExist,
    IsNotExist,
    SolidControlEquipmentInputService,
    SolidControlEquipmentInputRepository,
  ],
  exports: [SolidControlEquipmentInputService],
})
export class SolidControlEquipmentInputModule {}
