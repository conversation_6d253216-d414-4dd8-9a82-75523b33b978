import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm';
import { EntityHelper } from '../../../utils/entity-helper';
import { DailyReport } from '../../daily-reports/entities/daily-report.entity';

@Entity()
export class Solid extends EntityHelper {
  @ManyToOne(() => DailyReport, { nullable: false })
  @JoinColumn({ name: 'dailyReportId' })
  @Column({ type: String, nullable: false })
  dailyReportId: string;

  @Column({ type: 'float', nullable: false })
  shaleCEC: number;

  @Column({ type: 'float', nullable: false })
  bentCEC: number;

  @Column({ type: 'float', nullable: false })
  highGelStrength: number;

  @Column({ type: 'float', nullable: false })
  linearGelStrength: number;
}
