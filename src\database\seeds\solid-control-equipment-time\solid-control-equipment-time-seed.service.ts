import { Injectable } from '@nestjs/common';
import { Repository } from 'typeorm';
import { SolidControlEquipment } from '../../../modules/solid-control-equipment/entities/solid-control-equipment.entity';
import { SolidControlEquipmentTime } from '../../../modules/solid-control-equipment-time/entities/solid-control-equipment-time.entity';
import { InjectRepository } from '@nestjs/typeorm';

@Injectable()
export class SolidControlEquipmentTimeSeedService {
  constructor(
    @InjectRepository(SolidControlEquipment)
    private solidControlEquipmentService: Repository<SolidControlEquipment>,
    @InjectRepository(SolidControlEquipmentTime)
    private solidControlEquipmentTimeService: Repository<SolidControlEquipmentTime>,
  ) {}

  async run(): Promise<void> {
    await this.fakeSolidControlEquipmentTimes();
  }

  private async fakeSolidControlEquipmentTimes(): Promise<void> {
    const equipments = await this.solidControlEquipmentService.find({ where: {} });
    for (let i = 0; i < equipments.length; i++) {
      await this.solidControlEquipmentTimeService.save(
        this.solidControlEquipmentTimeService.create({
          solidControlEquipment: { id: equipments[i]?.id },
          duration: 50,
        }),
      );
    }
  }
}
