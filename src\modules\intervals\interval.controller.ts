import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { PaginationResponseDto } from '../../common/dtos/pagination.response.dto';
import { AuthGuard } from '../auth/guards/auth.guard';
import { IntervalService } from './interval.service';
import { IntervalResponseDto } from './dtos/responses/interval.response.dto';
import { GetIntervalsQueryDto } from './dtos/requests/get-intervals.dto';
import { PaginationDto } from '../../common/dtos/paginationDto';
import { toDto, toPaginateDtos } from '../../common/transformers/dto.transformer';
import { CurrentUser } from '../../common/decorators/current-user.decorator';
import { User } from '../users/entities/user.entity';
import { CreateIntervalDto } from './dtos/requests/create-interval.dto';
import { RolesDecorator } from '../auth/decorators/roles.decorator';
import { UserRole } from '../roles/enums/roles.enum';
import { RolesGuard } from '../auth/guards/roles.guard';
import { UpdateIntervalDto } from './dtos/requests/update-interval.dto';
import { Responder } from '../../common/decorators/responder.decorator';

@ApiTags('Interval')
@Controller('intervals')
@ApiBearerAuth('access-token')
@RolesDecorator(UserRole.ADMIN, UserRole.COMPANY_ADMIN, UserRole.SUPERVISOR, UserRole.ENGINEER)
@UseGuards(AuthGuard, RolesGuard)
export class IntervalController {
  constructor(private readonly intervalService: IntervalService) {}

  @Get()
  @ApiOperation({ description: 'Get intervals' })
  @Responder.handle('Get intervals')
  @HttpCode(HttpStatus.OK)
  async findAll(
    @Query() query: GetIntervalsQueryDto,
    @Query() paginationQuery: PaginationDto,
  ): Promise<PaginationResponseDto<IntervalResponseDto>> {
    const data = await this.intervalService.findAll(query, paginationQuery);
    return toPaginateDtos(IntervalResponseDto, data);
  }

  @Post()
  @ApiOperation({ description: 'Create interval' })
  @Responder.handle('Create interval')
  @HttpCode(HttpStatus.CREATED)
  async create(@Body() data: CreateIntervalDto): Promise<IntervalResponseDto> {
    const value = await this.intervalService.create(data);
    return toDto(IntervalResponseDto, value);
  }

  @Put('/:id')
  @ApiOperation({ description: 'Update interval' })
  @Responder.handle('Update interval')
  @HttpCode(HttpStatus.CREATED)
  async update(@Param('id') id: string, @Body() data: UpdateIntervalDto): Promise<boolean> {
    return this.intervalService.update(id, data);
  }

  @Get('/:id')
  @ApiOperation({ description: 'Get interval detail' })
  @Responder.handle('Get interval detail')
  @HttpCode(HttpStatus.OK)
  async detail(@Param('id') id: string): Promise<IntervalResponseDto> {
    const data = await this.intervalService.findOne(id);
    return toDto(IntervalResponseDto, data);
  }

  @Delete('/:id')
  @ApiOperation({ description: 'Delete interval' })
  @Responder.handle('Delete interval')
  @HttpCode(HttpStatus.OK)
  delete(@CurrentUser() user: User, @Param('id') id: string): Promise<boolean> {
    return this.intervalService.softDeleteById(id);
  }
}
