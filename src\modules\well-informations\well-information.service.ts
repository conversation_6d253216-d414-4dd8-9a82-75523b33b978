import { Injectable } from '@nestjs/common';
import { WellInformationRepository } from './well-information.repository';
import { CreateWellInformationDto } from './dtos/requests/create-well-information.dto';
import { WellInformation } from './entities/well-information.entity';
import { DailyReport } from '../daily-reports/entities/daily-report.entity';
import { User } from '../users/entities/user.entity';
import { UpdateWellInformationDto } from './dtos/requests/update-well-information.dto';
import { DailyReportRepository } from '../daily-reports/daily-report.repository';

@Injectable()
export class WellInformationService {
  constructor(
    private repository: WellInformationRepository,
    private reportRepository: DailyReportRepository,
  ) {}

  async create(user: User, data: CreateWellInformationDto): Promise<WellInformation> {
    const wellInfo = this.repository.create(data);
    wellInfo.dailyReport = { id: data.dailyReportId } as DailyReport;
    wellInfo.engineer = { id: data.engineerId } as User;
    await this.reportRepository.updateReportUpdatedBy(user, data.dailyReportId);
    return this.repository.save(wellInfo);
  }

  async update(user: User, id: string, data: UpdateWellInformationDto): Promise<boolean> {
    await this.reportRepository.updateReportUpdatedBy(user, data.dailyReportId!);
    const engineer = { id: data.engineerId } as User;
    delete data.engineerId;
    delete data.dailyReportId;
    const updateData = { engineer, ...data };
    await this.repository.update({ id }, updateData);
    return true;
  }

  async findOne(id: string): Promise<WellInformation | null> {
    return this.repository
      .createQueryBuilder('wellInformation')
      .withDeleted()
      .where('wellInformation.deletedAt IS NULL')
      .leftJoinAndSelect(
        'wellInformation.dailyReport',
        'dailyReport',
        'dailyReport.id = wellInformation.dailyReportId',
      )
      .leftJoinAndSelect(
        'wellInformation.engineer',
        'engineer',
        'wellInformation.engineerId = engineer.id',
      )
      .where('wellInformation.id = :id', { id })
      .getOne();
  }

  async findOneToday(dailyReportId: string): Promise<WellInformation | null> {
    return this.repository
      .createQueryBuilder('wellInformation')
      .leftJoinAndSelect('wellInformation.dailyReport', 'dailyReport')
      .leftJoinAndSelect('wellInformation.engineer', 'engineer')
      .where('wellInformation.dailyReportId = :dailyReportId', { dailyReportId })
      .andWhere('wellInformation.engineerId = engineer.id')
      .orderBy('wellInformation.reportedAt', 'DESC')
      .getOne();
  }

  async softDeleteById(id: string): Promise<boolean> {
    await this.repository.softDelete({ id });
    return true;
  }
}
