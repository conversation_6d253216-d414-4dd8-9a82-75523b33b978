import { NestFactory } from '@nestjs/core';
import { SeedModule } from './seed.module';
import { UserSeedService } from './user/user-seed.service';
import { CompanySeedService } from './company/company-seed.service';
import { CustomerSeedService } from './customer/customer-seed.service';
import { CustomerContactSeedService } from './customer-contact/customer-contact-seed.service';
import { GuideSeedService } from './guide/guide-seed.service';
import { WellSeedService } from './well/well-seed.service';
import { IntervalSeedService } from './interval/interval-seed.service';
import { DailyReportSeedService } from './daily-report/daily-report-seed.service';
import { WellInformationSeedService } from './well-information/well-information-seed.service';
import { CasedHoleSeedService } from './cased-hole/cased-hole-seed.service';
import { OpenHoleSeedService } from './open-hole/open-hole-seed.service';
import { DrillStringSeedService } from './drill-string/drill-string-seed.service';
import { DrillBitSeedService } from './drill-bit/drill-bit-seed.service';
import { NozzleSeedService } from './nozzle/nozzle-seed.service';
import { SolidSeedService } from './solid/solid-seed.service';
import { SolidControlEquipmentSeedService } from './solid-control-equipment/solid-control-equipment-seed.service';
import { SampleSeedService } from './sample/sample-seed.service';
import { SolidControlEquipmentTimeSeedService } from './solid-control-equipment-time/solid-control-equipment-time-seed.service';
import { SolidControlEquipmentInputSeedService } from './solid-control-equipment-input/solid-control-equipment-input-seed.service';
import { TaskSeedService } from './task/task-seed.service';
import { ProductAndPackageInventorySeedService } from './product-and-package-inventory/product-and-package-inventory-seed.service';
import { ProductAndPackageInventoryItemSeedService } from './product-and-package-inventory-item/product-and-package-inventory-item-seed.service';
import { ProductSeedService } from './product/product-seed.service';
import { VolumeTrackingSeedService } from './volume-tracking/volume-tracking-seed.service';
import { VolumeTrackingItemSeedService } from './volume-tracking-item/volume-tracking-item-seed.service';
import { CostSeedService } from './cost/cost-seed.service';
import { NoteSeedService } from './note/note-seed.service';
import { PlanSeedService } from './plan/plan-seed.service';
import { PumpSeedService } from './pump/pump-seed.service';
import { CountryStateSeedService } from './country-state/country-state-seed.service';
import { CostSettingSeedService } from './cost-setting/cost-setting-seed.service';
import { PumpDurationSeedService } from './pump-duration/pump-duration-seed.service';
import { SolidControlEquipmentTypeSeedService } from './solid-control-equipment-type/solid-control-equipment-type-seed.service';
import { TargetPropertySeedService } from './target-property/target-property-seed.service';
import { ProductAndPackageInventoryReportSeedService } from './product-and-package-inventory-report/product-and-package-inventory-report-seed.service';
import { RoleSeedService } from './roles/role-seed.service';

const runSeed = async () => {
  const app = await NestFactory.create(SeedModule);

  // run
  await app.get(CountryStateSeedService).run();
  await app.get(CompanySeedService).run();
  await app.get(RoleSeedService).run();
  await app.get(UserSeedService).run();
  await app.get(CustomerSeedService).run();
  await app.get(CustomerContactSeedService).run();
  await app.get(GuideSeedService).run();
  await app.get(WellSeedService).run();
  await app.get(IntervalSeedService).run();
  await app.get(DailyReportSeedService).run();
  await app.get(WellInformationSeedService).run();
  await app.get(CasedHoleSeedService).run();
  await app.get(OpenHoleSeedService).run();
  await app.get(DrillStringSeedService).run();
  await app.get(DrillBitSeedService).run();
  await app.get(NozzleSeedService).run();
  await app.get(SolidSeedService).run();
  await app.get(SampleSeedService).run();
  await app.get(TargetPropertySeedService).run();
  await app.get(PlanSeedService).run();
  await app.get(PumpSeedService).run();
  await app.get(PumpDurationSeedService).run();
  await app.get(SolidControlEquipmentTypeSeedService).run();
  await app.get(SolidControlEquipmentSeedService).run();
  await app.get(SolidControlEquipmentTimeSeedService).run();
  await app.get(SolidControlEquipmentInputSeedService).run();
  await app.get(TaskSeedService).run();
  await app.get(VolumeTrackingSeedService).run();
  await app.get(VolumeTrackingItemSeedService).run();
  await app.get(ProductSeedService).run();
  await app.get(ProductAndPackageInventorySeedService).run();
  await app.get(ProductAndPackageInventoryReportSeedService).run();
  await app.get(ProductAndPackageInventoryItemSeedService).run();
  await app.get(CostSettingSeedService).run();
  await app.get(CostSeedService).run();
  await app.get(NoteSeedService).run();

  await app.close();
};

void runSeed();
