import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IsExist } from '../../utils/validators/is-exists.validator';
import { IsNotExist } from '../../utils/validators/is-not-exists.validator';
import { CasedHoleService } from './cased-hole.service';
import { CasedHole } from './entities/cased-hole.entity';
import { JwtModule } from '@nestjs/jwt';
import { CasedHoleController } from './cased-hole.controller';
import { CasedHoleRepository } from './cased-hole.repository';
import { DailyReportRepository } from '../daily-reports/daily-report.repository';

@Module({
  imports: [TypeOrmModule.forFeature([CasedHole]), JwtModule.register({})],
  controllers: [CasedHoleController],
  providers: [IsExist, IsNotExist, CasedHoleService, CasedHoleRepository, DailyReportRepository],
  exports: [CasedHoleService],
})
export class CasedHoleModule {}
