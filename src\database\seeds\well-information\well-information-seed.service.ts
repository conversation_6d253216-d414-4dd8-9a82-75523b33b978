import { Injectable } from '@nestjs/common';
import { faker } from '@faker-js/faker';
import { Repository } from 'typeorm';
import { DailyReport } from '../../../modules/daily-reports/entities/daily-report.entity';
import { WellInformation } from '../../../modules/well-informations/entities/well-information.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { User } from '../../../modules/users/entities/user.entity';
import { UserRole } from '../../../modules/roles/enums/roles.enum';

@Injectable()
export class WellInformationSeedService {
  constructor(
    @InjectRepository(DailyReport)
    private dailyReportService: Repository<DailyReport>,
    @InjectRepository(WellInformation)
    private wellInformationService: Repository<WellInformation>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {}

  async run(): Promise<void> {
    await this.fakeStates();
  }

  private async fakeStates(): Promise<void> {
    const reports = await this.dailyReportService.find({ where: {} });
    const engineer = await this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.roles', 'role')
      .where('role.value IN (:...roles)', { roles: [UserRole.ENGINEER] })
      .getOne();
    for (let i = 0; i < reports.length; i++) {
      await this.wellInformationService.save(
        this.wellInformationService.create({
          dailyReport: { id: reports[i]?.id },
          engineer: engineer,
          reportedAt: faker.date.anytime(),
          activity: faker.lorem.text(),
          azimuth: faker.number.float({ max: 200, precision: 0.01 }),
          depthDrilled: faker.number.float({ max: 200, precision: 0.01 }),
          inclination: faker.number.float({ max: 200, precision: 0.01 }),
          measuredDepth: faker.number.float({ max: 200, precision: 0.01 }),
          pullUpWeight: faker.number.float({ max: 200, precision: 0.01 }),
          rotaryWeight: faker.number.float({ max: 200, precision: 0.01 }),
          standOffWeight: faker.number.float({ max: 200, precision: 0.01 }),
          totalLength: faker.number.float({ max: 200, precision: 0.01 }),
          totalStringLength: faker.number.float({ max: 200, precision: 0.01 }),
          trueVerticalDepth: faker.number.float({ max: 200, precision: 0.01 }),
          weightOnBit: faker.number.float({ max: 200, precision: 0.01 }),
          rateOfPenetration: faker.number.float({ max: 200, precision: 0.01 }),
          revolutionsPerMinute: faker.number.float({ max: 200, precision: 0.01 }),
          drillingInterval: faker.string.alphanumeric(6),
          formation: faker.word.noun(),
        }),
      );
    }
  }
}
