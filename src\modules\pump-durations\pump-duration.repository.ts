import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { BaseRepository } from '../../common/bases/base.repository';
import { PumpDuration } from './entities/pump-duration.entity';

@Injectable()
export class PumpDurationRepository extends BaseRepository<PumpDuration> {
  constructor(dataSource: DataSource) {
    super(PumpDuration, dataSource);
  }

  async getTotalDurations(pumpId: string): Promise<number> {
    const durations = await this.createQueryBuilder('pumpDuration')
      .where('pumpDuration.pumpId = :pumpId', { pumpId })
      .getMany();
    if (!durations.length) {
      return 0;
    }
    return durations.map(e => e.durations).reduce((a, b) => a + b);
  }
}
