import { Injectable } from '@nestjs/common';
import { faker } from '@faker-js/faker';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { CostSetting } from '../../../modules/cost-settings/entities/cost-setting.entity';
import { CostSettingTypeEnum } from '../../../modules/cost-settings/enums/cost-setting-type.enum';
import { Company } from '../../../modules/companies/entities/company.entity';

@Injectable()
export class CostSettingSeedService {
  constructor(
    @InjectRepository(CostSetting)
    private costSettingRepository: Repository<CostSetting>,
    @InjectRepository(Company)
    private companyRepository: Repository<Company>,
  ) {}

  async run(): Promise<void> {
    await this.fakeCostSettings();
  }

  private async fakeCostSettings(): Promise<void> {
    const company = await this.companyRepository.findOne({ where: {} });
    for (let i = 0; i < 5; i++) {
      await this.costSettingRepository.save(
        this.costSettingRepository.create({
          companyId: company?.id,
          name: faker.commerce.productName(),
          description: faker.lorem.text(),
          cost: faker.number.float({ max: 200, precision: 0.01 }),
          type: CostSettingTypeEnum.service,
        }),
      );
    }
    for (let i = 0; i < 5; i++) {
      await this.costSettingRepository.save(
        this.costSettingRepository.create({
          companyId: company?.id,
          name: faker.person.firstName(),
          description: faker.lorem.text(),
          cost: faker.number.float({ max: 200, precision: 0.01 }),
          type: CostSettingTypeEnum.engineer,
        }),
      );
    }
  }
}
