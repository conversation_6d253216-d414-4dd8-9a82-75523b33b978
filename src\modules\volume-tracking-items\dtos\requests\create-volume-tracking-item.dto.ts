import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsNotEmpty, IsNumber, IsString, IsUUID } from 'class-validator';

export class CreateVolumeTrackingItemDto {
  @ApiProperty({ required: true })
  @IsUUID()
  @IsNotEmpty()
  volumeTrackingId: string;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  volume: number;

  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({ required: true, default: 1 })
  @IsInt()
  @IsNotEmpty()
  type: number = 1;
}
