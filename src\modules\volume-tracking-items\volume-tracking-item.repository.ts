import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { BaseRepository } from '../../common/bases/base.repository';
import { VolumeTrackingItem } from './entities/volume-tracking-item.entity';
import { VolumeTrackingItemTypeEnum } from './enums/volume-tracking-item-type.enum';

@Injectable()
export class VolumeTrackingItemRepository extends BaseRepository<VolumeTrackingItem> {
  constructor(dataSource: DataSource) {
    super(VolumeTrackingItem, dataSource);
  }

  async getTotalVolumes(
    volumeTrackingId: string,
    type: VolumeTrackingItemTypeEnum,
  ): Promise<number> {
    const volumes = await this.createQueryBuilder('volume')
      .where('volume.volumeTrackingId = :volumeTrackingId', {
        volumeTrackingId,
      })
      .andWhere('volume.type = :type', { type })
      .getMany();
    if (!volumes.length) {
      return 0;
    }
    return volumes.map(e => e.volume).reduce((a, b) => a + b);
  }
}
