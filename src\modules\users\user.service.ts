import { Injectable } from "@nestjs/common";
import { In, SelectQueryBuilder } from "typeorm";
import { PaginationDto } from "../../common/dtos/paginationDto";
import { PaginationResponseDto } from "../../common/dtos/pagination.response.dto";
import { UserRepository } from "./user.repository";
import { GetUserDto } from "./dtos/requests/get-user.dto";
import { User } from "./entities/user.entity";
import { EntityCondition } from "../../common/types/entity-condition.type";
import { NullableType } from "../../common/types/nullable.type";
import { UserRole } from "../roles/enums/roles.enum";
import { HttpForbiddenError } from "../../errors/forbidden.error";
import { UpdateUserProfileDto } from "./dtos/requests/update-user-profile.dto";
import { WellInformationRepository } from "../well-informations/well-information.repository";
import { MailService } from "src/mail/mail.service";
import { ResetUserPasswordRequestDto } from "./dtos/requests/reset-password.request.dto";
import { ConfigService } from "@nestjs/config";
import { UserChangePasswordRequestDto } from "./dtos/requests/user-change-password.request.dto";
import { UserStatus } from "./enums/statuses.enum";
import { CreateUserDto } from "./dtos/requests/create-user.dto";
import bcrypt from "bcryptjs";
import { RoleRepository } from "../roles/role.repository";
import { ChangePasswordRequestDto } from "./dtos/requests/change-password.request.dto";
import { HttpBadRequestError } from "../../errors/bad-request.error";
import { ErrorCode } from "../../errors/error-code";
import { generateOtp } from "../../utils/common/generate-otp";
import { getOtpExpiration } from "../../utils/common/get-otp-expiration";
import { AssignEngineersDto } from "./dtos/requests/assign-engineers.request.dto";
import { GetAssignedEngineerDto } from "./dtos/requests/get-assigned-engineer.dto";
import { UpdateMyProfileDto } from "./dtos/requests/update-my-profile.dto";
import { CompanyRepository } from "../companies/company.repository";
import { DeleteUsersDto } from "./dtos/requests/delete-users.dto";
import { InviteUsersDto } from "./dtos/requests/invite-users.dto";
import { Company } from "../companies/entities/company.entity";
import { RemoveAssignedEngineersDto } from "./dtos/requests/remove-assigned-engineers.request.dto";
import { MailjetService } from "../../mail/mailjet.service";

@Injectable()
export class UserService {
  constructor(
    private userRepository: UserRepository,
    private configService: ConfigService,
    private wellInformationRepository: WellInformationRepository,
    private roleRepository: RoleRepository,
    private companyRepository: CompanyRepository,
    private mailService: MailService,
    private mailjetService: MailjetService
  ) {
  }

  async findAll(
    user: User,
    query: GetUserDto,
    paginationQuery: PaginationDto
  ): Promise<PaginationResponseDto<User>> {
    const currentRoles = user.roles?.map(role => role.value) ?? [];
    // if (query.role && currentRoles.every(item => item > query.role!)) {
    //   throw new HttpForbiddenError('INVALID_PERMISSION');
    // }
    let queryBuilder: SelectQueryBuilder<User> = this.userRepository
      .createQueryBuilder("user")
      .leftJoinAndSelect("user.roles", "role")
      .leftJoinAndSelect("user.company", "company");
    if (query.role) {
      const userIds = await this.userRepository
        .createQueryBuilder('user')
        .select('user.id')
        .leftJoin('user.roles', 'role')
        .where('role.value = :roleName', { roleName: query.role })
        .getMany();
      const ids = userIds.map(user => user.id);
      queryBuilder = queryBuilder.andWhereInIds(ids);
    }
    if (query.status) {
      queryBuilder = queryBuilder.andWhere("user.status = :status", { status: query.status });
    }
    if (query.name) {
      queryBuilder = queryBuilder
        .andWhere("CONCAT(user.firstName, ' ', user.lastName) ILIKE :name", { name: `%${query.name}%` });
    }
    if (query.email) {
      queryBuilder = queryBuilder.andWhere("user.email ILIKE :email", {
        email: `%${query.email}%`
      });
    }
    if (currentRoles.every(item => item >= UserRole.SUPERVISOR)) {
      queryBuilder = queryBuilder.andWhere("user.companyId = :companyId", {
        companyId: user.companyId
      });
    } else if (query.companyId) {
      queryBuilder = queryBuilder.andWhere("user.companyId = :companyId", {
        companyId: query.companyId
      });
    }
    if (query.sortBy) {
      const sortDirection = query.sortDirection ?? "ASC";
      if (query.sortBy === "name") {
        queryBuilder = queryBuilder
          .addSelect("CONCAT(user.firstName, ' ', user.lastName)", "name")
          .orderBy("name", sortDirection === "DESC" ? "DESC" : "ASC");
      } else {
        queryBuilder = queryBuilder.orderBy(
          "user.email",
          sortDirection === "DESC" ? "DESC" : "ASC"
        );
      }
    } else {
      queryBuilder = queryBuilder.orderBy("user.createdAt", "DESC");
    }
    return this.userRepository.paginate(queryBuilder, paginationQuery);
  }

  async findOne(fields: EntityCondition<User>, relations?: string[]): Promise<NullableType<User>> {
    const query: any = { where: fields };
    if (relations) {
      query.relations = relations;
    }
    return this.userRepository.findOne({ ...query });
  }

  async findProfile(user: User) {
    return this.userRepository.findOne({ where: { id: user.id }, relations: ["roles"] });
  }

  async findProfileFullInfo(userId: string) {
    return await this.userRepository
      .createQueryBuilder("user")
      .leftJoinAndSelect("user.supervisor", "supervisor")
      .leftJoinAndSelect("user.roles", "roles")
      .leftJoinAndSelect("user.company", "company")
      .andWhere({ id: userId }).getOne();
  }

  async changePassword(user: User, body: ChangePasswordRequestDto): Promise<string> {
    const { currentPassword, newPassword } = body;
    const userInfo = await this.userRepository.findOne({ where: { email: user.email } });
    const isValidPassword = await bcrypt.compare(currentPassword, userInfo?.password ?? "");
    if (!isValidPassword) {
      throw new HttpBadRequestError(ErrorCode.INCORRECT_CURRENT_PASSWORD);
    }
    const hashedPassword = await bcrypt.hash(newPassword, 10);
    await this.userRepository.update(user.id, { password: hashedPassword });
    return "success";
  }

  async updateProfile(
    currentUser: User,
    userId: string,
    body: UpdateUserProfileDto
  ): Promise<NullableType<User>> {
    // if (currentUser.id === userId) {
    //   throw new HttpForbiddenError("INVALID_PERMISSION");
    // }
    const targetUser = await this.userRepository.findOne({ where: { id: userId } });
    if (body.companyId && body.companyId !== targetUser?.companyId) {
      await this.wellInformationRepository.softDelete({ engineer: { id: userId } });
    }
    const roles = await this.roleRepository.find({
      where: { value: In(body.userRoles ?? []) }
    });
    if (roles.length) {
      return await this.userRepository.save({ id: userId, ...body, roles });
    }
    return await this.userRepository.save({ id: userId, ...body });
  }

  async updateMyProfile(
    currentUser: User,
    body: UpdateMyProfileDto
  ): Promise<NullableType<User>> {
    // if (
    //   body.companyId &&
    //   (currentUser.roles?.map(role => role.value).includes(UserRole.SUPERVISOR) ||
    //     currentUser.roles?.map(role => role.value).includes(UserRole.ADMIN))
    // ) {
    //   await this.wellInformationRepository.softDelete({ engineer: { id: currentUser.id } });
    // }
    const currentRoles = currentUser.roles?.map(role => role.value) ?? [];
    if (body.userRoles && !currentRoles.includes(UserRole.ADMIN) && !currentRoles.includes(UserRole.COMPANY_ADMIN)) {
      throw new HttpBadRequestError(ErrorCode.NOT_PERMITTED_ROLE);
    }
    const roles = await this.roleRepository.find({
      where: { value: In(body.userRoles ?? []) }
    });
    if (roles.length) {
      return this.userRepository.save({ id: currentUser.id, ...body, roles });
    }
    return this.userRepository.save({ id: currentUser.id, ...body });
  }

  async createUser(body: CreateUserDto): Promise<User> {
    const roles = await this.roleRepository.find({ where: { value: In(body.userRoles) } });
    const password = await bcrypt.hash('12345678', 10);
    const existingUser = await this.userRepository
      .createQueryBuilder('user')
      .withDeleted()  // This will include soft-deleted users in the query
      .where('user.email = :email', { email: body.email })
      .getOne();
    if (existingUser) {
      if (existingUser.deletedAt) {
        if (existingUser.companyId !== body.companyId) {
          throw new HttpBadRequestError(ErrorCode.EXIST_USER_COMPANY_INVALID);
        }
        const userData = {
          id: existingUser.id,
          email: body.email,
          firstName: body.firstName,
          lastName: body.lastName,
          companyId: body.companyId,
          note: body.note,
          password: password,
          officePhone: body.officePhone,
          mobilePhone: body.mobilePhone,
          address: body.address,
          roles: roles,
          status: UserStatus.ACTIVE,
          wells: null,
          supervisor: null,
          subordinates: null,
        };
        existingUser.deletedAt = null;
        Object.assign(existingUser, userData);
        return this.userRepository.save(existingUser);
      } else if (existingUser.status !== UserStatus.ACTIVE) {
        if (existingUser.companyId && existingUser.companyId !== body.companyId) {
          throw new HttpBadRequestError(ErrorCode.EXIST_USER_COMPANY_INVALID);
        }
        const userData = {
          id: existingUser.id,
          email: body.email,
          firstName: body.firstName,
          lastName: body.lastName,
          companyId: body.companyId,
          note: body.note,
          password: password,
          officePhone: body.officePhone,
          mobilePhone: body.mobilePhone,
          address: body.address,
          roles: roles,
          status: UserStatus.ACTIVE,
          wells: null,
          supervisor: null,
          subordinates: null,
        };
        Object.assign(existingUser, userData);
        await this.userRepository.save(existingUser);
      } else {
        throw new HttpBadRequestError(ErrorCode.USER_EXIST);
      }
    }
    const newUser = await this.userRepository.save(
      this.userRepository.create({
        email: body.email,
        firstName: body.firstName,
        lastName: body.lastName,
        companyId: body.companyId,
        note: body.note,
        password: password,
        officePhone: body.officePhone,
        mobilePhone: body.mobilePhone,
        address: body.address,
        roles: roles,
        status: UserStatus.ACTIVE,
      }),
    );
    // await this.mailService.sendActiveConfirmation(body.email, newUser.firstName ?? '', '12345678');
    await this.mailjetService.sendActiveConfirmation(body.email, newUser.firstName ?? '', '12345678');
    return newUser;
  }

  async getAssignedEngineers(
    user: User,
    query: GetAssignedEngineerDto,
    paginationQuery: PaginationDto
  ): Promise<PaginationResponseDto<User>> {
    let queryBuilder: SelectQueryBuilder<User> = this.userRepository
      .createQueryBuilder("user")
      .where({ supervisorId: query.supervisorId });
      // .where({ companyId: user.companyId })

    if (query.name) {
      queryBuilder = queryBuilder
        .andWhere("user.firstName ILIKE :firstName", { firstName: `%${query.name}%` })
        .orWhere("user.lastName ILIKE :lastName", { lastName: `%${query.name}%` });
    }
    if (query.sortBy) {
      const sortDirection = query.sortDirection ?? "ASC";
      if (query.sortBy === 'name') {
        queryBuilder = queryBuilder.addSelect("CONCAT(user.firstName, ' ', user.lastName)", "name")
          .orderBy("name", sortDirection === "DESC" ? "DESC" : "ASC");
      } else {
        queryBuilder = queryBuilder
          .orderBy(`user.${query.sortBy}`, sortDirection === "DESC" ? "DESC" : "ASC");
      }
    } else {
      queryBuilder = queryBuilder
        .orderBy(`user.assignedDate`, 'DESC');
    }
    return this.userRepository.paginate(queryBuilder, paginationQuery);
  }

  async assignEngineers(body: AssignEngineersDto, user: User): Promise<boolean> {
    const users = await this.userRepository.find({
      where: { id: In(body.engineerIds) },
      relations: ["roles"]
    });
    if (
      !user.roles?.map(r => r.value).includes(UserRole.ADMIN) &&
      users.some(user => user.roles?.map(r => r.value).includes(UserRole.ADMIN))
    ) {
      throw new HttpForbiddenError("INVALID_PERMISSION");
    }
    for (const item of users) {
      //The engineers were assigned if they are in the same company with supervisor
      await this.userRepository.update(item.id, {
        supervisorId: body.supervisorId,
        companyId: user.companyId,
        assignedDate: new Date()
      });
    }
    return true;
  }

  async removeAssignedEngineers(body: RemoveAssignedEngineersDto, user: User): Promise<boolean> {
    const users = await this.userRepository.find({
      where: { id: In(body.engineerIds) },
      relations: ["roles"]
    });
    // if (
    //   !user.roles?.map(r => r.value).includes(UserRole.ADMIN) &&
    //   users.some(user => user.roles?.map(r => r.value).includes(UserRole.ADMIN))
    // ) {
    //   throw new HttpForbiddenError("INVALID_PERMISSION");
    // }
    for (const item of users) {
      await this.userRepository.update(item.id, { supervisorId: null });
    }
    return true;
  }

  async resetPassword(resetPassword: ResetUserPasswordRequestDto): Promise<string> {
    const user = await this.userRepository.findOne({ where: { email: resetPassword.email } });
    if (!user) {
      throw new HttpForbiddenError("INVALID_EMAIL");
    }
    const otp = generateOtp();
    const hashedOtp = await bcrypt.hash(otp, 10);
    await this.userRepository.update(user.id, {
      confirmCode: hashedOtp
    });
    const url =
      this.configService.get("app.frontendDomain", { infer: true }) +
      "/users/user-reset-password/" +
      Buffer.from(
        JSON.stringify({
          email: user.email,
          otp: otp,
          expiresIn: getOtpExpiration(5) // OTP expires in 5 minutes
        })
      ).toString("base64");
    const name = user.firstName + " " + user.lastName;
    // await this.mailService.sendResetPasswordMail(user.email, name, url);
    await this.mailjetService.sendResetPasswordMail(user.email, name, url);
    return "Success";
  }

  async userResetPassword(
    token: string,
    resetPassword: UserChangePasswordRequestDto
  ): Promise<boolean> {
    const { newPassword } = resetPassword;
    const data = Buffer.from(token, "base64").toString("utf-8");
    const { email, otp, expiresIn } = JSON.parse(data);
    const user = await this.userRepository.findOne({ where: { email: email } });
    if (!user || !user.confirmCode || !expiresIn) {
      throw new HttpForbiddenError("INVALID_TOKEN");
    }
    const now = new Date();
    if (now > expiresIn || !(await bcrypt.compare(otp, user.confirmCode))) {
      throw new HttpForbiddenError("INVALID_OR_EXPIRATION_TOKEN");
    }
    const newOtp = generateOtp();
    const hashedOtp = await bcrypt.hash(newOtp, 10);
    const hashedPassword = await bcrypt.hash(newPassword, 10);
    await this.userRepository.update(user.id, { password: hashedPassword, confirmCode: hashedOtp });
    return true;
  }

  async uploadAvatar(userId: string, filename: string) {
    await this.userRepository.update(userId, {
      avatar: this.configService.get("app.backendDomain", { infer: true }) + "/avatars/" + filename
    });
    return {
      id: userId,
      uploadedAt: new Date(),
      url: this.configService.get("app.backendDomain", { infer: true }) + "/avatars/" + filename
    };
  }

  async getInvitedUsers(): Promise<User[]> {
    return this.userRepository.find({where: {status: UserStatus.INVITED}});
  }

  async inviteUsers(currentUser: User, body: InviteUsersDto): Promise<Boolean> {
    const roles = await this.roleRepository.find({ where: { value: In(body.userRoles ?? []) } });
    const company = await this.companyRepository.findOne({ where: { id: body.companyId } });
    const existUsers = await this.userRepository.createQueryBuilder('user')
      .withDeleted()  // This will include soft-deleted users in the query
      .where('user.email IN (:...email)', { email: body.emails })
      .getMany();
    if (!company) {
      throw new HttpBadRequestError(ErrorCode.COMPANY_NOT_FOUND);
    }
    if (existUsers.length) {
      for (const existingUser of existUsers) {
        if (existingUser.deletedAt) {
          if (existingUser.companyId && existingUser.companyId !== body.companyId) {
            throw new HttpBadRequestError(ErrorCode.EXIST_USER_COMPANY_INVALID);
          }
          const userData = {
            id: existingUser.id,
            companyId: company?.id,
            note: null,
            password: await bcrypt.hash("12345678", 10),
            firstName: '',
            lastName: '',
            status: UserStatus.INVITED,
            roles: roles,
            wells: null,
            supervisor: null,
            subordinates: null,
          };
          existingUser.deletedAt = null;
          Object.assign(existingUser, userData);
          await this.userRepository.save(existingUser);
          await this.sendInviteEmail(existingUser, company);
          const index = body.emails.indexOf(existingUser.email, 0);
          if (index > -1) {
            body.emails.splice(index, 1);
          }
        } else if (existingUser.status !== UserStatus.ACTIVE) {
          if (existingUser.companyId && existingUser.companyId !== body.companyId) {
            throw new HttpBadRequestError(ErrorCode.EXIST_USER_COMPANY_INVALID);
          }
          const userData = {
            id: existingUser.id,
            companyId: company?.id,
            note: null,
            password: await bcrypt.hash("12345678", 10),
            firstName: '',
            lastName: '',
            status: UserStatus.INVITED,
            roles: roles,
            wells: null,
            supervisor: null,
            subordinates: null,
          };
          Object.assign(existingUser, userData);
          await this.userRepository.save(existingUser);
          await this.sendInviteEmail(existingUser, company);
          const index = body.emails.indexOf(existingUser.email, 0);
          if (index > -1) {
            body.emails.splice(index, 1);
          }
        } else {
          throw new HttpBadRequestError('The email ' + existingUser.email + ' is exist.');
        }
      }
    }
    for (const email of body.emails) {
      const newUser = await this.userRepository.create({
        email: email,
        password: await bcrypt.hash("12345678", 10),
        firstName: '',
        lastName: '',
        status: UserStatus.INVITED,
        companyId: company?.id,
        roles: roles
      });
      await this.userRepository.save(newUser);
      await this.sendInviteEmail(newUser, company);
    }

    return true;
  }

  async sendInviteEmail(user: User, company: Company) {
    const otp = generateOtp();
    const hashedOtp = await bcrypt.hash(otp, 10);
    await this.userRepository.update(user.id, {
      confirmCode: hashedOtp
    });
    const token = Buffer.from(
      JSON.stringify({
        email: user.email,
        otp: otp,
        expiresIn: getOtpExpiration(2880) // OTP expires in 2 day
      })
    ).toString("base64");
    const url =
      this.configService.get("app.frontendDomain", { infer: true }) +
      "/accept-invite/" + token;
    const name = user.email.split("@")[0];
    // await this.mailService.sendInviteMail(user.email, company.name, name, url);
    await this.mailjetService.sendInviteMail(user.email, company.name, name, url);
  }

  async acceptInvitation(
    token: string
  ): Promise<string> {
    const data = Buffer.from(token, "base64").toString("utf-8");
    const { email, otp, expiresIn } = JSON.parse(data);
    const user = await this.userRepository.findOne({ where: { email: email } });
    if (user && user.status === UserStatus.ACTIVE) {
      throw new HttpForbiddenError("INVITE_LINK_IS_NOT_VALID");
    }
    if (!user || !user.confirmCode || !expiresIn) {
      throw new HttpForbiddenError("INVALID_TOKEN");
    }
    const now = new Date();
    const expiresInDate = new Date(expiresIn);
    if (now.getTime() > expiresInDate.getTime() || !(await bcrypt.compare(otp, user.confirmCode))) {
      throw new HttpForbiddenError("INVALID_OR_EXPIRATION_TOKEN");
    }
    await this.userRepository.update(user.id, {status: UserStatus.ACTIVE});
    return email;
  }

  async deleteUsers(body: DeleteUsersDto) {
    for (const id of body.ids) {
      await this.userRepository.softDelete({ id });
    }
    return true;
  }
}
